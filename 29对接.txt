
一键对接步骤：

1.商品管理——分类设置——添加——填写yyy，其他随意   *必须填写yyy，否则后续会出错。

2.xdjk.php中wkname添加  "yyy" => "yyy"

3.网站管理——对接设置——添加——名称写yyy，平台选择yyy,然后填写域名和账密

4.

在api文件夹中新建php文件，粘贴下面的代码。文件名随意。计划任务设置定时访问即可。
例如文件名为yyy，则地址是  你的域名/api/yyy?pricee=3
pricee是价格倍数，别写错。


<?php

// 引入公共配置文件
include('../confing/common.php');

// 获取并处理 GET 参数
$pricee = trim(strip_tags(daddslashes($_GET['pricee'])));

// 查询 qingka_wangke_huoyuan 表获取相关信息
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE  instr(pt,'yyy') or instr(name,'yyy')");
$hid = $a["hid"];

// 查询 qingka_wangke_fenlei 表获取分类信息
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'yyy') ORDER BY id DESC LIMIT 0, 1");
$category = $b["id"];

// 准备请求数据
$data = array(
    "uid" => $a["user"],
    "key" => $a["pass"]
);

// 构建请求 URL
$er_url = "{$a["url"]}/api/site";

// 发送请求并获取结果
$result = get_url($er_url, $data);
$result = json_decode($result, true);

// 检查对接结果
if ($result["code"] != "200") {
    jsonReturn(1, $result["message"]);
}

// 查询 qingka_wangke_class 表的最大 sort 值
$max_sort_query = "SELECT MAX(sort) as max_sort FROM qingka_wangke_class";
$max_sort_result = mysqli_query($conn, $max_sort_query);
$max_sort_row = mysqli_fetch_assoc($max_sort_result);
$current_sort = $max_sort_row['max_sort'] ?? 0;

// 初始化插入和更新记录的计数器
$inserted_count = 0;
$updated_count = 0;

// 初始化输出数组
$output = [];

// 遍历结果数据列表
foreach ($result["data"]["list"] as $data_item) {
    $id = $data_item['id'];
    $ids[] = $id;
    $name = $DB->escape($data_item['name']);
    $price = $data_item['price'] * $pricee;
    $content = $DB->escape($data_item['trans']);

    // 查询 qingka_wangke_class 表是否存在对应记录
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '$hid' AND noun = '$id' LIMIT 1");

    if (!$rs) {
        // 若记录不存在，执行插入操作
        $sql = "INSERT INTO qingka_wangke_class 
                (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, addtime, status, fenlei) 
                VALUES 
                ('$current_sort', '$name', '$id', '$id', '$price', '$hid', '$hid', '*', '$content', '$now_time', '1', '$category')";

        $is = $DB->query($sql);
        if ($is) {
            $inserted_count++;
            $current_sort++;
        }
    } else {
        // 若记录存在，执行更新操作
        $sql = "UPDATE qingka_wangke_class 
                SET name = '$name', 
                    price = '$price', 
                    content = '$content', 
                WHERE docking = '$hid' AND noun = '$id'";

        $is = $DB->query($sql);
        if ($is) {
            $updated_count++;
        }
    }
}

//下架已下架的项目
if (!empty($ids)) {
    $ids_str = implode(',', array_map(function($id) use ($DB) {
        return "'" . $DB->escape($id) . "'";
    }, $ids));
    $sql = "UPDATE qingka_wangke_class SET state = 0 WHERE docking = '$hid' AND noun NOT IN ($ids_str)";
    $DB->query($sql);
}

// 返回操作结果
echo ("插入操作完成。成功上架 {$inserted_count} 条记录，更新 {$updated_count} 条记录。\n\n");
————————————————————————————————————————————————
查课
————————————————————————————————————————————————
	//YYY查课接口
	if ($type == "yyy") {

		$maxAttempts = 20; // 最大尝试次数
		$intervalTime = 2; // 尝试间隔时间（秒）
		$attempt = 0;
		$json_data = [];

		$data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "search" => 1);
		//die(json_encode($data));
		$dx_rl = $a["url"];
		$dx_url = "$dx_rl/api/order";

		while ($attempt < $maxAttempts) {

			$result = get_url($dx_url, $data);
			$result = json_decode($result, true);

			if (isset($result['data']) && is_array($result['data']) ) {

				if (count($result['data']) > 0){
					foreach ($result['data'] as $row) {
						$json_data[] = ['name' => $row];
					}
					break;
				}elseif ($result['message']!=='查询成功'){
					return ['code' => -1, 'msg' => $result['message'], 'data' => []];
				}
			}

			$attempt++;
			if ($attempt < $maxAttempts) {
				sleep($intervalTime);
			}
		}

		if ($attempt == $maxAttempts && empty($json_data)) {
			return ['code' => -1, 'msg' => '查询中，请稍后尝试', 'data' => []];
		}

		$b  = ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
		return $b;
	}
————————————————————————————————————————————————
下单
————————————————————————————————————————————————
	//YYY下单接口
	if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
		$dx_rl = $a["url"];
		$dx_url = "$dx_rl/api/order";

		$result = get_url($dx_url, $data);
		$result = json_decode($result, true);

        if ($result["code"] == "200") {
			$b = array("code" => 1, "msg" => "下单成功" ,"yid"=>$result["data"]["yid"] );
        } else {
			$b = array("code" => -1, "msg" => $result["msg"]);
        }
		return $b;
	}
————————————————————————————————————————————————
进度
————————————————————————————————————————————————
	//YYY进度接口
    if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $pt, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "yid" => $d['yid']);
		$dx_rl = $a["url"];
        $dx_url = "$dx_rl/api/getorder";

		$result = get_url($dx_url,$data);
		$result = json_decode($result, true);

		if ($result["code"] == "200") {
			foreach ($result["data"]["list"] as $res) {
				$yid = $res["id"];
				$remarks = $res["status"];
				$process = 0;
				if (!isset($res["train"])){
					$kcname = $res["train"];
				}
				$code=$res["code"];
				if ($code==107){
					$status = "队列中";
				}elseif ($code==103){
					$status = "异常";
				}elseif ($code==102){
					$status = "已完成";
					$process = 100;
				}elseif ($code==101){
					$status = "已退款";
				}else{
					$status = "进行中";
					preg_match('/^\d+(\.\d+)?/', $remarks, $matches);
					if (isset($matches[0])) {
						$process = $matches[0];
					} else {
						$process = 50;
					}
				}
				$process="$process%";

				$b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "status_text" => $status, "process" => $process, "remarks" => $remarks);
			}
		} else {
			$b[] = array("code" => -1, "msg" => $result["message"]);
		}
		return $b;
	}
————————————————————————————————————————————————
补刷
————————————————————————————————————————————————
    //YYY补刷接口
    if ($type == "yyy") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "ids" => $yid, "dotype" => "reset");
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api/setOrder";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        $b = array("code" => 1, "msg" => "补刷成功");
        return $b;
    }
————————————————————————————————————————————————
修改密码
————————————————————————————————————————————————
    //YYY修改密码接口     newpass可以按需换成自己网站中的变量名
    if ($type == "yyy") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "ids" => $yid, "dotype" => "edit","odpwd" => $pass);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api/setOrder";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        $b = array("code" => 1, "msg" => "修改成功");
        return $b;
    }