<?php
// Error reporting and constants
error_reporting(0);
define('IN_CRONLITE', true);
define('ROOT', dirname(__FILE__).'/');
define('SYS_KEY', "$verification");
$password_hash = '!@#%!s?';

// Date and time setup
date_default_timezone_set("PRC");
$date = date("Y-m-d H:i:s");
$jtdate = date("Y-m-d");
$tomorrow = date("Y-m-d", strtotime("+1 day"));
$ztdate = date("Y-m-d", strtotime("-1 day"));
$qtdate = date("Y-m-d", strtotime("-2 day"));
$wqtdate = date("Y-m-d", strtotime("-3 day"));
$wwqtdate = date("Y-m-d", strtotime("-4 day"));
$wwwqtdate = date("Y-m-d", strtotime("-5 day"));
$wwwwqtdate = date("Y-m-d", strtotime("-6 day"));
// Session and site configuration
session_start();
$scriptpath = str_replace('\\', '/', $_SERVER['SCRIPT_NAME']);
$sitepath = substr($scriptpath, 0, strrpos($scriptpath, '/'));
$siteurl = ($_SERVER['SERVER_PORT'] == '443' ? 'https://' : 'http://').$_SERVER['HTTP_HOST'].$sitepath.'/';
if (!isset($port)) $port = '3306';

// Include configuration files
require_once ROOT.'config.php';
if (is_file(ROOT.'360safe/360webscan.php')) {
    require_once ROOT.'360safe/360webscan.php';
}
include_once ROOT.'epay/epay.config.php';
include_once ROOT.'../Checkorder/configuration.php';

// Database connection
$DB = new DB($host, $user, $pwd, $dbname, $port);

// Load configuration from database
$sql1 = $DB->prepare_query("SELECT * FROM `qingka_wangke_config`", []);
$result = $sql1->get_result();
$conf = [];
while ($row = $DB->fetch($result)) {
    $conf[$row['v']] = $row['k'];
}

// Alipay configuration
$alipay_config['sign_type'] = strtoupper('MD5');
$alipay_config['input_charset'] = strtolower('utf-8');
$alipay_config['transport'] = 'http';
$alipay_config['apiurl'] = $conf['epay_api'];
$alipay_config['partner'] = $conf['epay_pid'];
$alipay_config['key'] = $conf['epay_key'];

if (!defined('IN_CRONLITE')) exit();
$clientip = real_ip();
$islogin = 0;
if (isset($_COOKIE["admin_token"])) {
    $token = authcode($_COOKIE['admin_token'], 'DECODE', SYS_KEY);
    list($user, $sid) = explode("\t", $token);
    $udata = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE user = ? LIMIT 1", [$user]);
    if ($udata) {
        $session = md5($udata['user'] . $udata['pass'] . $password_hash);
        if ($session == $sid) {
            $sql = "SELECT ip FROM qingka_wangke_log WHERE uid = ? AND type = '登录' ORDER BY addtime DESC LIMIT 1";
            $params = [$udata['uid']];
            $last_login_ip = $DB->prepare_getrow($sql, $params)['ip'] ?? null;
            if ($last_login_ip && $last_login_ip !== $clientip) {
                setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
            } else {
                $DB->prepare_query("UPDATE qingka_wangke_user SET endtime = ?, ip = ? WHERE user = ?", [$date, $clientip, $user]);
                $islogin = 1;
                $userrow = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE user = ? LIMIT 1", [$user]);
            }
        }
    }
}
?>