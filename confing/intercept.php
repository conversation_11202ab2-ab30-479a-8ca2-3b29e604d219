<?php
if (!isset($_COOKIE["admin_token"]) || !isset($islogin) || $islogin != 1) {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        exit(json_encode(['code' => -1, 'msg' => '登录过期请重新登录', 'redirect' => '/index/login']));
    } else {
        @header('Content-Type: text/html; charset=UTF-8');
        echo '<script>
                if (self !== top) {
                    top.location.href = "/index/login";
                } else {
                    window.location.href = "/index/login";
                }
              </script>';
        exit;
    }
}。
?>