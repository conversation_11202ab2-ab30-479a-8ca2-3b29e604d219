<?php
/**
 * 通用商品名称清理工具
 * 用于清理各种货源的商品名称前缀，提供更好的用户体验
 */

if (!defined('IN_CRONLITE')) {
    define('IN_CRONLITE', true);
}

class NameCleaner {
    
    /**
     * 通用前缀清理规则
     * 按优先级排序，越靠前的规则优先级越高
     */
    private static $common_prefixes = array(
        // 教育类前缀
        'YYY继教-',
        'yyy教育-',
        'YYY-',
        'yyy-',
        '学习公社云-',
        '课件全学-',
        'YYY继教学习公社云-',
        'YYY继教-学习公社云-',
        'YYY继教-学习公社云-课件全学-',
        'YYY继教学习公社云课件全学-',
        
        // 常见的商业前缀
        '网课代刷-',
        '代刷平台-',
        '在线教育-',
        '网络课程-',
        '智慧教育-',
        '云课堂-',
        '学习平台-',
        '教育平台-',
        
        // 技术类前缀
        'API-',
        'SDK-',
        '接口-',
        '系统-',
        '平台-',
        
        // 地区类前缀（常见格式）
        '北京-',
        '上海-',
        '广州-',
        '深圳-',
        '杭州-',
        '成都-',
        '武汉-',
        '西安-',
        '南京-',
        '重庆-',
        
        // 机构类前缀
        '大学-',
        '学院-',
        '培训-',
        '教育局-',
        '教委-',
        
        // 数字编号前缀
        '001-',
        '002-',
        '003-',
        '004-',
        '005-',
        '01-',
        '02-',
        '03-',
        '04-',
        '05-',
        
        // 特殊符号前缀
        '【官方】',
        '【正版】',
        '【推荐】',
        '【热门】',
        '【新】',
        '[官方]',
        '[正版]',
        '[推荐]',
        '[热门]',
        '[新]',
        '★',
        '☆',
        '●',
        '○',
        '■',
        '□',
        '▲',
        '△'
    );
    
    /**
     * 货源特定的前缀规则
     * 键为货源标识，值为该货源特有的前缀数组
     */
    private static $source_specific_prefixes = array(
        'yyy' => array(
            'YYY继教-',
            'yyy教育-',
            '学习公社云-',
            '课件全学-'
        ),
        // 可以为其他货源添加特定规则
        // 'other_source' => array('其他前缀-'),
    );
    
    /**
     * 清理商品名称
     * @param string $name 原始名称
     * @param string $source_type 货源类型（可选）
     * @return string 清理后的名称
     */
    public static function cleanName($name, $source_type = '') {
        if (empty($name)) {
            return $name;
        }
        
        $cleaned_name = trim($name);
        $original_name = $cleaned_name;
        
        // 1. 先应用货源特定的清理规则
        if (!empty($source_type) && isset(self::$source_specific_prefixes[$source_type])) {
            $specific_prefixes = self::$source_specific_prefixes[$source_type];
            foreach ($specific_prefixes as $prefix) {
                if (strpos($cleaned_name, $prefix) === 0) {
                    $cleaned_name = substr($cleaned_name, strlen($prefix));
                    break; // 只移除第一个匹配的前缀
                }
            }
        }
        
        // 2. 再应用通用清理规则
        foreach (self::$common_prefixes as $prefix) {
            if (strpos($cleaned_name, $prefix) === 0) {
                $cleaned_name = substr($cleaned_name, strlen($prefix));
                break; // 只移除第一个匹配的前缀
            }
        }
        
        // 3. 清理多余的空格和特殊字符
        $cleaned_name = trim($cleaned_name);
        $cleaned_name = preg_replace('/\s+/', ' ', $cleaned_name); // 多个空格合并为一个
        
        // 4. 如果清理后名称为空，返回原名称
        if (empty($cleaned_name)) {
            return $original_name;
        }
        
        return $cleaned_name;
    }
    
    /**
     * 批量清理名称数组
     * @param array $names 名称数组
     * @param string $source_type 货源类型
     * @return array 清理后的名称数组
     */
    public static function cleanNames($names, $source_type = '') {
        $cleaned_names = array();
        foreach ($names as $key => $name) {
            $cleaned_names[$key] = self::cleanName($name, $source_type);
        }
        return $cleaned_names;
    }
    
    /**
     * 添加新的通用前缀规则
     * @param string|array $prefixes 要添加的前缀
     */
    public static function addCommonPrefix($prefixes) {
        if (is_string($prefixes)) {
            $prefixes = array($prefixes);
        }
        
        foreach ($prefixes as $prefix) {
            if (!in_array($prefix, self::$common_prefixes)) {
                array_unshift(self::$common_prefixes, $prefix); // 添加到开头，优先级最高
            }
        }
    }
    
    /**
     * 为特定货源添加前缀规则
     * @param string $source_type 货源类型
     * @param string|array $prefixes 要添加的前缀
     */
    public static function addSourcePrefix($source_type, $prefixes) {
        if (is_string($prefixes)) {
            $prefixes = array($prefixes);
        }
        
        if (!isset(self::$source_specific_prefixes[$source_type])) {
            self::$source_specific_prefixes[$source_type] = array();
        }
        
        foreach ($prefixes as $prefix) {
            if (!in_array($prefix, self::$source_specific_prefixes[$source_type])) {
                array_unshift(self::$source_specific_prefixes[$source_type], $prefix);
            }
        }
    }
    
    /**
     * 获取所有前缀规则（用于调试）
     * @return array
     */
    public static function getAllPrefixes() {
        return array(
            'common' => self::$common_prefixes,
            'source_specific' => self::$source_specific_prefixes
        );
    }
    
    /**
     * 检测名称是否包含前缀
     * @param string $name 名称
     * @param string $source_type 货源类型
     * @return array 包含检测结果的数组
     */
    public static function detectPrefix($name, $source_type = '') {
        $result = array(
            'has_prefix' => false,
            'detected_prefix' => '',
            'cleaned_name' => $name,
            'prefix_type' => ''
        );
        
        // 检测货源特定前缀
        if (!empty($source_type) && isset(self::$source_specific_prefixes[$source_type])) {
            foreach (self::$source_specific_prefixes[$source_type] as $prefix) {
                if (strpos($name, $prefix) === 0) {
                    $result['has_prefix'] = true;
                    $result['detected_prefix'] = $prefix;
                    $result['cleaned_name'] = substr($name, strlen($prefix));
                    $result['prefix_type'] = 'source_specific';
                    return $result;
                }
            }
        }
        
        // 检测通用前缀
        foreach (self::$common_prefixes as $prefix) {
            if (strpos($name, $prefix) === 0) {
                $result['has_prefix'] = true;
                $result['detected_prefix'] = $prefix;
                $result['cleaned_name'] = substr($name, strlen($prefix));
                $result['prefix_type'] = 'common';
                return $result;
            }
        }
        
        return $result;
    }
}

// 提供全局函数，方便在其他地方调用
if (!function_exists('clean_product_name')) {
    /**
     * 全局函数：清理商品名称
     * @param string $name 商品名称
     * @param string $source_type 货源类型
     * @return string 清理后的名称
     */
    function clean_product_name($name, $source_type = '') {
        return NameCleaner::cleanName($name, $source_type);
    }
}

?>
