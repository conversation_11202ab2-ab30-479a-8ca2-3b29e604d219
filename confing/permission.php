<?php
$act = isset($_GET["act"]) ? daddslashes($_GET["act"]) : null;
@header("Content-Type: application/json; charset=UTF-8");
switch ($act) {
    case "login":
        if ($islogin == 1) {
            exit("{\"code\":-1,\"msg\":\"已登录，请先退出后再登录\"}");
        }
        $user = trim(strip_tags($_POST["user"]));
        $pass = trim(strip_tags($_POST["pass"]));
        $pass2 = trim(strip_tags($_POST["pass2"]));
        $clientip = real_ip();
        $sql = "SELECT COUNT(*) FROM qingka_wangke_log WHERE type='登录失败' AND addtime > ? AND ip=?";
        $params = [date('Y-m-d'), $clientip];
        $error_count = $DB->prepare_count($sql, $params);
        if ($error_count >= 10) {
            exit("{\"code\":-1,\"msg\":\"登录失败次数过多，请联系管理员\"}");
        }
        if ($user == "" || $pass == "") {
            jsonReturn(-1, "账号密码不能为空");
        }
        $sql = "SELECT uid, user, pass FROM qingka_wangke_user WHERE user=? LIMIT 1";
        $params = [$user];
        $row = $DB->prepare_getrow($sql, $params);
        if (!$row || empty($row["user"])) {
            exit("{\"code\":-1,\"msg\":\"用户名密码不正确\"}");
        } else {
            if ($pass != $row["pass"]) {
                wlog($row["uid"], "登录失败", "密码错误", 0);
                exit("{\"code\":-1,\"msg\":\"用户名密码不正确\"}");
            } else {
                if ($row["user"] == $user && $row["pass"] == $pass) {
                    if ($row["uid"] == 1) {
                        if ($pass2 == "") {
                            exit("{\"code\":5,\"msg\":\"二次验证失败\"}");
                        } elseif ($pass2 == $verification) {
                            $session = md5($user . $pass . $password_hash);
                            $token = authcode($user . "\t" . $session, "ENCODE", SYS_KEY);
                            $secure = (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
                            setcookie("admin_token", $token, time() + 90000, "/", "", $secure, true);
                            wlog($row["uid"], "登录", "登录成功" . $conf["sitename"], "0");
                            tuisong($row["uid"], "login", "新登录提醒", "登录成功");
                            exit("{\"code\":1,\"msg\":\"登录成功\"}");
                        } else {
                            exit("{\"code\":-1,\"msg\":\"验证失败\"}");
                        }
                    } else {
                        $session = md5($user . $pass . $password_hash);
                        $token = authcode($user . "\t" . $session, "ENCODE", SYS_KEY);
                        $secure = (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
                        setcookie("admin_token", $token, time() + 90000, "/", "", $secure, true);
                        wlog($row["uid"], "登录", "登录成功" . $conf["sitename"], "0");
                        tuisong($row["uid"], "login", "新登录提醒", "登录成功");
                        exit("{\"code\":1,\"msg\":\"登录成功\"}");
                    }
                }
            }
        }
        break;

    case "register":
        if ($conf["user_yqzc"] == "0") {
            jsonReturn(-1, "邀请码注册已关闭，具体开放时间等通知");
        }
        $name = trim(strip_tags($_POST["name"]));
        $user = trim(strip_tags($_POST["user"]));
        $pass = trim(strip_tags($_POST["pass"]));
        $yqm = trim(strip_tags($_POST["yqm"]));
        if ($user == "" || $pass == "" || $name == "" || $yqm == "") {
            exit("{\"code\":-1,\"msg\":\"所有项目不能为空\"}");
        }
        if (!preg_match("/[1-9]([0-9]{4,10})/", $user)) {
            exit("{\"code\":-1,\"msg\":\"账号必须为QQ号\"}");
        }
        if (!is_numeric($user)) {
            exit("{\"code\":-1,\"msg\":\"请正确输入账号\"}");
        }
        if ($DB->get_row("select uid from qingka_wangke_user where user='" . $user . "' ")) {
            exit("{\"code\":-1,\"msg\":\"该账号已存在\"}");
        }
        if ($DB->get_row("select uid from qingka_wangke_user where name='" . $name . "' ")) {
            exit("{\"code\":-1,\"msg\":\"该昵称已存在\"}");
        }
        if (strlen($pass) < 6) {
            exit("{\"code\":-1,\"msg\":\"密码最少为6位数\"}");
        }
        $a = $DB->get_row("select uid,yqm,yqprice from qingka_wangke_user where yqm='" . $yqm . "' ");
        if (!$a) {
            exit("{\"code\":-1,\"msg\":\"邀请码不存在\"}");
        }
        if ($a["yqprice"] == "") {
            exit("{\"code\":-1,\"msg\":\"当前邀请码未设置邀请费率\"}");
        }
        $clientip = real_ip();
        $ip = $DB->count("select ip from qingka_wangke_log where type='邀请码注册商户' and addtime>'" . $jtdate . "' and ip='" . $clientip . "' ");
        if ($ip > 1) {
            exit("{\"code\":-1,\"msg\":\"同一个IP同一天最多只能注册1次\"}");
        }
        if ($DB->query("insert into qingka_wangke_user (uuid,name,user,pass,addprice,addtime) values ('" . $a["uid"] . "','" . $name . "','" . $user . "','" . $pass . "','" . $a["yqprice"] . "','" . $date . "')")) {
            wlog($a["uid"], "邀请码注册商户", "成功邀请昵称为[" . $name . "],账号为[" . $user . "]的靓仔注册成功！还望再接再厉！", "0");
            exit("{\"code\":1,\"msg\":\"注册成功！\"}");
        } else {
            exit("{\"code\":-1,\"msg\":\"未知异常\"}");
        }
        break;

    case "logout":
        $secure = (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
        setcookie("admin_token", 123456, time() -90000, "/", "", $secure, true);
        @header("Content-Type: text/html; charset=UTF-8");
        exit("<script language='javascript'>window.location.href='index';</script>");
        break;
}
if ($islogin != 1) {
    exit(json_encode(['code' => -1, 'msg' => '登录过期请重新登录', 'redirect' => '/index/login']));
}
?>