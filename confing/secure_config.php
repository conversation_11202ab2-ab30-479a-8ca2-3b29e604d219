<?php
/**
 * 安全配置文件
 * 请将此文件移动到Web根目录之外
 */

// 防止直接访问
if (!defined('IN_CRONLITE')) {
    die('Access denied');
}

// 数据库配置 - 请修改为您的实际配置
$secure_config = [
    'db' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'username' => 'your_db_user',  // 请修改
        'password' => 'your_secure_password',  // 请修改为强密码
        'database' => 'your_db_name',  // 请修改
        'charset' => 'utf8mb4'
    ],
    
    // 安全配置
    'security' => [
        'verification_code' => 'your_secure_verification_code',  // 请修改
        'encryption_key' => 'your_32_character_encryption_key',  // 请修改为32位随机字符串
        'password_salt' => 'your_password_salt',  // 请修改
        'session_timeout' => 3600,  // 1小时
        'max_login_attempts' => 5,
        'login_lockout_time' => 900  // 15分钟
    ],
    
    // Redis配置
    'redis' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => '',  // 建议设置Redis密码
        'database' => 11
    ],
    
    // API安全配置
    'api' => [
        'rate_limit' => [
            'requests_per_minute' => 60,
            'requests_per_hour' => 1000
        ],
        'allowed_ips' => [],  // 如需要，可设置IP白名单
        'require_https' => true  // 生产环境建议启用HTTPS
    ]
];

// 验证配置
function validate_config($config) {
    $errors = [];
    
    // 检查数据库配置
    if (empty($config['db']['password']) || $config['db']['password'] === 'your_secure_password') {
        $errors[] = '请设置安全的数据库密码';
    }
    
    // 检查加密密钥
    if (strlen($config['security']['encryption_key']) < 32) {
        $errors[] = '加密密钥长度必须至少32位';
    }
    
    // 检查验证码
    if ($config['security']['verification_code'] === 'your_secure_verification_code') {
        $errors[] = '请修改默认的验证码';
    }
    
    if (!empty($errors)) {
        die('配置错误: ' . implode(', ', $errors));
    }
}

validate_config($secure_config);

// 导出配置
$host = $secure_config['db']['host'];
$port = $secure_config['db']['port'];
$user = $secure_config['db']['username'];
$dbname = $secure_config['db']['database'];
$pwd = $secure_config['db']['password'];
$verification = $secure_config['security']['verification_code'];

?>
