<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端订单列表测试</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        /* 模拟订单卡片 - 现代化美观设计 */
        .order-card {
            background: #fff;
            border: none;
            border-radius: 16px;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        .order-card:hover {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .order-card:active {
            transform: scale(0.98);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 16px 18px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .platform-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
            line-height: 1.4;
        }
        
        .miao-tag {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .card-content {
            padding: 18px;
            background: #fff;
        }

        .info-row {
            display: flex;
            margin-bottom: 16px;
            align-items: flex-start;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            min-width: 80px;
            flex-shrink: 0;
            margin-right: 12px;
            line-height: 1.5;
        }

        .info-value {
            flex: 1;
            font-size: 14px;
            color: #343a40;
            line-height: 1.5;
        }
        
        .account-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 6px 8px;
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .account-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            gap: 6px;
            padding: 2px 0;
        }

        .copy-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 3px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            cursor: pointer;
            flex-shrink: 0;
            box-shadow: 0 1px 4px rgba(0, 123, 255, 0.2);
            transition: all 0.2s ease;
            -webkit-tap-highlight-color: transparent;
            min-width: 35px;
        }

        .copy-btn:active {
            transform: scale(0.95);
        }

        .copy-all-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            box-shadow: 0 1px 6px rgba(40, 167, 69, 0.2);
            transition: all 0.2s ease;
            -webkit-tap-highlight-color: transparent;
        }

        .copy-all-btn:active {
            transform: scale(0.98);
        }
        
        .status-btn {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            display: inline-block;
            color: white;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; color: #212529; }
        .status-danger { background: #dc3545; }
        .status-primary { background: #007bff; }
        
        .progress-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .progress-text {
            font-weight: 600;
            color: #007bff;
            min-width: 40px;
            flex-shrink: 0;
        }
        
        .progress-bar-container {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.3s ease;
        }
        
        /* 响应式优化 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .test-section {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .test-title {
                font-size: 16px;
            }
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }
            
            .test-section {
                padding: 10px;
            }
            
            .card-header {
                padding: 10px;
            }
            
            .card-content {
                padding: 10px;
            }
            
            .info-label {
                min-width: 60px;
                font-size: 12px;
            }
            
            .info-value {
                font-size: 12px;
            }
        }
        
        .demo-note {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 13px;
            color: #1565c0;
        }

        /* 搜索切换按钮样式 */
        .mobile-search-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #409eff, #3a8ee6);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
        }

        .mobile-search-toggle:active {
            transform: scale(0.98);
        }

        /* 批量操作切换按钮样式 */
        .mobile-batch-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #67c23a, #5daf34);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
            transition: all 0.3s ease;
        }

        .mobile-batch-toggle:active {
            transform: scale(0.98);
        }

        .search-demo,
        .batch-demo {
            overflow: hidden;
            transition: all 0.4s ease;
        }

        /* 下拉菜单演示样式 */
        .dropdown-demo {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: linear-gradient(135deg, #409eff, #3a8ee6);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            min-width: 80px;
            box-shadow: 0 3px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.2s ease;
        }

        .dropdown-btn:active {
            transform: scale(0.98);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            border: none;
            padding: 8px 0;
            min-width: 140px;
            z-index: 1000;
            display: none;
            margin-top: 4px;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            color: #409eff;
        }

        /* 订单详情样式 */
        .modern-order-detail {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        .detail-container {
            padding: 0;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .header-left {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
        }

        .order-title {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            line-height: 1.3;
            word-break: break-all;
            text-align: left;
        }

        .speed-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }

        .order-id {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        .status-progress-section {
            padding: 20px 24px;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-bottom: 1px solid #e9ecef;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .status-info, .progress-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .status-label, .progress-label {
            font-size: 12px;
            color: #666;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-value {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        .status-badge.primary { background: #cce7ff; color: #004085; }

        .refresh-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            cursor: pointer;
        }

        .progress-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .progress-bar-modern {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            border-radius: 4px;
        }

        .progress-text {
            font-size: 12px;
            font-weight: 600;
            color: #007bff;
            min-width: 40px;
        }

        .info-section {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .section-title {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 8px;
            border-left: 4px solid #409eff;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 500;
        }

        .account-detail {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 16px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .account-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 12px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .account-label {
            font-size: 12px;
            color: #666;
            font-weight: 600;
            min-width: 50px;
            margin-right: 12px;
            text-align: left;
            flex-shrink: 0;
        }

        .account-value {
            flex: 1;
            font-size: 13px;
            color: #2c3e50;
            font-weight: 500;
            margin-right: 8px;
            text-align: left;
        }

        .copy-btn-small {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
        }

        .copy-all-btn-detail {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .action-section {
            background: #f8f9fa;
            padding: 20px 24px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
        }

        .action-btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            color: white;
        }

        .action-btn.danger { background: linear-gradient(135deg, #dc3545, #c82333); }
        .action-btn.info { background: linear-gradient(135deg, #17a2b8, #138496); }
        .action-btn.primary { background: linear-gradient(135deg, #007bff, #0056b3); }
        .action-btn.default { background: linear-gradient(135deg, #6c757d, #5a6268); }

        @media (max-width: 768px) {
            .detail-header {
                padding: 8px 12px;
                flex-direction: row;
                align-items: center;
                gap: 6px;
            }

            .header-left {
                flex-direction: column;
                align-items: flex-start;
                gap: 2px;
            }

            .order-title {
                font-size: 12px;
                line-height: 1.2;
            }

            .speed-tag {
                font-size: 8px;
                padding: 1px 4px;
                margin-left: 0;
                margin-top: 2px;
            }

            .order-id {
                font-size: 10px;
            }

            .status-progress-section {
                grid-template-columns: 1fr;
                gap: 8px;
                padding: 6px 12px;
            }

            .info-section, .action-section {
                padding: 6px 12px;
            }

            .section-title {
                font-size: 11px;
                margin-bottom: 6px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 4px;
            }

            .action-buttons {
                grid-template-columns: repeat(2, 1fr);
                gap: 4px;
            }

            .action-btn {
                padding: 5px 6px;
                font-size: 9px;
            }

            .info-item {
                padding: 4px 6px;
            }

            .info-label {
                font-size: 8px;
                margin-bottom: 1px;
            }

            .info-value {
                font-size: 9px;
            }

            .account-detail {
                padding: 6px;
            }

            .account-item {
                padding: 3px 4px;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .account-label {
                font-size: 8px;
                min-width: 25px;
                margin-right: 4px;
                text-align: left;
                flex-shrink: 0;
            }

            .account-value {
                flex: 1;
                font-size: 9px;
                margin-right: 4px;
                text-align: left;
            }

            .copy-btn-small {
                padding: 1px 3px;
                font-size: 7px;
            }

            .copy-all-btn-detail {
                padding: 4px 6px;
                font-size: 9px;
                gap: 2px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-section">
            <div class="test-title">移动端订单列表优化测试</div>
            <div class="demo-note">
                <strong>测试说明：</strong>这是订单列表移动端优化的演示页面。请在不同设备上测试以下功能：
                <br>• 搜索区域默认收起，点击展开
                <br>• 批量操作区域默认收起，点击展开
                <br>• 卡片式布局适配
                <br>• 优化的操作按钮大小
                <br>• 更紧凑的账号信息框
                <br>• 移动端弹窗优化
                <br>• 触摸反馈效果
                <br>• 复制功能
            </div>

            <!-- 弹窗测试按钮 -->
            <div style="margin-bottom: 15px; text-align: center;">
                <button onclick="testDialog()" style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 8px; margin: 0 5px;">测试确认弹窗</button>
                <button onclick="testMessage()" style="padding: 8px 16px; background: #67c23a; color: white; border: none; border-radius: 8px; margin: 0 5px;">测试消息提示</button>
                <button onclick="showOrderDetail()" style="padding: 8px 16px; background: #e6a23c; color: white; border: none; border-radius: 8px; margin: 0 5px;">查看订单详情</button>
            </div>

            <!-- 搜索区域演示 -->
            <div class="mobile-search-toggle" onclick="toggleSearch()">
                <span><i>🔍</i> 搜索筛选</span>
                <i id="searchArrow">▼</i>
            </div>

            <div class="search-demo" id="searchDemo" style="display: none;">
                <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
                    <p style="margin: 0; color: #666; font-size: 13px;">
                        这里是搜索筛选区域，在实际页面中包含项目名称、订单状态、处理状态、时间范围等筛选条件。
                        移动端默认收起，需要时点击展开。
                    </p>
                </div>
            </div>

            <!-- 批量操作区域演示 -->
            <div class="mobile-batch-toggle" onclick="toggleBatch()">
                <span><i>⚙️</i> 批量操作</span>
                <i id="batchArrow">▼</i>
            </div>

            <div class="batch-demo" id="batchDemo" style="display: none;">
                <div style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
                    <p style="margin: 0 0 10px 0; color: #666; font-size: 13px;">
                        这里是批量操作区域，包含以下功能：
                    </p>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 10px;">
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #409eff; color: white; font-size: 12px;">批量更新</button>
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #e6a23c; color: white; font-size: 12px;">批量补单</button>
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #909399; color: white; font-size: 12px;">批量停止</button>
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #f56c6c; color: white; font-size: 12px;">批量转秒</button>
                    </div>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 10px;">
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #67c23a; color: white; font-size: 12px;">手动刷新</button>
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #409eff; color: white; font-size: 12px;">更新进度</button>
                        <button style="padding: 6px 12px; border: none; border-radius: 6px; background: #909399; color: white; font-size: 12px;">设置间隔</button>
                    </div>
                    <p style="margin: 10px 0 0 0; color: #666; font-size: 12px;">
                        移动端默认收起，需要时点击展开。
                    </p>
                </div>
            </div>
            
            <!-- 示例订单卡片 -->
            <div class="order-card">
                <div class="card-header">
                    <div>
                        <span class="platform-name">智慧树网课平台</span>
                        <span class="miao-tag">秒单</span>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <div class="dropdown-demo">
                            <button class="dropdown-btn" onclick="toggleDropdown('dropdown1')">
                                操作 ▼
                            </button>
                            <div class="dropdown-menu" id="dropdown1">
                                <div class="dropdown-item" onclick="testAction('刷新')">刷新</div>
                                <div class="dropdown-item" onclick="testAction('补单')">补单</div>
                                <div class="dropdown-item" onclick="testAction('停止')">停止</div>
                                <div class="dropdown-item" onclick="testAction('一键反馈')">一键反馈</div>
                            </div>
                        </div>
                        <button class="copy-btn" onclick="testCopy('详情')">详情</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="info-row">
                        <div class="info-label">课程名称:</div>
                        <div class="info-value">大学生心理健康教育</div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">账号信息:</div>
                        <div class="info-value account-info">
                            <div class="account-item">
                                <button class="copy-btn" onclick="testCopy('北京大学')">复制</button>
                                <span>北京大学</span>
                            </div>
                            <div class="account-item">
                                <button class="copy-btn" onclick="testCopy('student001')">复制</button>
                                <span>student001</span>
                            </div>
                            <div class="account-item">
                                <button class="copy-btn" onclick="testCopy('password123')">复制</button>
                                <span>password123</span>
                            </div>
                            <button class="copy-all-btn" onclick="testCopyAll()">全部复制</button>
                        </div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">状态:</div>
                        <div class="info-value">
                            <span class="status-btn status-primary">进行中</span>
                        </div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">进度:</div>
                        <div class="info-value progress-info">
                            <span class="progress-text">75%</span>
                            <div class="progress-bar-container">
                                <div class="progress-bar" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">时间:</div>
                        <div class="info-value">2024-01-15 14:30:25</div>
                    </div>
                </div>
            </div>
            
            <!-- 第二个示例卡片 -->
            <div class="order-card">
                <div class="card-header">
                    <div>
                        <span class="platform-name">超星学习通</span>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <div class="dropdown-demo">
                            <button class="dropdown-btn" onclick="toggleDropdown('dropdown2')">
                                操作 ▼
                            </button>
                            <div class="dropdown-menu" id="dropdown2">
                                <div class="dropdown-item" onclick="testAction('刷新')">刷新</div>
                                <div class="dropdown-item" onclick="testAction('补单')">补单</div>
                                <div class="dropdown-item" onclick="testAction('停止')">停止</div>
                                <div class="dropdown-item" onclick="testAction('一键反馈')">一键反馈</div>
                            </div>
                        </div>
                        <button class="copy-btn" onclick="testCopy('详情')">详情</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="info-row">
                        <div class="info-label">课程名称:</div>
                        <div class="info-value">计算机网络基础</div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">账号信息:</div>
                        <div class="info-value account-info">
                            <div class="account-item">
                                <button class="copy-btn" onclick="testCopy('清华大学')">复制</button>
                                <span>清华大学</span>
                            </div>
                            <div class="account-item">
                                <button class="copy-btn" onclick="testCopy('student002')">复制</button>
                                <span>student002</span>
                            </div>
                            <div class="account-item">
                                <button class="copy-btn" onclick="testCopy('pass456')">复制</button>
                                <span>pass456</span>
                            </div>
                            <button class="copy-all-btn" onclick="testCopyAll2()">全部复制</button>
                        </div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">状态:</div>
                        <div class="info-value">
                            <span class="status-btn status-success">已完成</span>
                        </div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">进度:</div>
                        <div class="info-value progress-info">
                            <span class="progress-text">100%</span>
                            <div class="progress-bar-container">
                                <div class="progress-bar" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">时间:</div>
                        <div class="info-value">2024-01-14 09:15:30</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试复制功能
        function testCopy(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showMessage('已复制: ' + text);
                }).catch(() => {
                    showMessage('复制失败');
                });
            } else {
                // 降级方案
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                textarea.style.left = '-999999px';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showMessage('已复制: ' + text);
            }
        }
        
        function testCopyAll() {
            const text = '学校: 北京大学\n账号: student001\n密码: password123';
            testCopy(text);
        }
        
        function testCopyAll2() {
            const text = '学校: 清华大学\n账号: student002\n密码: pass456';
            testCopy(text);
        }
        
        function showMessage(msg) {
            // 简单的消息提示
            const toast = document.createElement('div');
            toast.textContent = msg;
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                z-index: 9999;
                font-size: 14px;
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                document.body.removeChild(toast);
            }, 2000);
        }

        // 搜索区域切换功能
        function toggleSearch() {
            const searchDemo = document.getElementById('searchDemo');
            const searchArrow = document.getElementById('searchArrow');

            if (searchDemo.style.display === 'none') {
                searchDemo.style.display = 'block';
                searchArrow.textContent = '▲';
            } else {
                searchDemo.style.display = 'none';
                searchArrow.textContent = '▼';
            }
        }

        // 批量操作区域切换功能
        function toggleBatch() {
            const batchDemo = document.getElementById('batchDemo');
            const batchArrow = document.getElementById('batchArrow');

            if (batchDemo.style.display === 'none') {
                batchDemo.style.display = 'block';
                batchArrow.textContent = '▲';
            } else {
                batchDemo.style.display = 'none';
                batchArrow.textContent = '▼';
            }
        }

        // 测试弹窗功能
        function testDialog() {
            if (confirm('这是一个测试确认弹窗\n\n在移动端应该正确居中显示，文字应该完整可见。\n\n是否继续测试？')) {
                showMessage('确认弹窗测试成功！');
            } else {
                showMessage('取消了操作');
            }
        }

        function testMessage() {
            showMessage('这是一个测试消息提示，在移动端应该正确显示并居中');
        }

        function showOrderDetail() {
            // 创建一个模拟的订单详情弹窗
            const detailHtml = `
                <div class="modern-order-detail">
                    <div class="detail-container">
                        <!-- 订单头部信息 -->
                        <div class="detail-header">
                            <div class="header-left">
                                <h3 class="order-title">智慧树网课平台</h3>
                                <span class="speed-tag">⚡ 秒刷</span>
                            </div>
                            <div class="header-right">
                                <span class="order-id">#12345</span>
                            </div>
                        </div>

                        <!-- 状态和进度区域 -->
                        <div class="status-progress-section">
                            <div class="status-info">
                                <div class="status-label">订单状态</div>
                                <div class="status-value">
                                    <span class="status-badge primary">进行中</span>
                                    <button class="refresh-btn">
                                        <i>🔄</i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="progress-info">
                                <div class="progress-label">完成进度</div>
                                <div class="progress-container">
                                    <div class="progress-bar-modern">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                    <span class="progress-text">75%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 课程信息区域 -->
                        <div class="info-section">
                            <h4 class="section-title">📚 课程信息</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">课程名称</div>
                                    <div class="info-value">大学生心理健康教育</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">学生姓名</div>
                                    <div class="info-value">张三</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">课程ID</div>
                                    <div class="info-value">KC001234</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">上游YID</div>
                                    <div class="info-value">YID567890</div>
                                </div>
                            </div>
                        </div>

                        <!-- 账号信息区域 -->
                        <div class="info-section">
                            <h4 class="section-title">👤 账号信息</h4>
                            <div class="account-detail">
                                <div class="account-item">
                                    <span class="account-label">学校:</span>
                                    <span class="account-value">北京大学</span>
                                    <button onclick="testCopy('北京大学')" class="copy-btn-small">复制</button>
                                </div>
                                <div class="account-item">
                                    <span class="account-label">账号:</span>
                                    <span class="account-value">student001</span>
                                    <button onclick="testCopy('student001')" class="copy-btn-small">复制</button>
                                </div>
                                <div class="account-item">
                                    <span class="account-label">密码:</span>
                                    <span class="account-value">password123</span>
                                    <button onclick="testCopy('password123')" class="copy-btn-small">复制</button>
                                </div>
                                <button onclick="testCopyAll()" class="copy-all-btn-detail">
                                    <i>📋</i> 复制全部账号信息
                                </button>
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <h4 class="section-title">⚙️ 订单操作</h4>
                            <div class="action-buttons">
                                <button onclick="testAction('秒刷')" class="action-btn danger">
                                    <i>⚡</i> 秒刷
                                </button>
                                <button onclick="testAction('改密码')" class="action-btn info">
                                    <i>✏️</i> 改密码
                                </button>
                                <button onclick="testAction('补单')" class="action-btn primary">
                                    <i>🔄</i> 补单
                                </button>
                                <button onclick="testAction('取消')" class="action-btn default">
                                    <i>❌</i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 创建弹窗容器
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9999;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                border-radius: 12px;
                max-width: 600px;
                width: 100%;
                height: 95vh;
                overflow: hidden;
                box-shadow: 0 10px 40px rgba(0,0,0,0.15);
                display: flex;
                flex-direction: column;
            `;

            content.innerHTML = detailHtml;
            modal.appendChild(content);
            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        function testCopyAll() {
            const text = '学校: 北京大学\n账号: student001\n密码: password123';
            testCopy(text);
        }

        // 下拉菜单功能
        function toggleDropdown(id) {
            const dropdown = document.getElementById(id);
            const isShow = dropdown.classList.contains('show');

            // 关闭所有下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });

            // 切换当前下拉菜单
            if (!isShow) {
                dropdown.classList.add('show');
            }
        }

        function testAction(action) {
            showMessage('执行操作: ' + action);
            // 关闭下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown-demo')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
        
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
