<?php
/**
 * 更新易教育商品描述脚本
 * 移除商品描述中的"(易教育平台)"标识
 */

// 引入公共配置文件
include('confing/common.php');

echo "<h1>更新易教育商品描述</h1>\n";
echo "<pre>\n";

echo "=== 移除易教育商品描述中的平台标识 ===\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：查找易教育货源
echo "步骤1：查找易教育货源配置\n";
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");

if (!$huoyuan) {
    echo "❌ 未找到易教育货源配置\n";
    exit;
}

echo "✅ 找到易教育货源\n";
echo "   货源ID: {$huoyuan['hid']}\n";
echo "   货源名称: {$huoyuan['name']}\n";

$hid = $huoyuan['hid'];

// 步骤2：查找需要更新的商品
echo "\n步骤2：查找需要更新的易教育商品\n";

$products = $DB->get_rows("SELECT id, name, content FROM qingka_wangke_class WHERE docking = '{$hid}' AND instr(content, '(易教育平台)')");

$total_count = count($products);
echo "找到 {$total_count} 个包含平台标识的商品\n";

if ($total_count == 0) {
    echo "✅ 没有需要更新的商品\n";
    exit;
}

// 步骤3：批量更新商品描述
echo "\n步骤3：批量更新商品描述\n";

$updated_count = 0;
$failed_count = 0;

foreach ($products as $product) {
    $old_content = $product['content'];
    
    // 移除 "(易教育平台)" 标识
    $new_content = str_replace(' | (易教育平台)', '', $old_content);
    $new_content = str_replace('(易教育平台)', '', $new_content);
    
    // 清理可能的多余空格和分隔符
    $new_content = trim($new_content);
    $new_content = rtrim($new_content, ' |');
    
    // 转义SQL字符
    $new_content_escaped = $DB->escape($new_content);
    
    // 更新数据库
    $update_sql = "UPDATE qingka_wangke_class SET content = '{$new_content_escaped}' WHERE id = '{$product['id']}'";
    
    if ($DB->query($update_sql)) {
        $updated_count++;
        echo "✅ 更新商品: {$product['name']}\n";
        echo "   原描述: " . substr($old_content, 0, 100) . "...\n";
        echo "   新描述: " . substr($new_content, 0, 100) . "...\n\n";
    } else {
        $failed_count++;
        echo "❌ 更新失败: {$product['name']}\n";
    }
}

// 步骤4：验证更新结果
echo "\n步骤4：验证更新结果\n";

$remaining_products = $DB->get_rows("SELECT id, name FROM qingka_wangke_class WHERE docking = '{$hid}' AND instr(content, '(易教育平台)')");
$remaining_count = count($remaining_products);

echo "=== 更新完成 ===\n";
echo "总商品数: {$total_count}\n";
echo "成功更新: {$updated_count}\n";
echo "更新失败: {$failed_count}\n";
echo "剩余包含标识的商品: {$remaining_count}\n";

if ($remaining_count > 0) {
    echo "\n⚠️  以下商品仍包含平台标识：\n";
    foreach ($remaining_products as $product) {
        echo "- {$product['name']} (ID: {$product['id']})\n";
    }
} else {
    echo "\n✅ 所有易教育商品描述已成功更新！\n";
}

echo "\n注意：新同步的商品将不再包含平台标识。\n";

echo "</pre>\n";
?>
