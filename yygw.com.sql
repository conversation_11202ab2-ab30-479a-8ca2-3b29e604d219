-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: localhost    Database: yygw_com
-- ------------------------------------------------------
-- Server version	5.7.44-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `qingka_wangke_class`
--

DROP TABLE IF EXISTS `qingka_wangke_class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_class` (
  `cid` int(11) NOT NULL AUTO_INCREMENT,
  `sort` int(11) NOT NULL DEFAULT '10',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '网课平台名字',
  `getnoun` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '查询参数',
  `noun` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '对接参数',
  `price` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '定价',
  `queryplat` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '查询平台',
  `docking` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '对接平台',
  `yunsuan` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '*' COMMENT '代理费率运算',
  `content` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '说明',
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '添加时间',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态0为下架。1为上架',
  `fenlei` varchar(11) COLLATE utf8_unicode_ci NOT NULL COMMENT '分类',
  `uptime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`cid`),
  KEY `name` (`name`),
  KEY `cid` (`cid`),
  KEY `fenlei` (`fenlei`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_class`
--

LOCK TABLES `qingka_wangke_class` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_class` DISABLE KEYS */;
INSERT INTO `qingka_wangke_class` VALUES (10,1,'测试商品','1','1','1','0','0','*','测试介绍','2025-08-16 22:40:47',1,'','');
/*!40000 ALTER TABLE `qingka_wangke_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_class_config`
--

DROP TABLE IF EXISTS `qingka_wangke_class_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_class_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hyid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监听的货源ID，逗号分隔，如9013,9023,9024',
  `huoyuan_price_rate` text COLLATE utf8mb4_unicode_ci COMMENT '各货源价格倍数json，如{"9013":7,"9023":6.5,"9024":5.9}',
  `huoyuan_category_price` text COLLATE utf8mb4_unicode_ci COMMENT '每货源下各分类单独价格倍数json，如{"9013":{"3":7,"4":5.5}}',
  `skip_rule` text COLLATE utf8mb4_unicode_ci COMMENT '跳过规则json（每货源可独立配置cid、区间、分类）',
  `replace_rule` text COLLATE utf8mb4_unicode_ci COMMENT '每货源名称前缀替换规则JSON（如{"9013":[{"from":"[上游]","to":"[前台]"}]}）',
  `klsjkg` tinyint(1) DEFAULT '0' COMMENT '克隆上架自动启用 1=开 0=关',
  `qzkg` tinyint(1) DEFAULT '1' COMMENT '前缀规则启用开关',
  `tgkg` tinyint(1) DEFAULT '1' COMMENT '跳过规则启用开关',
  `tbms` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'full' COMMENT '同步模式 full/incremental',
  `ltxgjg` int(11) DEFAULT '300' COMMENT '同步轮询间隔（秒）',
  `tskg` tinyint(1) DEFAULT '1' COMMENT '推送变更开关 1=开，0=关',
  `sdurl` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Showdoc推送地址',
  `zmsjkg` tinyint(1) DEFAULT '0' COMMENT '6个月未上架自动删除开关',
  `tbjgkg` tinyint(1) DEFAULT '1' COMMENT '同步价格开关，1=开，0=关',
  `tbsmkg` tinyint(1) DEFAULT '1' COMMENT '同步说明开关',
  `tbsjmc` tinyint(1) DEFAULT '0' COMMENT '同步商品名称开关',
  `tbztkg` tinyint(1) DEFAULT '1' COMMENT '同步状态（上下架）开关',
  `qzdqjg` tinyint(1) DEFAULT '0' COMMENT '强制价格对齐，0只涨不降，1强制对齐',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品同步参数及控制配置表（拼音缩写变量版）';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_class_config`
--

LOCK TABLES `qingka_wangke_class_config` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_class_config` DISABLE KEYS */;
INSERT INTO `qingka_wangke_class_config` VALUES (22,'3','{\"3\":6}','{\"3\":{}}','{\"3\":{\"cidList\":[],\"cidRange\":[],\"fenleiList\":[]}}','{\"3\":[]}',1,0,0,'',1,1,'',1,1,1,1,1,0,'','2025-08-14 10:41:27','2025-08-14 10:41:27');
/*!40000 ALTER TABLE `qingka_wangke_class_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_class_log`
--

DROP TABLE IF EXISTS `qingka_wangke_class_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_class_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增ID，日志流水号',
  `product_id` int(11) DEFAULT '0' COMMENT '商品ID（本地商品表cid）',
  `huoyuan_id` int(11) DEFAULT '0' COMMENT '货源ID',
  `huoyuan_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '货源名称',
  `platform_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '本地商品名',
  `fenlei_id` int(11) DEFAULT '0' COMMENT '本地分类ID',
  `fenlei_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '本地分类名',
  `action` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '操作类型（如:更新价格/上架/下架/删除/克隆上架等）',
  `data_before` text COLLATE utf8mb4_unicode_ci COMMENT '变更前数据，JSON字符串',
  `data_after` text COLLATE utf8mb4_unicode_ci COMMENT '变更后数据，JSON字符串',
  `operator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '操作人（自动识别，或auto）',
  `op_time` datetime DEFAULT NULL COMMENT '操作时间',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注，记录所有操作与来源',
  `push_status` tinyint(1) DEFAULT '0' COMMENT '推送结果 1=成功 0=失败',
  PRIMARY KEY (`id`),
  KEY `idx_op_time` (`op_time`),
  KEY `idx_action` (`action`),
  KEY `idx_product` (`product_id`),
  KEY `idx_huoyuan` (`huoyuan_id`),
  KEY `idx_operator` (`operator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品同步变更日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_class_log`
--

LOCK TABLES `qingka_wangke_class_log` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_class_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_class_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_config`
--

DROP TABLE IF EXISTS `qingka_wangke_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_config` (
  `v` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `k` text COLLATE utf8_unicode_ci NOT NULL,
  UNIQUE KEY `v` (`v`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_config`
--

LOCK TABLES `qingka_wangke_config` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_config` DISABLE KEYS */;
INSERT INTO `qingka_wangke_config` VALUES ('akcookie','7321sPn6n+Yt9tGs1wy7f2ULOKbENP2W/J83w50jYbpDpQEXjkGRJnZOlXPY7XeOX5zCSU6vfhOLJSoKLMeWQ7cv9ghbEsFowYoCzQ'),('api_ck','15'),('api_proportion','20'),('api_tongb','3'),('api_xd','5'),('description',''),('dklcookie','d1abUN9efprWp3E93jrzR27IaTDLsmnV7AGLOb+rPKLrml1YDKRVBgXW0JxsfktUYm/Ym/Sk9J4eJiLfEdoPFXgKRuy/vtoV4YXYuRkarpydLsoeMgp93qAWuTWF9vRdk4xdew4QGgPIt/LzbMnUsDOHq/u5HGrnMhU8wbD35T/47sGC5cCVzGlGK565qMhH3kUzazhgiEoEpDJNAOFXttowDLKM+6G1iAApLyUNDxP7rluq383FyGxO3tcYR4VVuTEJA1V0LTNTOzhq8TJXl0l/hGE6JfDmLkDo4piWok2lnG5VTzYdMf6AJYSHwO+W4P2rAxgd3augn/kkJUfvxRiWZWPoddS4n0n1kRpIJrBNgTWuE3U9EQqxIMMwMWLI3IZvP0NZE/Nl0yqyFg'),('epay_api',''),('epay_key',''),('epay_pid',''),('flkg','1'),('fllx','1'),('is_alipay','1'),('is_qqpay','0'),('is_wxpay','1'),('keywords',''),('logo','/favicon.ico'),('nanatoken','14cen/DXBsnMXkKPE50giKP/KmWGgIwn/B2sdw9z+b7zL2riFzG2mvyb04YP/xdD5w5h8uvXIKwjCo7AIF3QgzvCoiOdJfK+a9IdjpFcb2mSpzqGg9jRrEeFOomuokkA0F971RhK'),('notice','测试'),('settings','1'),('sitename','猿猿国王'),('sjqykg','1'),('sykg','1'),('tcgonggao','测试公告，别几把废话就是对接推就完了'),('user_htkh','1'),('user_ktmoney','0'),('user_yqzc','0'),('zdpay','0');
/*!40000 ALTER TABLE `qingka_wangke_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_czk`
--

DROP TABLE IF EXISTS `qingka_wangke_czk`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_czk` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `endtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `usetime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `uid` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_czk`
--

LOCK TABLES `qingka_wangke_czk` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_czk` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_czk` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_dengji`
--

DROP TABLE IF EXISTS `qingka_wangke_dengji`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_dengji` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sort` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  `rate` decimal(10,2) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `addkf` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  `gjkf` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  `status` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  `time` varchar(11) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_dengji`
--

LOCK TABLES `qingka_wangke_dengji` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_dengji` DISABLE KEYS */;
INSERT INTO `qingka_wangke_dengji` VALUES (1,'1','国王',0.20,500.00,'1','1','1','2023-03-14 '),(2,'2','大公爵',0.25,250.00,'1','1','1','2023-03-15 '),(3,'3','公爵',0.30,200.00,'1','1','1','2023-03-15 '),(4,'4','侯爵',0.35,150.00,'1','1','1','2024-03-17 '),(5,'5','伯爵',0.40,100.00,'1','1','1','2024-03-17 '),(6,'6','子爵',0.45,50.00,'1','1','1','2024-03-17 '),(7,'7','男爵',0.50,30.00,'1','1','1','2024-03-17 '),(8,'8','平民',0.75,10.00,'1','1','1','2024-03-17 ');
/*!40000 ALTER TABLE `qingka_wangke_dengji` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_fenlei`
--

DROP TABLE IF EXISTS `qingka_wangke_fenlei`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_fenlei` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sort` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `name` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `text` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `time` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_fenlei`
--

LOCK TABLES `qingka_wangke_fenlei` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_fenlei` DISABLE KEYS */;
INSERT INTO `qingka_wangke_fenlei` VALUES (99,'','我的收藏','你的收藏夹','1','2025-08-15 ');
/*!40000 ALTER TABLE `qingka_wangke_fenlei` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_gongdan`
--

DROP TABLE IF EXISTS `qingka_wangke_gongdan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_gongdan` (
  `gid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `region` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '工单类型',
  `title` text COLLATE utf8_unicode_ci NOT NULL COMMENT '工单标题',
  `content` text COLLATE utf8_unicode_ci NOT NULL COMMENT '工单内容',
  `answer` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '工单回复',
  `state` varchar(11) COLLATE utf8_unicode_ci NOT NULL COMMENT '工单状态',
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`gid`),
  KEY `region` (`region`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_gongdan`
--

LOCK TABLES `qingka_wangke_gongdan` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_gongdan` DISABLE KEYS */;
INSERT INTO `qingka_wangke_gongdan` VALUES (7,473,'bug反馈','为什么下单余额会变少','希望尽快修复！','','待回复','2023-06-21 17:46:24'),(8,492,'其他问题','无','2025-08-16 16:58:29 用户提问: 没问题','','待回复','2025-08-16 16:58:29'),(9,1,'1','测试商品\n清华大学 123456 123456\n母猪的产后护理\n状态: 待处理 备注: \n下单时间: 2025-08-16 22:41:15','2025-08-16 22:42:15 用户反馈: 测试工单','','待回复','2025-08-16 22:42:15');
/*!40000 ALTER TABLE `qingka_wangke_gongdan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_gonggao`
--

DROP TABLE IF EXISTS `qingka_wangke_gonggao`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_gonggao` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `time` varchar(255) NOT NULL,
  `uid` int(11) NOT NULL,
  `status` int(11) NOT NULL COMMENT '状态',
  `zhiding` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `time` (`time`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_gonggao`
--

LOCK TABLES `qingka_wangke_gonggao` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_gonggao` DISABLE KEYS */;
INSERT INTO `qingka_wangke_gonggao` VALUES (1,'新学期新气象','layui2.0让我们越走越远','2025-08-10 21:20:05',1,1,0),(7,'即将发布','全新29.5系统','2025-08-11 01:28:56',1,1,0),(9,'8月16号新搭建测试','测试内容','2025-08-16 15:23:39',1,1,1);
/*!40000 ALTER TABLE `qingka_wangke_gonggao` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_huodong`
--

DROP TABLE IF EXISTS `qingka_wangke_huodong`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_huodong` (
  `hid` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '活动名字',
  `yaoqiu` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '要求',
  `type` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '1为邀人活动 2为订单活动',
  `num` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '要求数量',
  `money` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '奖励',
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '活动开始时间',
  `endtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '活动结束时间',
  `status_ok` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '1' COMMENT '1为正常 2为结束',
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '1' COMMENT '1为进行中 2为待领取 3为已完成',
  PRIMARY KEY (`hid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_huodong`
--

LOCK TABLES `qingka_wangke_huodong` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_huodong` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_huodong` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_huoyuan`
--

DROP TABLE IF EXISTS `qingka_wangke_huoyuan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_huoyuan` (
  `hid` int(11) NOT NULL AUTO_INCREMENT,
  `pt` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '不带http 顶级',
  `user` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `pass` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `token` varchar(500) COLLATE utf8_unicode_ci NOT NULL,
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `cookie` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `money` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '1',
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `endtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`hid`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_huoyuan`
--

LOCK TABLES `qingka_wangke_huoyuan` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_huoyuan` DISABLE KEYS */;
INSERT INTO `qingka_wangke_huoyuan` VALUES (40,'29','测试','','','','','','','0','1','2025-08-15 22:26:16','');
/*!40000 ALTER TABLE `qingka_wangke_huoyuan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_km`
--

DROP TABLE IF EXISTS `qingka_wangke_km`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_km` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '卡密id',
  `content` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '卡密内容',
  `money` int(11) NOT NULL COMMENT '卡密金额',
  `status` int(11) DEFAULT NULL COMMENT '卡密状态',
  `uid` int(11) DEFAULT NULL COMMENT '使用者id',
  `addtime` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '添加时间',
  `usedtime` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '使用时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_km`
--

LOCK TABLES `qingka_wangke_km` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_km` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_km` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_log`
--

DROP TABLE IF EXISTS `qingka_wangke_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `type` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `text` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `money` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `smoney` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_log`
--

LOCK TABLES `qingka_wangke_log` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_log` DISABLE KEYS */;
INSERT INTO `qingka_wangke_log` VALUES (1,1,'批量提交',' 测试商品 清华大学 123456 123456 母猪的产后护理 ','-0.2','119045.80','**************','2025-08-16 14:41:15');
/*!40000 ALTER TABLE `qingka_wangke_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_mijia`
--

DROP TABLE IF EXISTS `qingka_wangke_mijia`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_mijia` (
  `mid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL,
  `mode` int(11) NOT NULL COMMENT '0.价格的基础上扣除 1.倍数的基础上扣除 2.直接定价',
  `price` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `addtime` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_mijia`
--

LOCK TABLES `qingka_wangke_mijia` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_mijia` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_mijia` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_order`
--

DROP TABLE IF EXISTS `qingka_wangke_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_order` (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL COMMENT '平台ID',
  `hid` int(11) NOT NULL COMMENT '接口ID',
  `yid` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '对接站ID',
  `ptname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '平台名字',
  `school` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '学校',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名',
  `user` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '账号',
  `pass` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '密码',
  `phone` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '手机号',
  `kcid` text COLLATE utf8_unicode_ci NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '课程名字',
  `courseStartTime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '课程开始时间',
  `courseEndTime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '课程结束时间',
  `examStartTime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '考试开始时间',
  `examEndTime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '考试结束时间',
  `chapterCount` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '总章数',
  `unfinishedChapterCount` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '剩余章数',
  `cookie` text COLLATE utf8_unicode_ci NOT NULL COMMENT 'cookie',
  `fees` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '扣费',
  `noun` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '对接标识',
  `miaoshua` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '0不秒 1秒',
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '添加时间',
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '下单ip',
  `dockstatus` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '对接状态 0待 1成 2失 3重复 4取消',
  `loginstatus` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '待处理',
  `process` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `bsnum` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '备注',
  `score` varchar(11) COLLATE utf8_unicode_ci NOT NULL COMMENT '分数',
  `shichang` varchar(11) COLLATE utf8_unicode_ci NOT NULL COMMENT '时长',
  `updatetime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`oid`),
  KEY `uid` (`uid`),
  KEY `cid` (`cid`),
  KEY `yid` (`yid`),
  KEY `hid` (`hid`),
  KEY `ptname` (`ptname`),
  KEY `school` (`school`),
  KEY `name` (`name`),
  KEY `user` (`user`),
  KEY `pass` (`pass`),
  KEY `phone` (`phone`),
  KEY `user_2` (`user`),
  KEY `cid_2` (`cid`),
  KEY `oid` (`oid`),
  KEY `courseStartTime` (`courseStartTime`),
  KEY `kcname` (`kcname`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_order`
--

LOCK TABLES `qingka_wangke_order` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_order` DISABLE KEYS */;
INSERT INTO `qingka_wangke_order` VALUES (1,1,10,0,'','测试商品','清华大学','站长','123456','123456','','0','母猪的产后护理','','0','','','','','','0.2','1','0','2025-08-16 22:41:15','**************','99','','待处理','','0','','','','2025-08-16 14:41:15');
/*!40000 ALTER TABLE `qingka_wangke_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_pay`
--

DROP TABLE IF EXISTS `qingka_wangke_pay`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_pay` (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `out_trade_no` varchar(64) COLLATE utf8_unicode_ci NOT NULL,
  `trade_no` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `type` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `uid` int(11) NOT NULL,
  `num` int(11) NOT NULL DEFAULT '1',
  `addtime` datetime DEFAULT NULL,
  `endtime` datetime DEFAULT NULL,
  `name` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
  `money` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ip` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `domain` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`oid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_pay`
--

LOCK TABLES `qingka_wangke_pay` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_pay` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_pay` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_user`
--

DROP TABLE IF EXISTS `qingka_wangke_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_user` (
  `uid` int(11) NOT NULL AUTO_INCREMENT,
  `uuid` int(11) NOT NULL,
  `user` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `pass` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `qq_openid` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT 'QQuid',
  `nickname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT 'QQ昵称',
  `faceimg` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT 'QQ头像',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00',
  `zcz` varchar(10) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  `addprice` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '加价',
  `key` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  `yqm` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '邀请码',
  `yqprice` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '邀请单价',
  `notice` text COLLATE utf8_unicode_ci NOT NULL,
  `tuisongtoken` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `addtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '添加时间',
  `endtime` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `grade` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `active` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '1',
  `khqx` int(11) NOT NULL,
  `user_settings` text COLLATE utf8_unicode_ci,
  PRIMARY KEY (`uid`),
  KEY `user` (`user`),
  KEY `uuid` (`uuid`),
  KEY `pass` (`pass`),
  KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=494 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_user`
--

LOCK TABLES `qingka_wangke_user` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_user` DISABLE KEYS */;
INSERT INTO `qingka_wangke_user` VALUES (1,1,'123123','123123','站长','','','',119045.80,'30000',0.20,'0','','','','','2022-05-20 22:58:54','2025-08-16 22:56:37','*************','','1',0,NULL),(492,1,'1245784512','1234567','直属下级','','','',385.58,'2253.8',0.20,'0','','','','','2025-08-08 12:30:46','2025-08-16 17:03:44','*************','','1',0,NULL),(493,492,'1245784513','1234567','下级的下级','','','',30.00,'30',0.50,'0','','','','','2025-08-10 23:12:44','2025-08-16 17:05:32','*************','','1',0,NULL);
/*!40000 ALTER TABLE `qingka_wangke_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_zhiya_config`
--

DROP TABLE IF EXISTS `qingka_wangke_zhiya_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_zhiya_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `amount` decimal(10,2) NOT NULL COMMENT '质押金额',
  `discount_rate` decimal(10,2) NOT NULL COMMENT '折扣率',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1生效 0禁用',
  `addtime` datetime NOT NULL COMMENT '添加时间',
  `days` int(11) NOT NULL DEFAULT '30' COMMENT '质押天数',
  `cancel_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提前取消扣费比例(0-1)',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='质押配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_zhiya_config`
--

LOCK TABLES `qingka_wangke_zhiya_config` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_zhiya_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_zhiya_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingka_wangke_zhiya_records`
--

DROP TABLE IF EXISTS `qingka_wangke_zhiya_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingka_wangke_zhiya_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `config_id` int(11) NOT NULL COMMENT '质押配置ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1生效 0已退还',
  `money` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `addtime` datetime NOT NULL COMMENT '质押时间',
  `endtime` datetime DEFAULT NULL COMMENT '退还时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_config` (`config_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='质押记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingka_wangke_zhiya_records`
--

LOCK TABLES `qingka_wangke_zhiya_records` WRITE;
/*!40000 ALTER TABLE `qingka_wangke_zhiya_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `qingka_wangke_zhiya_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'yygw_com'
--

--
-- Dumping routines for database 'yygw_com'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- --------------------------------------------------------

--
-- 易教育专用表结构
--

--
-- 表的结构 `qingka_wangke_jxjyclass`
--

CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `number` varchar(255) NOT NULL COMMENT '对接站ID',
  `name` varchar(255) NOT NULL,
  `format` varchar(255) NOT NULL COMMENT '下单格式',
  `url` varchar(255) NOT NULL COMMENT 'url',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `unitId` varchar(255) NOT NULL,
  `must` varchar(255) NOT NULL,
  `unit` varchar(255) NOT NULL COMMENT '价格类型',
  `rate` varchar(255) NOT NULL COMMENT '速率',
  `isExam` varchar(255) NOT NULL COMMENT '考试',
  `isSearchCourse` varchar(255) NOT NULL COMMENT '查课',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
  `description` text COMMENT '描述',
  `remark` varchar(255) NOT NULL,
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `number` (`number`),
  KEY `status` (`status`),
  KEY `isSearchCourse` (`isSearchCourse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='易教育项目表';

--
-- 表的结构 `qingka_wangke_jxjyorder`
--

CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyorder` (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL COMMENT '平台ID',
  `yid` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '对接站ID',
  `ptname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '平台名字',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名',
  `user` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '账号',
  `pass` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '密码',
  `kcid` text COLLATE utf8_unicode_ci NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '课程名字',
  `fees` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '扣费',
  `noun` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '对接参数number',
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '待处理',
  `process` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0%',
  `bsnum` int(11) NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` text COLLATE utf8_unicode_ci COMMENT '备注',
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '下单IP',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`oid`),
  KEY `uid` (`uid`),
  KEY `yid` (`yid`),
  KEY `user` (`user`),
  KEY `status` (`status`),
  KEY `addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='易教育订单表';

-- Dump completed on 2025-08-16 23:01:04
