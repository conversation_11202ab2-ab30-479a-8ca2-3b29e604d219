# 订单进度自动同步系统 - 项目结构说明

## 📁 项目概述
本项目是一个完整的订单进度自动同步系统，支持多平台（包括8090教育）的订单进度实时同步和前端显示更新。

## 🏗️ 核心架构

### 1. 后台同步系统
```
redis/
├── chusheng1.php          # 主要进度同步守护进程 ⭐
├── chusheng2.php          # 备份进度同步守护进程 
├── chusheng3.php          # 特殊队列处理进程
├── aaru.php               # 手动入队脚本
└── auto_enqueue_orders.php # 自动入队脚本 ⭐
```

### 2. 前端更新系统
```
api/
└── progress_update.php    # 进度查询API ⭐

index/
└── list.php              # 订单列表页面（已添加实时更新功能）⭐
```

### 3. 进度查询核心
```
Checkorder/
└── jdjk.php              # 各平台进度查询函数 ⭐
```

### 4. 配置和文档
```
confing/
└── common.php            # 公共配置文件

8090教育对接文档.md        # 8090教育平台对接说明
订单列表自动刷新功能说明.md  # 功能详细说明文档
项目结构说明.md           # 本文档
```

## ⚙️ 系统工作流程

### 自动同步流程
```
1. 计划任务 (每3分钟)
   ↓
2. auto_enqueue_orders.php (自动入队)
   ↓
3. Redis队列 (oidsydcl)
   ↓
4. chusheng1.php/chusheng2.php (守护进程)
   ↓
5. jdjk.php (调用各平台API)
   ↓
6. 数据库更新 (订单进度)
```

### 前端更新流程
```
1. 用户访问订单列表页面
   ↓
2. 页面每30秒调用 progress_update.php
   ↓
3. API查询数据库最新进度
   ↓
4. 前端更新显示（不刷新页面）
```

## 🔧 关键组件说明

### 守护进程
- **chusheng1.php**: 主要进程，处理所有队列，包括进度同步
- **chusheng2.php**: 备份进程，与chusheng1功能相同，提供冗余
- **chusheng3.php**: 特殊进程，不处理主要进度同步队列

### 队列系统
- **oidsydcl (数据库7)**: 主要进度同步队列 ⭐
- **plztoid (数据库0)**: 批量状态更新
- **oidjxz (数据库9)**: 精准下单
- **oidblpt (数据库6)**: 补量平台
- **oidstdks (数据库10)**: 手动考试

### 计划任务
```bash
# 每3分钟自动入队
*/3 * * * * /usr/bin/php /www/wwwroot/117.72.158.75/redis/auto_enqueue_orders.php
```

## 🎯 支持的平台

### 8090教育 (8090edu)
- **API认证**: Token方式
- **进度查询**: 通过订单列表API
- **状态映射**: 已完成→100%, 进行中→动态%, 已退款→0%
- **特殊处理**: 订单ID匹配和用户名验证

### 其他平台
- **yyy平台**: 传统API方式
- **各种第三方平台**: 通过统一的processCx函数处理

## 📊 功能特性

### 后台自动同步
- ✅ 多平台支持
- ✅ 自动入队机制
- ✅ 守护进程冗余
- ✅ 错误处理和日志
- ✅ 性能优化

### 前端实时更新
- ✅ 30秒自动轮询
- ✅ 不刷新页面
- ✅ 智能暂停（弹窗时）
- ✅ 用户控制（开关、间隔设置）
- ✅ 状态显示

### 用户体验
- ✅ 无感知更新
- ✅ 手动控制选项
- ✅ 状态透明显示
- ✅ 操作状态保持

## 🚀 部署和运维

### 必需的守护进程
```bash
# 推荐配置
✅ chusheng1.php - 主要进程（必须）
✅ chusheng2.php - 备份进程（推荐）
❌ chusheng3.php - 特殊进程（可选）
```

### 监控要点
- Redis队列长度
- 守护进程运行状态
- 数据库连接状态
- API调用成功率

### 日志位置
- 自动入队日志: `redis/auto_enqueue.log`
- 守护进程日志: 宝塔面板查看
- API错误日志: 浏览器控制台

## 🔍 故障排除

### 常见问题
1. **进度不更新**: 检查守护进程状态
2. **前端不刷新**: 检查API调用和网络
3. **8090教育特殊问题**: 检查Token有效性

### 调试方法
1. 检查Redis队列: `redis-cli -n 7 llen oidsydcl`
2. 检查守护进程: `ps aux | grep chusheng`
3. 检查计划任务: `crontab -l`
4. 手动测试API: 访问 `api/progress_update.php`

## 📈 性能指标

### 处理能力
- **chusheng1**: 500订单/次，队列阈值200
- **chusheng2**: 500订单/次，队列阈值150
- **自动入队**: 每3分钟，最多20个订单

### 响应时间
- **API调用**: <100ms（正常）
- **进度查询**: <30s（8090教育）
- **前端更新**: 30秒间隔

## 🎉 项目成果

### 解决的问题
- ❌ 订单进度不自动更新 → ✅ 完全自动化同步
- ❌ 需要手动点击同步 → ✅ 无需人工干预
- ❌ 8090教育进度获取失败 → ✅ 完美支持
- ❌ 前端显示不实时 → ✅ 30秒自动更新

### 技术亮点
- 🔧 多进程守护系统
- 🔄 Redis队列管理
- 🌐 RESTful API设计
- 📱 响应式前端更新
- 🛡️ 完善的错误处理

**🎯 系统现已完全自动化，用户可以实时看到所有平台（包括8090教育）的订单进度更新！**
