#!/bin/bash

echo "=========================================="
echo "删除Redis systemd服务脚本"
echo "=========================================="

# 定义所有Redis服务名称
services=("redis-chusheng1" "redis-chusheng2" "redis-chusheng3" "redis-addchu" "redis-plbs" "redis-plms" "redis-pltx")

echo "正在停止所有Redis服务..."
echo ""

# 停止所有服务
for service in "${services[@]}"; do
    echo "停止服务: $service"
    systemctl stop "$service" 2>/dev/null
    
    if systemctl is-active --quiet "$service"; then
        echo "❌ $service 停止失败"
    else
        echo "✅ $service 已停止"
    fi
done

echo ""
echo "正在禁用所有Redis服务..."
echo ""

# 禁用所有服务
for service in "${services[@]}"; do
    echo "禁用服务: $service"
    systemctl disable "$service" 2>/dev/null
    echo "✅ $service 已禁用"
done

echo ""
echo "正在删除服务文件..."
echo ""

# 删除服务文件
for service in "${services[@]}"; do
    service_file="/etc/systemd/system/${service}.service"
    if [ -f "$service_file" ]; then
        echo "删除文件: $service_file"
        rm -f "$service_file"
        echo "✅ $service_file 已删除"
    else
        echo "⚠️  文件不存在: $service_file"
    fi
done

echo ""
echo "重新加载systemd配置..."
systemctl daemon-reload

echo ""
echo "删除定时任务..."

# 删除crontab中的Redis任务
echo "正在删除crontab中的Redis相关任务..."
crontab -l 2>/dev/null | grep -v "redis_cron_tasks.sh" | grep -v "# Redis队列入队任务" | crontab -
echo "✅ 定时任务已删除"

echo ""
echo "删除相关脚本和日志..."

# 删除定时任务脚本
BASE_PATH="/www/wwwroot/117.72.158.75"
if [ -f "$BASE_PATH/redis_cron_tasks.sh" ]; then
    rm -f "$BASE_PATH/redis_cron_tasks.sh"
    echo "✅ 删除定时任务脚本: redis_cron_tasks.sh"
fi

# 删除日志目录（可选）
if [ -d "$BASE_PATH/logs" ]; then
    echo "是否删除日志目录? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -rf "$BASE_PATH/logs"
        echo "✅ 日志目录已删除"
    else
        echo "⚠️  保留日志目录"
    fi
fi

echo ""
echo "=========================================="
echo "清理完成！"
echo "=========================================="
echo ""
echo "验证清理结果:"

# 验证服务是否已删除
echo "检查剩余的Redis服务:"
remaining_services=$(systemctl list-units --all | grep redis- | wc -l)
if [ "$remaining_services" -eq 0 ]; then
    echo "✅ 所有Redis服务已清理完成"
else
    echo "⚠️  仍有 $remaining_services 个Redis服务存在"
    systemctl list-units --all | grep redis-
fi

echo ""
echo "检查定时任务:"
redis_cron_count=$(crontab -l 2>/dev/null | grep -c redis)
if [ "$redis_cron_count" -eq 0 ]; then
    echo "✅ 所有Redis定时任务已清理完成"
else
    echo "⚠️  仍有 $redis_cron_count 个Redis定时任务存在"
    crontab -l 2>/dev/null | grep redis
fi

echo ""
echo "现在您可以使用宝塔面板的图形界面重新配置守护进程了！"
echo ""
echo "建议步骤:"
echo "1. 登录宝塔面板"
echo "2. 安装'进程守护管理器'插件"
echo "3. 手动添加需要的守护进程"
echo "4. 在'计划任务'中添加定时任务"
