# yyy教育货源对接完整指南

## 概述
本文档详细说明如何将yyy教育货源对接到现有的网课代刷平台系统中。yyy教育是一个聚合平台，支持1175+个教育网站的查课和下单功能。

## 货源信息
- **货源名称**: yyy教育 (1175+网站聚合平台)
- **API基础地址**: http://*************:6966/api
- **网站地址**: http://*************:6966/
- **账号**: 573749877
- **密码**: liuyaxin123
- **平台标识**: yyy
- **认证方式**: Bearer Token + Cookie 双重认证

## 对接步骤

### 第一步：数据库配置
执行SQL脚本 `sql/yyy_education_setup.sql` 来创建必要的数据库表和初始数据：

```bash
mysql -u用户名 -p密码 数据库名 < sql/yyy_education_setup.sql
```

### 第二步：接口文件修改
已完成以下文件的修改：

1. **查课接口** (`Checkorder/ckjk.php`)
   - 添加了yyy教育的查课逻辑
   - 支持Bearer Token + Cookie双重认证
   - 使用制表符分隔账号密码进行查课
   - 解析返回的课程信息（格式：账号----密码----课程名称）

2. **下单接口** (`Checkorder/xdjk.php`)
   - 添加了yyy教育的下单逻辑
   - 使用空格分隔账号、密码、课程名称进行下单
   - 支持Bearer Token + Cookie双重认证

3. **订单查询接口** (`Checkorder/jdjk.php`)
   - 添加了订单状态查询功能
   - 支持通过账号名查询相关订单
   - 返回订单状态和进度信息

4. **补刷接口** (`Checkorder/bsjk.php`)
   - 通过重新下单实现补刷功能
   - yyy教育平台没有专门的补刷接口

5. **暂停接口** (`Checkorder/ztjk.php`)
   - yyy教育平台不支持暂停功能，返回相应提示

6. **秒刷接口** (`Checkorder/msjk.php`)
   - yyy教育平台不支持秒刷功能，返回相应提示

### 第三步：系统配置
1. 登录管理后台
2. 进入货源管理页面
3. 确认yyy教育货源已添加并启用
4. 检查分类和商品是否正确创建

### 第四步：测试验证
1. **查课测试**
   - 选择yyy教育分类下的商品
   - 输入测试账号密码
   - 验证是否能正确返回课程信息

2. **下单测试**
   - 提交一个测试订单
   - 检查订单状态是否正确更新
   - 验证上游订单ID是否正确返回

3. **功能测试**
   - 测试补刷功能
   - 测试暂停功能
   - 测试秒刷功能

## API接口说明

### 1. 登录接口
- **URL**: `http://*************:6966/api/login`
- **方法**: POST
- **Headers**: `Content-Type: application/json`
- **参数**:
  ```json
  {
    "username": "573749877",
    "password": "liuyaxin123"
  }
  ```
- **返回**:
  ```json
  {
    "code": 200,
    "data": {
      "username": "573749877",
      "accessToken": "c256668535c8576190496e65871f35b9",
      "refreshToken": "c256668535c8576190496e65871f35b9",
      "expires": "2030/10/30 00:00:00"
    },
    "message": "登录成功"
  }
  ```

### 2. 获取网站列表
- **URL**: `http://*************:6966/api/site`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
  - `Cookie: authorized-token={encoded_token}; multiple-tabs=true`
- **参数**:
  ```json
  {
    "version": "20250622004954"
  }
  ```
- **返回**:
  ```json
  {
    "code": 200,
    "data": {
      "version": "20250622145527",
      "lastoid": 2864,
      "itemCount": 1174,
      "list": [
        {
          "id": 1683,
          "name": "合肥永君专业技术人员继续教育在线",
          "trans": "视频+考试 格式：账号 密码 培训名",
          "url": "https://hfyj.zjzx.ah.cn/",
          "price_unit": "2.4 /培训"
        }
      ]
    }
  }
  ```

### 3. 查询课程
- **URL**: `http://*************:6966/api/order`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
  - `Cookie: authorized-token={encoded_token}; multiple-tabs=true`
- **参数**:
  ```json
  {
    "lastoid": "2864",
    "orderData": "账号\t密码",
    "orderNote": "",
    "search": "1"
  }
  ```
- **返回**:
  ```json
  {
    "code": 200,
    "data": [
      "账号----密码----课程名称1",
      "账号----密码----课程名称2"
    ],
    "message": "查询成功"
  }
  ```

### 4. 提交订单
- **URL**: `http://*************:6966/api/order`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
  - `Cookie: authorized-token={encoded_token}; multiple-tabs=true`
- **参数**:
  ```json
  {
    "lastoid": "2864",
    "orderData": "账号 密码 课程名称",
    "orderNote": "",
    "search": "0"
  }
  ```

### 5. 查询订单列表
- **URL**: `http://*************:6966/api/getorder`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
  - `Cookie: authorized-token={encoded_token}; multiple-tabs=true`
- **参数**:
  ```json
  {
    "lastoid": "",
    "odname": "账号名",
    "nickname": "",
    "notetype": "1",
    "note": "",
    "statusbox": [],
    "page": 1,
    "pageSize": 50
  }
  ```

## 重要特性说明

### 1. 数据格式要求
- **查课时**: 使用制表符(`\t`)分隔账号和密码
- **下单时**: 使用空格分隔账号、密码、课程名称
- **课程信息**: 返回格式为"账号----密码----课程名称"

### 2. 认证机制
- **双重认证**: 需要同时设置Authorization头和Cookie
- **Cookie格式**: `authorized-token={URL编码的JSON}; multiple-tabs=true`
- **Token有效期**: 到2030年10月30日

### 3. 功能支持情况
- ✅ **查课功能**: 完全支持，使用`/api/order`接口
- ✅ **下单功能**: 完全支持，使用`/api/order`接口
- ✅ **订单查询**: 完全支持，使用`/api/getorder`接口
- ✅ **补刷功能**: 通过重新下单实现
- ❌ **暂停功能**: 平台不支持
- ❌ **秒刷功能**: 平台不支持

### 4. lastoid参数说明
- `lastoid`是yyy教育平台的重要参数
- 不同的`lastoid`对应不同的网站或项目
- 在商品配置中，`noun`字段存储`lastoid`值
- 常用值：2864（通用），1683（合肥永君），等

## 注意事项

1. **API地址确认**
   - API基础地址：`http://*************:6966/api`
   - 所有接口都需要完整的认证头和Cookie

2. **数据格式严格要求**
   - 查课和下单的数据格式不同，必须严格按照规范
   - 制表符和空格不能混用

3. **错误处理**
   - 已添加完整的错误处理逻辑
   - 包含登录失败、接口调用失败等情况

4. **安全性**
   - 账号密码存储在数据库中
   - Token自动管理，无需手动维护

## 测试验证

### 运行测试脚本
```bash
# 在浏览器中访问测试脚本
http://你的域名/test/yyy_education_test.php
```

测试脚本会验证以下功能：
1. 登录功能
2. 获取网站列表
3. 查课功能
4. 订单查询功能
5. 系统集成测试

### 手动测试步骤
1. **登录管理后台**
2. **进入货源管理** - 确认yyy教育货源已添加
3. **进入商品管理** - 确认yyy教育商品已创建
4. **测试查课功能** - 使用真实账号密码测试
5. **测试下单功能** - 提交测试订单
6. **查看订单状态** - 确认订单正常处理

## 故障排除

### 常见问题
1. **登录失败 "请重新登录"**
   - 检查Bearer token格式是否正确
   - 确认Cookie设置是否完整
   - 验证HTTP头部是否包含必要字段

2. **查课返回空结果**
   - 确认账号密码格式正确
   - 检查制表符分隔是否正确
   - 验证lastoid参数是否正确

3. **下单失败**
   - 确认使用空格分隔账号、密码、课程名称
   - 检查课程名称是否完整
   - 验证账号密码是否有效

4. **认证失败**
   - 检查Authorization头格式
   - 确认Cookie中的token是否正确编码
   - 验证token是否过期

### 调试技巧
1. **查看原始响应**
   ```php
   echo "API响应: " . $result;
   ```

2. **检查认证头**
   ```php
   var_dump($headers);
   var_dump($cookie);
   ```

3. **验证数据格式**
   ```php
   echo "查课数据: " . $user . "\t" . $pass;
   echo "下单数据: " . $user . " " . $pass . " " . $course;
   ```

### 日志查看
- 系统日志位置：`logs/` 目录
- 数据库日志表：`qingka_wangke_log`
- PHP错误日志：检查服务器错误日志

## 性能优化建议

1. **Token缓存**: 可以缓存access_token减少登录次数
2. **网站列表缓存**: 定期缓存网站列表，减少API调用
3. **批量处理**: 对于大量查课需求，可以考虑批量处理
4. **错误重试**: 增加网络异常重试机制

## 联系支持
如遇到技术问题，请：
1. 首先运行测试脚本进行诊断
2. 查看系统日志和错误信息
3. 联系系统管理员或技术支持
