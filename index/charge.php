<?php
include('head.php');
?>
<div id="uuidindex">
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
           <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">上级资料</div>
        <div class="card-body">
          <form class="site-form">
                <div class="form-group">
                  <label>上级邀请码</label>
                  <input type="text" class="form-control" :value="row.yqm==''?'无':row.yqm" disabled="disabled" />
                </div>
                <div class="form-group">
                  <label>上级剩余积分</label>
                  <input type="text" class="form-control" :value="row.money" disabled="disabled" />
                </div>
                <div class="form-group">
                  <label>上级费率</label>
                  <input type="text" class="form-control" :value="row.addprice" disabled="disabled" />
                </div>
                <div class="form-group">
                  <label>上级代理数量</label>
                  <input type="text" class="form-control" :value="row.dailishu==''?'无':row.dailishu" disabled="disabled" />
                </div>
                <div class="form-group">
                  <label>上级加入时间</label>
                  <input type="text" class="form-control" :value="row.addtime==''?'无':row.addtime" disabled="disabled" />
                </div>
                <div class="form-group">
                  <label>上级最后操作时间</label>
                  <input type="text" class="form-control" :value="row.endtime==''?'无':row.endtime" disabled="disabled" />
                </div>
                
            
							  
          </form>
        </div>
      </div>
    </div>
    
  </div>
  </div>
</div>
<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script type="text/javascript">
    var vm = new Vue({
        el: "#uuidindex",
        data: {
            row: null,
        },
        methods: {
            userinfo: function() {
                var load = layer.load(2);
                axios.post("/apisub.php?act=uuidinfo")
                    .then(response => {
                        layer.close(load);
                        const data = response.data;
                        if (data.code == 1) {
                            this.row = data;
                        } else {
                            layer.alert(data.msg, {
                                icon: 2
                            });
                        }
                    })
                    .catch(error => {
                        layer.close(load);
                        console.error(error);
                        layer.alert('请求失败，请检查网络', {
                            icon: 2
                        });
                    });
            }
        },
        mounted() {
            this.userinfo();
        }
    });
</script>
</body>
</html>