<?php
$title='货源监控';
include('../confing/common.php');
if($userrow['uid']!=1){
setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
exit("<script language='javascript'>window.location.href='login.php';</script>");}
?>
<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
  <title>商品监控同步配置</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/element-plus/2.10.6/index.min.css">
  <style>
body {
  font-family:'Helvetica Neue',Helvetica,'PingFang SC','Hiragino Sans GB','Microsoft YaHei',Arial,sans-serif;
  margin:0;padding:20px;background:#f5f7fa
}
.app-container {
  max-width:100%;margin:0 auto;background:#fff;border-radius:8px;
  box-shadow:0 2px 12px 0 rgba(0,0,0,.1);padding:24px
}
.header {display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid #ebeef5}
.header-title {font-size:20px;font-weight:600;color:#303133}
.config-section {margin-bottom:24px;background:#f9fafc;padding:20px;border-radius:6px}
.section-title {font-size:16px;font-weight:600;color:#606266;margin-bottom:16px;display:flex;align-items:center}
.section-title::before {content:"";display:inline-block;width:4px;height:16px;background:#409eff;margin-right:10px;border-radius:2px}
.switch-group {display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.switch-item {display:flex;align-items:center;justify-content:space-between;padding:8px;background:#fff;border-radius:4px;box-shadow:0 1px 4px rgba(0,0,0,.08)}
.switch-label {font-size:14px;color:#606266}
.input-item {display:flex;align-items:center;margin-bottom:16px}
.input-label {width:120px;font-size:14px;color:#606266}
.action-buttons {text-align:right;margin-top:24px;display:flex;gap:16px;flex-wrap:wrap}
.tips {font-size:12px;color:#909399;margin-top:16px;text-align:center}
.el-divider {margin:24px 0}
.log-card {margin-top:32px}
.card-title {font-size:18px;font-weight:bold;margin-bottom:16px;color:#409eff}
.json-viewer {background:#23272e;color:#eee;font-family:monospace;font-size:13px;line-height:1.7;border-radius:4px;padding:12px;word-break:break-all}
.json-viewer .key { color:#36c6d3 }
.json-viewer .string { color:#b6e876 }
.json-viewer .number { color:#d798e0 }
.json-viewer .boolean { color:#f6cd46 }
.json-viewer .null { color:#f6747a }
.log-remark {color:#999;font-size:12px}
@media (max-width:900px){.app-container{padding:10px}.section-title{font-size:15px}.header-title{font-size:17px}}
@media (max-width:600px){.app-container{padding:2px}.section-title{font-size:14px}.header-title{font-size:15px}.el-table .cell{font-size:12px}}
.rule-dialog .el-dialog {max-width:95vw;min-width:270px !important;border-radius:10px;}
@media (max-width:600px){.rule-dialog .el-dialog {width:98vw!important;min-width:0!important;padding:0;}}
.rule-dialog .el-dialog__body {padding:20px 6px 10px 6px;overflow-x:auto;}
.skip-rule-table{width:100%;max-width:100%;table-layout:fixed;border-collapse:separate;border-spacing:0 5px}
.skip-rule-table th{width:96px;text-align:right;vertical-align:middle;font-weight:400;color:#666;padding:4px 8px}
.skip-rule-table td{padding:4px 6px;}
.skip-rule-table .el-input-number, .skip-rule-table .el-input, .skip-rule-table .el-select{width:98%;}
.prefix-rule-tips{color:#999;font-size:13px;margin-bottom:10px}
.prefix-rule-input-row{display:flex;align-items:center;gap:5px;margin-bottom:6px}
.replace-link{margin:0 3px;color:#bbb}
@media (max-width:600px){.skip-rule-table th{font-size:12px;}.prefix-rule-input-row .el-input{font-size:12px;}}
.rule-dialog .el-dialog__footer{display:flex;justify-content:flex-end;gap:10px;}
@media (max-width:600px){.rule-dialog .el-dialog__footer{justify-content:center;}}
  </style>
</head>
<body>
<div id="app"></div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vue/3.2.47/vue.global.prod.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/element-plus/2.10.6/index.full.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.7.7/axios.min.js"></script>
<script>
const { reactive, createApp, ref, onMounted, computed, watch, nextTick } = Vue;

createApp({
setup() {
const huoyuans = ref([]);
const config = reactive({
  tskg: 0, sdurl: '', zmsjkg: 0, tbjgkg: 0, tbsmkg: 0, tbsjmc: 0,
  tbztkg: 0, qzdqjg: 0, klsjkg: 0, ltxgjg: 300, hyid: '', qzkg: 0, tgkg: 0
});
const loading=ref(false),saveLoading=ref(false);
const hyListenMap=reactive({}),jgbsMap=reactive({}),
      tggzMap=reactive({}),qzgzMap=reactive({});
const categoryPriceMap = reactive({});
const catPriceDialogVisible = ref(false);
const catPriceDialogHid = ref('');
const catPriceDialogName = ref('');
const catPriceDialogData = ref([]);

// 改造：跳过规则弹窗相关
const skipRuleDialogVisible = ref(false);
const skipRuleDialogHid = ref('');
const skipRuleDialogName = ref('');
const skipRuleDialogTemp = reactive({
  cidListStr:'', cidRangeStart:'', cidRangeEnd:'', fenleiList:[]
});

// 改造：前缀规则弹窗相关
const prefixRuleDialogVisible = ref(false);
const prefixRuleDialogHid = ref('');
const prefixRuleDialogName = ref('');
const prefixRuleDialogTemp = ref([{from:'',to:''}]);

const logLoading=ref(false),logDialog=ref(false),
      logDialogTitle=ref(''),logDialogContent=ref('');
const allLogsRaw=ref([]),logPage=ref(1),logPageSize=ref(15),logTotal=ref(0);
const searchField=ref(''),searchKeyword=ref('');
const searchFieldOptions=[
  {label:'全部',value:''},{label:'货源名称',value:'huoyuan_name'},
  {label:'分类名称',value:'fenlei_name'},
  {label:'商品名称',value:'platform_name'},
  {label:'动作',value:'action'},
  {label:'变更前内容',value:'data_before'},
  {label:'变更后内容',value:'data_after'}
];

const showdocTestLoading = ref(false);

// 前缀替换执行按钮 loading
const prefixExecLoading = ref(false);
function executePrefixReplace() {
  prefixExecLoading.value = true;
  axios.post('/jkjg/jkjg.php?act=execute_prefix_replace', {})
    .then(res => {
      if(res.data.code === 1) {
        ElementPlus.ElMessage.success(res.data.msg || '前缀替换执行完成');
        fetchLogs();
      } else {
        ElementPlus.ElMessage.error(res.data.msg || '执行失败');
      }
    })
    .catch(()=>ElementPlus.ElMessage.error('请求出错'))
    .finally(()=>{ prefixExecLoading.value = false; });
}

function testShowdocUrl(){
  if(!config.sdurl){
    ElementPlus.ElMessage.warning('请输入Showdoc推送地址');
    return;
  }
  showdocTestLoading.value = true;
  axios.post('/jkjg/jkjg.php?act=test_showdoc_push', {
    url: config.sdurl
  }).then(res=>{
    if(res.data.code===1){
      ElementPlus.ElMessage.success('推送成功，请到Showdoc查看通知');
    }else{
      ElementPlus.ElMessage.error(res.data.msg||'推送失败');
    }
  }).catch(()=>{
    ElementPlus.ElMessage.error('请求出错');
  }).finally(()=>{
    showdocTestLoading.value = false;
  });
}

function getDialogWidth(){
  const w=window.innerWidth;
  if(w<430)return'96vw';
  if(w<800)return'88vw';
  if(w<1000)return'600px';
  return'440px';
}

function fetchData(){
  loading.value=true;
  axios.get('/jkjg/jkjg.php?act=get_huoyuan_with_fenlei').then(res=>{
    if(res.data.code!==1){ElementPlus.ElMessage.error(res.data.msg||'获取数据失败');huoyuans.value=[]}
    else{huoyuans.value=res.data.data||[]}
  }).then(()=>axios.get('/jkjg/jkjg.php?act=get_config'))
    .then(res=>{
      if(res?.data?.code===1&&res.data.data){
        config.tskg = +res.data.data.tskg||0;
        config.sdurl = res.data.data.sdurl||'';
        config.zmsjkg = +res.data.data.zmsjkg||0;
        config.tbjgkg = +res.data.data.tbjgkg||0;
        config.tbsmkg = +res.data.data.tbsmkg||0;
        config.tbsjmc = +res.data.data.tbsjmc||0;
        config.tbztkg = +res.data.data.tbztkg||0;
        config.qzdqjg = +res.data.data.qzdqjg||0;
        config.klsjkg = +res.data.data.klsjkg||0;
        config.ltxgjg = +res.data.data.ltxgjg||300;
        config.hyid = res.data.data.hyid||'';
        config.qzkg = +res.data.data.qzkg||0;
        config.tgkg = +res.data.data.tgkg||0;

        const enabled=(config.hyid||'').split(',').filter(Boolean);
        Object.keys(hyListenMap).forEach(k=>delete hyListenMap[k]);
        huoyuans.value.forEach(h=>hyListenMap[h.hid]=enabled.includes(String(h.hid))?1:0);

        const rateObj=res.data.data.huoyuan_price_rate?JSON.parse(res.data.data.huoyuan_price_rate):{};
        Object.keys(jgbsMap).forEach(k=>delete jgbsMap[k]);
        huoyuans.value.forEach(h=>jgbsMap[h.hid]=rateObj[h.hid]!==undefined?rateObj[h.hid]:5);

        // 分类价格配置
        const categoryRateObj = res.data.data.huoyuan_category_price?JSON.parse(res.data.data.huoyuan_category_price):{};
        Object.keys(categoryPriceMap).forEach(k=>delete categoryPriceMap[k]);
        huoyuans.value.forEach(h=>{
          categoryPriceMap[h.hid]=categoryRateObj[h.hid] ? {...categoryRateObj[h.hid]} : {};
        });

        const tgObj=res.data.data.skip_rule?JSON.parse(res.data.data.skip_rule):{};
        Object.keys(tggzMap).forEach(k=>delete tggzMap[k]);
        huoyuans.value.forEach(h=>tggzMap[h.hid]=tgObj[h.hid]||{cidList:[],cidRange:[],fenleiList:[]});

        const qzObj=res.data.data.replace_rule?JSON.parse(res.data.data.replace_rule):{};
        Object.keys(qzgzMap).forEach(k=>delete qzgzMap[k]);
        huoyuans.value.forEach(h=>qzgzMap[h.hid]=Array.isArray(qzObj[h.hid])?qzObj[h.hid]:[]);
      }
    }).finally(()=>loading.value=false);
}

// 分类价格弹窗
function openCatPriceDialog(row) {
  catPriceDialogHid.value = row.hid;
  catPriceDialogName.value = row.name;
  const map = categoryPriceMap[row.hid] || {};
  catPriceDialogData.value = (row.fenlei_list || []).map(f => ({
    fenlei_id: f.fenlei_id,
    fenlei_name: f.fenlei_name,
    rate: map[f.fenlei_id] !== undefined ? map[f.fenlei_id] : (jgbsMap[row.hid] || 1)
  }));
  catPriceDialogVisible.value = true;
}
function saveCatPriceDialog() {
  const hid = catPriceDialogHid.value;
  const list = catPriceDialogData.value;
  const obj = {};
  list.forEach(item => {
    const mainRate = jgbsMap[hid] || 1;
    if (Number(item.rate) !== Number(mainRate)) {
      obj[item.fenlei_id] = Number(item.rate) || 1;
    }
  });
  categoryPriceMap[hid] = obj;
  catPriceDialogVisible.value = false;
}
function hasCategoryCustomPrice(row) {
  const map = categoryPriceMap[row.hid] || {};
  const main = Number(jgbsMap[row.hid] || 1);
  const catArr = (row.fenlei_list || []);
  for (let i=0;i<catArr.length;i++) {
    const f=catArr[i], v=map[f.fenlei_id];
    if (v !== undefined && Number(v)!==main) return true;
  }
  return false;
}

// 跳过规则弹窗
function openSkipRuleDialog(hid) {
  skipRuleDialogHid.value = hid;
  const row = huoyuans.value.find(r=>r.hid==hid);
  skipRuleDialogName.value = row ? row.name : hid;
  // 跳过规则
  const r = tggzMap[hid] || {cidList:[],cidRange:[],fenleiList:[]};
  skipRuleDialogTemp.cidListStr = (r.cidList||[]).join(',');
  skipRuleDialogTemp.cidRangeStart = r.cidRange?.[0] || '';
  skipRuleDialogTemp.cidRangeEnd = r.cidRange?.[1] || '';
  skipRuleDialogTemp.fenleiList = (r.fenleiList || []).map(String);
  skipRuleDialogVisible.value = true;
}
function saveSkipRuleDialog() {
  const hid = skipRuleDialogHid.value;
  const list = (skipRuleDialogTemp.cidListStr || '').split(',')
    .map(s=>s.trim()).filter(v=>v).map(Number).filter(n=>!isNaN(n));
  const start = parseInt(skipRuleDialogTemp.cidRangeStart), end = parseInt(skipRuleDialogTemp.cidRangeEnd);
  const fenlei = skipRuleDialogTemp.fenleiList.map(Number).filter(n=>!isNaN(n));
  tggzMap[hid] = {
    cidList: list,
    cidRange: (!isNaN(start)&&!isNaN(end)&&start<=end)?[start,end]:[],
    fenleiList: fenlei
  };
  skipRuleDialogVisible.value = false;
}
function closeSkipRuleDialog() {
  skipRuleDialogVisible.value = false;
}

// 前缀规则弹窗
function openPrefixRuleDialog(hid) {
  prefixRuleDialogHid.value = hid;
  const row = huoyuans.value.find(r=>r.hid==hid);
  prefixRuleDialogName.value = row ? row.name : hid;
  // 前缀规则
  prefixRuleDialogTemp.value = qzgzMap[hid]?.length ? qzgzMap[hid].map(r=>({...r})) : [{from:'',to:''}];
  prefixRuleDialogVisible.value = true;
}
function addPrefixRuleRow() {
  prefixRuleDialogTemp.value.push({from:'',to:''});
}
function removePrefixRuleRow(idx) {
  if(prefixRuleDialogTemp.value.length === 1) prefixRuleDialogTemp.value = [{from:'',to:''}];
  else prefixRuleDialogTemp.value.splice(idx,1);
}
function savePrefixRuleDialog() {
  const hid = prefixRuleDialogHid.value;
  qzgzMap[hid] = prefixRuleDialogTemp.value.filter(r=>r.from||r.to);
  prefixRuleDialogVisible.value = false;
}
function closePrefixRuleDialog() {
  prefixRuleDialogVisible.value = false;
}

function onClearAllConfig(){
  axios.post('/jkjg/jkjg.php?act=clear_all_config').then(res=>{
    if(res.data.code==1){
      ElementPlus.ElMessage.success('已清空所有配置！');fetchData();
    }else{
      ElementPlus.ElMessage.error(res.data.msg||'清空失败');
    }
  });
}

function saveConf(){
  const allOff=(
    !config.tskg && !config.tbjgkg && !config.tbsmkg &&
    !config.tbsjmc && !config.tbztkg && !config.qzdqjg &&
    !config.klsjkg && !config.qzkg && !config.tgkg
  );
  saveConfigDo();
}

function buildSaveJSON(){
  const enabled=huoyuans.value.filter(h=>hyListenMap[h.hid]==1).map(h=>h.hid);
  const priceRate={},skip_rule={},replace_rule={},listen={},categoryRate={};
  enabled.forEach(hid=>{
    const rate=Number(jgbsMap[hid]);priceRate[hid]=!isNaN(rate)&&rate>0?rate:1;
    skip_rule[hid]=tggzMap[hid]||{cidList:[],cidRange:[],fenleiList:[]};
    replace_rule[hid]=qzgzMap[hid]?.filter(r=>r.from||r.to)||[];
    categoryRate[hid]=categoryPriceMap[hid]||{};
  });
  huoyuans.value.forEach(h=>listen[h.hid]=hyListenMap[h.hid]||0);
  return{
    hyid: enabled.join(','),
    huoyuan_price_rate: JSON.stringify(priceRate),
    skip_rule: JSON.stringify(skip_rule),
    replace_rule: JSON.stringify(replace_rule),
    huoyuan_listen: JSON.stringify(listen),
    huoyuan_category_price: JSON.stringify(categoryRate),
    tskg: config.tskg,
    sdurl: config.sdurl,
    zmsjkg: config.zmsjkg,
    tbjgkg: config.tbjgkg,
    tbsmkg: config.tbsmkg,
    tbsjmc: config.tbsjmc,
    tbztkg: config.tbztkg,
    qzdqjg: config.qzdqjg,
    klsjkg: config.klsjkg,
    ltxgjg: config.ltxgjg,
    qzkg: config.qzkg,
    tgkg: config.tgkg
  };
}
function saveConfigDo(){
  saveLoading.value=true;
  axios.post('/jkjg/jkjg.php?act=save_config',buildSaveJSON()).then(res=>{
    if(res.data.code==1){ElementPlus.ElMessage.success('配置保存成功');fetchData()}
    else{ElementPlus.ElMessage.error(res.data.msg||'保存失败')}
  }).catch(()=>ElementPlus.ElMessage.error('保存出错')).finally(()=>saveLoading.value=false);
}

function fetchLogs(){
  logLoading.value=true;
  axios.get('/jkjg/jkjg.php?act=get_logs').then(res=>{
    allLogsRaw.value=res.data.code===1?res.data.data||[]:[];
    logPage.value=1;
  }).finally(()=>logLoading.value=false);
}
const logsPaged=computed(()=>{
  let arr=[...allLogsRaw.value];
  if(searchKeyword.value){
    const kw=searchKeyword.value.toLowerCase();
    if(searchField.value){
      arr=arr.filter(r=>String(r[searchField.value]||'').toLowerCase().includes(kw));
    }else{
      arr=arr.filter(r=>['huoyuan_name','fenlei_name','platform_name','action','data_before','data_after']
        .some(k=>String(r[k]||'').toLowerCase().includes(kw)));
    }
  }
  logTotal.value=arr.length;
  const start=(logPage.value-1)*logPageSize.value;
  return arr.slice(start,start+logPageSize.value);
});
function onLogPageChange(p){logPage.value=p}
function onLogPageSizeChange(ps){logPageSize.value=ps;logPage.value=1}
function syntaxHighlight(json){
  if(!json)return'';
  if(typeof json!=='string')json=JSON.stringify(json,null,2);
  return json.replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;')
    .replace(/("(\\u[\da-fA-F]{4}|\\[^u]|[^\\"])*"(?:\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
      match=>{
        let cls='number';
        if(/^"/.test(match)){cls=/:$/.test(match)?'key':'string'}
        else if(/true|false/.test(match))cls='boolean';
        else if(/null/.test(match))cls='null';
        return`<span class="${cls}">${match}</span>`;
      });
}
function openLogDialog(t,c){logDialogTitle.value=t;logDialogContent.value=c;logDialog.value=true}
function getFenleiListForHid(hid){
  const h=huoyuans.value.find(r=>r.hid==hid);
  return h?(h.fenlei_list||[]).map(f=>({value:String(f.fenlei_id),label:f.fenlei_name})):[];
}
function getFenleiNameById(hid,id){
  const list=getFenleiListForHid(hid);
  const f=list.find(i=>i.value==id);
  return f?f.label:id;
}
onMounted(()=>{fetchData();fetchLogs()});
watch([searchField,searchKeyword],()=>logPage.value=1);

return{
  config,huoyuans,loading,saveLoading,hyListenMap,jgbsMap,tggzMap,qzgzMap,
  catPriceDialogVisible, catPriceDialogHid, catPriceDialogName, catPriceDialogData, openCatPriceDialog, saveCatPriceDialog, hasCategoryCustomPrice,
  categoryPriceMap,
  saveConf,getDialogWidth,logLoading,logDialog,logDialogTitle,logDialogContent,
  logsPaged,logPage,logPageSize,logTotal,searchField,searchKeyword,searchFieldOptions,onLogPageChange,onLogPageSizeChange,syntaxHighlight,openLogDialog,
  getFenleiListForHid,getFenleiNameById,onClearAllConfig,
  showdocTestLoading, testShowdocUrl,
  prefixExecLoading, executePrefixReplace,
  // 新增
  skipRuleDialogVisible, skipRuleDialogHid, skipRuleDialogName, skipRuleDialogTemp,
  openSkipRuleDialog, saveSkipRuleDialog, closeSkipRuleDialog,
  prefixRuleDialogVisible, prefixRuleDialogHid, prefixRuleDialogName, prefixRuleDialogTemp,
  openPrefixRuleDialog, savePrefixRuleDialog, closePrefixRuleDialog, addPrefixRuleRow, removePrefixRuleRow,
};
},
template:`
<div>
  <div class="app-container">
    <div class="header"><div class="header-title">货源监控</div></div>
    <div class="config-section">
      <div class="section-title">全局同步参数</div>
      <div class="switch-group">
        <div class="switch-item"><span class="switch-label">推送变更</span>
          <el-switch v-model="config.tskg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">下架180天删除开关</span>
          <el-switch v-model="config.zmsjkg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">同步价格开关</span>
          <el-switch v-model="config.tbjgkg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">同步说明</span>
          <el-switch v-model="config.tbsmkg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">同步商品名称</span>
          <el-switch v-model="config.tbsjmc" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">同步上下架</span>
          <el-switch v-model="config.tbztkg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">奸商模式</span>
          <el-switch v-model="config.qzdqjg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item"><span class="switch-label">克隆上架开关</span>
          <el-switch v-model="config.klsjkg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item">
          <span class="switch-label">启用前缀替换</span>
          <el-switch v-model="config.qzkg" :active-value="1" :inactive-value="0" />
        </div>
        <div class="switch-item">
          <span class="switch-label">替换前缀</span>
          <el-button
            type="warning"
            :loading="prefixExecLoading"
            @click="executePrefixReplace"
            size="small"
            style="width:50px"
          >执行</el-button>
        </div>
        <div class="switch-item"><span class="switch-label">启用跳过规则</span>
          <el-switch v-model="config.tgkg" :active-value="1" :inactive-value="0" />
        </div>
      </div>
      <div class="input-item" style="margin-top:16px">
        <span class="input-label">监听轮询间隔</span>
        <el-input-number v-model="config.ltxgjg" :step="10" :controls="true"/>
        <span style="margin-left:8px;font-size:13px;color:#909399">秒</span>
      </div>
    </div>
    <el-divider />
    <div class="section-title">货源配置</div>
    <el-table :data="huoyuans" border size="medium" row-key="hid" v-loading="loading">
      <el-table-column label="货源ID" prop="hid" width="80" align="center" />
      <el-table-column label="货源名称" prop="name" min-width="60" align="center" />
      <el-table-column label="监听" width="90" align="center">
        <template #default="{row}">
          <el-switch v-model="hyListenMap[row.hid]" :active-value="1" :inactive-value="0"/>
        </template>
      </el-table-column>
      <el-table-column label="价格倍数" width="160" align="center">
        <template #default="{row}">
          <el-input-number v-model="jgbsMap[row.hid]" :step="0.01" :controls="true"
            :disabled="hyListenMap[row.hid]!=1" style="width:120px"/>
        </template>
      </el-table-column>
      <el-table-column label="分类价格" width="110" align="center">
        <template #default="{row}">
          <el-button size="small"
            :type="hasCategoryCustomPrice(row)?'danger':'info'"
            plain
            @click.stop="openCatPriceDialog(row)">
            分类价格
          </el-button>
        </template>
      </el-table-column>
      <!-- 变更部分：两列规则弹窗 -->
      <el-table-column label="跳过规则" width="100" align="center">
        <template #default="{row}">
          <el-button
            size="small"
            type="primary"
            plain
            @click.stop="openSkipRuleDialog(row.hid)"
            :disabled="hyListenMap[row.hid]!=1"
          >跳过规则</el-button>
        </template>
      </el-table-column>
      <el-table-column label="前缀规则" width="100" align="center">
        <template #default="{row}">
          <el-button
            size="small"
            type="warning"
            plain
            @click.stop="openPrefixRuleDialog(row.hid)"
            :disabled="hyListenMap[row.hid]!=1"
          >前缀规则</el-button>
        </template>
      </el-table-column>
      <!-- 原有“跳过分类”和“本地分类”不变 -->
      <el-table-column label="跳过分类" width="120" align="center">
        <template #default="{row}">
          <el-popover placement="bottom" width="220" trigger="click">
            <template #reference><el-button size="small" type="info">查看</el-button></template>
            <div style="max-height:180px;overflow:auto">
              <div v-if="(tggzMap[row.hid]?.fenleiList||[]).length">
                <div v-for="fid in tggzMap[row.hid].fenleiList" :key="fid">{{getFenleiNameById(row.hid,fid)}}</div>
              </div><div v-else style="color:#bbb">暂无跳过分类</div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="本地分类" width="120" align="center">
        <template #default="{row}">
          <el-popover placement="bottom" width="220" trigger="click">
            <template #reference><el-button size="small" type="info">查看</el-button></template>
            <div style="max-height:180px;overflow:auto">
              <div v-if="(row.fenlei_list||[]).length">
                <div v-for="f in row.fenlei_list" :key="f.fenlei_id">{{f.fenlei_name}}</div>
              </div><div v-else style="color:#bbb">无</div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <div class="action-buttons">
      <el-button type="primary" size="large" :loading="saveLoading" @click="saveConf">保存全部配置</el-button>
      <el-button type="danger" size="large" plain @click="onClearAllConfig">一键清空所有配置</el-button>
    </div>
    <div class="tips">每个货源配置可单独控制监听与参数，监听开关关闭则不会上传配置到同步。</div>
  </div>
  <!-- 分类价格配置弹窗 -->
  <el-dialog
    class="rule-dialog"
    v-model="catPriceDialogVisible"
    :width="getDialogWidth()"
    :title="catPriceDialogName + ' 分类价格配置'"
    append-to-body
    destroy-on-close
    :lock-scroll="false"
  >
    <table class="skip-rule-table">
      <tr v-for="row in catPriceDialogData" :key="row.fenlei_id">
        <th>{{row.fenlei_name}}</th>
        <td>
          <el-input-number v-model="row.rate" :step="0.01" :controls="true" style="width:150px"/>
        </td>
      </tr>
    </table>
    <template #footer>
      <el-button @click="catPriceDialogVisible=false">取消</el-button>
      <el-button type="primary" @click="saveCatPriceDialog">保存</el-button>
    </template>
  </el-dialog>
  <!-- 跳过规则弹窗 -->
  <el-dialog
    class="rule-dialog"
    v-model="skipRuleDialogVisible"
    :width="getDialogWidth()"
    :title="skipRuleDialogName + ' 跳过规则配置'"
    append-to-body
    destroy-on-close
    :lock-scroll="false"
  >
    <div class="section-title" style="margin-top:0">跳过规则</div>
    <table class="skip-rule-table">
      <tr>
        <th>跳过cid</th>
        <td>
          <el-input
            v-model="skipRuleDialogTemp.cidListStr"
            placeholder="如 101,105,110 (逗号分隔)"
            clearable
          />
        </td>
      </tr>
      <tr>
        <th>跳过cid区间</th>
        <td style="display:flex;align-items:center;gap:4px;">
          <el-input-number v-model="skipRuleDialogTemp.cidRangeStart" size="small" :controls="true" style="width:90px" />
          <span style="color:#aaa;font-size:14px;padding:0 4px;">~</span>
          <el-input-number v-model="skipRuleDialogTemp.cidRangeEnd" size="small" :controls="true" style="width:90px" />
          <span style="color:#bbb;font-size:12px;margin-left:6px;">(可留空)</span>
        </td>
      </tr>
      <tr>
        <th>跳过本地分类</th>
        <td>
          <el-select
            v-model="skipRuleDialogTemp.fenleiList"
            multiple
            clearable
            size="small"
            style="width:99%"
            placeholder="选择分类"
          >
            <el-option
              v-for="item in getFenleiListForHid(skipRuleDialogHid)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </td>
      </tr>
    </table>
    <template #footer>
      <el-button @click="closeSkipRuleDialog">取消</el-button>
      <el-button type="primary" @click="saveSkipRuleDialog">保存</el-button>
    </template>
  </el-dialog>
  <!-- 前缀规则弹窗 -->
  <el-dialog
    class="rule-dialog"
    v-model="prefixRuleDialogVisible"
    :width="getDialogWidth()"
    :title="prefixRuleDialogName + ' 前缀规则配置'"
    append-to-body
    destroy-on-close
    :lock-scroll="false"
  >
    <div class="section-title" style="margin-top:0">前缀规则</div>
    <div class="prefix-rule-tips">
      例如：上游名称如“【上游】xxx”，需批量替换为“【本地】xxx”，请填写 <b>【上游】 —— 【本地】</b>
    </div>
    <div>
      <div class="prefix-rule-input-row" v-for="(row,idx) in prefixRuleDialogTemp" :key="idx">
        <el-input v-model="row.from" placeholder="上游前缀" size="small" clearable />
        <span class="replace-link">——</span>
        <el-input v-model="row.to" placeholder="替换为(可留空)" size="small" clearable />
        <el-button icon="el-icon-delete" size="small" type="danger" circle @click="removePrefixRuleRow(idx)" v-if="prefixRuleDialogTemp.length>1"/>
      </div>
      <el-button size="small" icon="el-icon-plus" type="primary" @click="addPrefixRuleRow" style="margin-top:4px">新增规则</el-button>
    </div>
    <template #footer>
      <el-button @click="closePrefixRuleDialog">取消</el-button>
      <el-button type="primary" @click="savePrefixRuleDialog">保存</el-button>
    </template>
  </el-dialog>
<!-- 日志 -->
<div class="log-card">
  <el-card>
    <div class="card-title">变更日志</div>
    <div style="display:flex;gap:12px;align-items:center;flex-wrap:wrap;margin-bottom:10px">
      <el-select v-model="searchField" placeholder="选择字段" clearable style="width:130px">
        <el-option v-for="opt in searchFieldOptions" :key="opt.value" :label="opt.label" :value="opt.value"/>
      </el-select>
      <el-input v-model="searchKeyword" placeholder="输入内容搜索" clearable style="width:220px" @keyup.enter.native="logPage=1">
        <template #append><el-button icon="el-icon-search" @click="logPage=1">搜索</el-button></template>
      </el-input>
    </div>
    <el-table :data="logsPaged" border size="small" v-loading="logLoading">
      <el-table-column label="ID" prop="id" width="60" align="center"/>
      <el-table-column label="商品ID" prop="product_id" width="80" align="center"/>
      <el-table-column label="货源名称" prop="huoyuan_name" min-width="120" align="center"/>
      <el-table-column label="分类名称" prop="fenlei_name" min-width="120" align="center"/>
      <el-table-column label="商品名称" prop="platform_name" min-width="120" align="center"/>
      <el-table-column label="动作" prop="action" width="80" align="center"/>
      <el-table-column label="变更前" prop="data_before" min-width="100" align="center" show-overflow-tooltip />
      <el-table-column label="变更后" prop="data_after" min-width="100" align="center" show-overflow-tooltip />
      <el-table-column label="时间" prop="op_time" min-width="150" align="center"/>
      <el-table-column label="备注" width="90" align="center">
        <template #default="{row}">
          <el-button type="primary" link size="small" @click.stop="openLogDialog('备注',row.remark)">查看</el-button>
        </template>
      </el-table-column>
<el-table-column label="推送状态" prop="push_status" width="90" align="center">
  <template #default="{row}">
    <el-tag v-if="row.push_status == 1" type="success" size="small">成功</el-tag>
    <el-tag v-else-if="row.push_status == 0" type="danger" size="small">失败</el-tag>
    <el-tag v-else-if="row.push_status == 2" type="warning" size="small">跳过</el-tag>
    <el-tag v-else-if="row.push_status == 3" type="info" size="small">手动</el-tag>
    <el-tag v-else type="default" size="small">未知</el-tag>
  </template>
</el-table-column>
      <el-table-column label="操作人" prop="operator" min-width="100" align="center"/>
    </el-table>
    <div style="margin-top:14px;text-align:right">
      <el-pagination background layout="prev, pager, next, jumper, ->, total, sizes"
        :total="logTotal" :page-size="logPageSize" :current-page="logPage"
        @current-change="onLogPageChange" @size-change="onLogPageSizeChange"
        :page-sizes="[10,15,20,30,50]"/>
    </div>
  </el-card>
  <el-dialog v-model="logDialog" :title="logDialogTitle" width="350px" append-to-body :lock-scroll="false">
    <div class="json-viewer" v-html="syntaxHighlight(logDialogContent)"></div>
  </el-dialog>
</div>
</div>
`
}).use(ElementPlus).mount('#app');
</script>
</body>
</html>