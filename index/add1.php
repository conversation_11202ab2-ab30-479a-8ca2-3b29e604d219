<?php
$title='批量查询';
require_once('head.php');
$addsalt=md5(mt_rand(300,999).time());
$_SESSION['addsalt']=$addsalt;
?>
<link rel="stylesheet" href="../assets/yqsladmin/css/add.css" type="text/css" />   
     
<div class="app-content-body">
    <div class="wrapper-md control" id="add">
        <div class="panel panel-default" id="addorder" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px; ">创建订单</div>
            <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
                <div class="layui-col-md12">
                        <form class="form-horizontal devform">
                            <div id="add">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">选择项目</label>
                                    <div class="col-sm-9">
                                        <form class="form-horizontal" id="form-update">
                                            <template>
                                                <el-select id="select" v-model="cid" @change="tips(cid)" filterable placeholder="请先选择分类再搜索项目下单" style="background: url('../index/arrow.png') no-repeat scroll 99%; width:95%; margin-left: 13px;">
                                                    <el-option v-for="class2 in class1" :key="class2.cid" :label="class2.name + '→' + class2.price + '积分'" :value="class2.cid">
                                                        <div class="el-option-content">
                                                            <span class="project-name">{{ class2.name }}</span>
                                                            <span class="project-price">{{ class2.price }}积分</span>
                                                        </div>
                                                    </el-option>
                                                </el-select>
                                            </template>
                                        </form>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">渠道分类</label>
                                    <div class="col-sm-9">
                                        <div class="col-xs-12">
                                            <div class="example-box">
                                                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary channel-category" @click="fenlei('')">全部分类</button>
                                                <?php
                                                $a = $DB->query("select * from qingka_wangke_fenlei where status=1 ORDER BY `sort` DESC");
                                                while($rs = $DB->fetch($a)){
                                                ?>
                                                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary channel-category" @mouseover="hoverButton(<?=$rs['id']?>)" @mouseout="unhoverButton(<?=$rs['id']?>)" @click="fenlei(<?=$rs['id']?>)" :class="{'layui-btn-disabled': selectedButton === <?=$rs['id']?>}"><?=$rs['name']?></button>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-show="show">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">分类介绍</label>
                                        <div class="col-sm-9">
                                            <div class="col-xs-12">
                                                <span class="help-block m-b-none" style="color:blue;" id="warning">
                                                    <span v-html="categoryIntro"></span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group" id="score" style="display: none;">
                                        <label class="col-sm-2 control-label">参数设置</label>
                                        <div class="col-sm-9 col-xs-8">
                                            <input id="range_02">
                                            <small class="form-text text-muted" v-html="score_text"></small>
                                        </div>
                                    </div>
                                    <div class="form-group" id="shic" style="display: none;">
                                        <label class="col-sm-2 control-label">参数设置</label>
                                        <div class="col-sm-9 col-xs-8">
                                            <input id="range_01">
                                            <small class="form-text text-muted" v-html="shichang_text"></small>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">信息填写</label>
                                        <div class="col-sm-9">
                                            <button type="button" @click="saveclass" style="border-radius: 8px;" class="layui-btn layui-btn-sm layui-btn-danger">收藏项目</button>
                                            <button type="button" @click="dellclass" style="border-radius: 8px;" class="layui-btn layui-btn-sm layui-btn-danger">移除收藏</button>
                                            <button type="button" @click="shareclass" style="border-radius: 8px;" class="layui-btn layui-btn-sm layui-btn-normal">分享项目</button><br>
                                            <textarea rows="5" class="layui-textarea" v-model="userinfo" placeholder="信息填写方式：&#10账号 密码（中间用空格分隔）&#10学校 账号 密码 （中间用空格分隔）&#10多账号下单必须换行，务必一行一条信息" style="border-radius: 8px;"></textarea>
                                            <span class="help-block m-b-none" style="color:red;" id="warning">
                                                <span v-html="content"></span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="layui-row layui-col-space15">
                                        <div class="col-sm-offset-2 col-sm-12">
                                            <button type="button" @click="get" style="border-radius: 13px;" class="layui-btn layui-btn-sm"> 经典查询</button>
                                            <button type="button" @click="add" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-normal"> 提交课程 </button>
                                            <button type="button" @click="checkUserinfo" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-danger" title="智能校正：自动修复多余空格、密码中的空格、中文符号等问题"> 🔧 检查数据</button>
                                            <button type="reset" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-primary"> 重置面板</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="layui-col-md12" v-show="show1">
                    <div class="panel panel-default" style="border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div class="panel-heading font-bold bg-white" style="border-radius: 12px 12px 0 0; padding: 15px 20px; background: linear-gradient(to right, #f8f9fa, #ffffff);">
                            查询结果
                        </div>
                        <div class="panel-body" style="padding: 20px;">
                            <form class="form-horizontal devform">
                                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                    <div v-for="(rs, key) in row">
                                        <div class="panel panel-default" style="border: none; margin-bottom: 15px; border-radius: 8px; overflow: hidden;">
                                            <div class="panel-heading" role="tab" id="headingOne" style="background: #f8f9fa; padding: 15px;">
                                                <h4 class="panel-title" style="display: flex; align-items: center; gap: 10px;">
                                                    <a role="button" data-toggle="collapse" data-parent="#accordion" :href="'#' + key" aria-expanded="true" style="flex: 1; text-decoration: none; color: #333;">
                                                        <b>{{ rs.userName }}</b> {{ rs.userinfo }}
                                                        <span v-if="rs.msg == '查询成功'"><b style="color: #28a745;">{{ rs.msg }}</b></span>
                                                        <span v-else-if="rs.msg != '成功'"><b style="color: #dc3545;">{{ rs.msg }}</b></span>
                                                    </a>
                                                    <button @click.prevent="selectAll(rs.userinfo, rs.userName, rs.data, key)" class="layui-btn layui-btn-sm layui-btn-primary layui-border-orange" style="border-radius: 6px; padding: 6px 12px; font-size: 14px;">
                                                        课程全选
                                                    </button>
                                                    <button type="button" @click="copyQueryInfo" class="layui-btn layui-btn-sm layui-btn-primary layui-border-red" style="border-radius: 6px; padding: 6px 12px; font-size: 14px;">
                                                        复制课程
                                                    </button>
                                                </h4>
                                            </div>
                                            <div :id="key" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">
                                                <div class="panel-body" style="padding: 15px;">
                                                    <div v-for="(res, key) in rs.data" class="resource-item" style="display: flex; align-items: center; margin-bottom: 10px;">
                                                        <label style="flex: 1; display: flex; flex-direction: column; padding: 12px; border: 1px solid #e0e0e0; border-radius: 6px; background: #ffffff; cursor: pointer; transition: all 0.3s; margin: 0;">
                                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                                <input type="checkbox" :value="res.name" :checked="isChecked(rs.userinfo, res.name)" @click="checkResources(rs.userinfo, rs.userName, rs.data, res.id, res.name)" style="margin-right: 8px;">
                                                                <span style="flex: 1; width: 100%;">
                                                                    {{ res.name }}
                                                                </span>
                                                                <img v-if="res.img" :src="res.img" alt="图片" referrerpolicy="no-referrer" style="width: 100px; height: 75px; object-fit: cover; border-radius: 4px;" />
                                                            </div>
                                                            <span style="margin-top: 6px; max-width: 100%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #666; font-size: 0.85em;">
                                                                {{ res.id }}
                                                            </span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script>
var vm = new Vue({
    el: "#add",
    data: {
        row: [],
        check_row: [],
        userinfo: '',
        cid: '',
        id: '',
        score_text: '',
        shichang_text: '',
        class1: '',
        class3: '',
        show: false,
        show1: false,
        content: '',
        categoryIntro: '',
        selectedButton: null
    },
    methods: {
        // 智能数据校正功能
        smartCorrectUserinfo: function(text) {
            // 中文符号到英文符号的映射表
            var symbolMap = {
                '，': ',', '。': '.', '；': ';', '：': ':', '？': '?', '！': '!',
                '"': '"', '"': '"', ''': "'", ''': "'", '（': '(', '）': ')',
                '【': '[', '】': ']', '｛': '{', '｝': '}', '《': '<', '》': '>',
                '～': '~', '｀': '`', '＠': '@', '＃': '#', '￥': '$', '％': '%',
                '＾': '^', '＆': '&', '＊': '*', '－': '-', '＿': '_', '＋': '+',
                '＝': '=', '｜': '|', '＼': '\\', '／': '/', '　': ' '
            };

            // 全角数字和字母到半角的映射
            var fullWidthMap = {};
            // 全角数字 0-9
            for (var i = 0; i <= 9; i++) {
                fullWidthMap[String.fromCharCode(0xFF10 + i)] = i.toString();
            }
            // 全角大写字母 A-Z
            for (var i = 0; i < 26; i++) {
                fullWidthMap[String.fromCharCode(0xFF21 + i)] = String.fromCharCode(0x41 + i);
            }
            // 全角小写字母 a-z
            for (var i = 0; i < 26; i++) {
                fullWidthMap[String.fromCharCode(0xFF41 + i)] = String.fromCharCode(0x61 + i);
            }

            // 合并映射表
            var allMap = Object.assign({}, symbolMap, fullWidthMap);

            // 执行字符替换
            var corrected = text;
            for (var key in allMap) {
                corrected = corrected.replace(new RegExp(key, 'g'), allMap[key]);
            }

            return corrected;
        },

        checkUserinfo: function () {
            var lines = this.userinfo.split('\n');
            var errors = [];
            var warnings = [];
            var correctedLines = [];
            var hasCorrections = false;

            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line === '') continue; // 跳过空行

                var originalLine = line;

                // 1. 智能符号校正
                line = this.smartCorrectUserinfo(line);

                // 2. 将Tab键转换为空格
                line = line.replace(/\t/g, ' ');

                // 3. 将多个空格替换为单个空格
                line = line.replace(/\s+/g, ' ');

                // 4. 智能密码校正 - 检测并修复密码中的空格
                var parts = line.split(' ');
                if (parts.length >= 2) {
                    // 检查是否有密码被空格分割的情况
                    var correctedParts = [];
                    var currentPart = '';
                    var isPassword = false;

                    for (var j = 0; j < parts.length; j++) {
                        var part = parts[j];

                        // 判断是否为学校名（通常包含中文或者是"自动识别"）
                        if (j === 0 && (/[\u4e00-\u9fa5]/.test(part) || part === '自动识别')) {
                            correctedParts.push(part);
                        }
                        // 判断是否为账号（通常是数字、邮箱或手机号）
                        else if ((j === 0 && !/[\u4e00-\u9fa5]/.test(part)) ||
                                (j === 1 && correctedParts.length === 1)) {
                            correctedParts.push(part);
                        }
                        // 其余部分可能是被分割的密码，需要合并
                        else {
                            if (!isPassword) {
                                currentPart = part;
                                isPassword = true;
                            } else {
                                currentPart += part; // 直接连接，不加空格
                            }
                        }
                    }

                    // 如果有密码部分，添加到结果中
                    if (isPassword && currentPart) {
                        correctedParts.push(currentPart);
                    }

                    // 重新组合
                    if (correctedParts.length >= 2) {
                        line = correctedParts.join(' ');
                    }
                }

                // 检查是否有修正
                if (originalLine !== line) {
                    hasCorrections = true;
                    warnings.push('第' + (i+1) + '行已自动校正：\n原始：' + originalLine + '\n校正：' + line);
                }

                correctedLines.push(line);

                // 验证最终格式
                var finalParts = line.split(' ');
                if (finalParts.length < 2 || finalParts.length > 3) {
                    errors.push('第' + (i+1) + '行数据数量不正确，此行有' + finalParts.length + '个数据。');
                }

                // 检查密码格式（基本验证）
                if (finalParts.length >= 2) {
                    var password = finalParts[finalParts.length - 1]; // 最后一个是密码
                    if (password.length < 3) {
                        warnings.push('第' + (i+1) + '行密码可能过短：' + password);
                    }
                    // 检查密码是否包含明显的空格（可能校正失败）
                    if (password.includes(' ')) {
                        errors.push('第' + (i+1) + '行密码仍包含空格，请手动检查：' + password);
                    }
                }
            }

            // 更新数据
            this.userinfo = correctedLines.join('\n');

            // 显示结果
            var message = '';
            if (hasCorrections) {
                message += '✅ 已自动校正 ' + warnings.length + ' 行数据\n\n';
                if (warnings.length <= 5) {
                    message += warnings.join('\n\n');
                } else {
                    message += warnings.slice(0, 3).join('\n\n') + '\n\n... 还有 ' + (warnings.length - 3) + ' 行校正';
                }
                message += '\n\n';
            }

            if (errors.length > 0) {
                message += '❌ 发现 ' + errors.length + ' 个错误：\n' + errors.join('\n');
                layer.alert(message, {title: '数据检查结果', icon: 5, area: ['500px', '400px']});
            } else {
                if (hasCorrections) {
                    message += '✅ 所有数据格式正确！';
                    layer.alert(message, {title: '数据校正完成', icon: 1, area: ['500px', '400px']});
                } else {
                    layer.msg('✅ 数据检查通过，无需校正！', {icon: 1});
                }
            }
        },

        get: function() {
    if (this.cid == '' || this.userinfo == '') {
        layer.msg("信息格式错误，请检查");
        return false;
    }
    let userinfo = this.userinfo.replace(/\r\n/g, "[br]").replace(/\n/g, "[br]").replace(/\r/g, "[br]");
    userinfo = userinfo.split('[br]'); // 分割
    this.row = [];
    this.check_row = [];
    for (let i = 0; i < userinfo.length; i++) {
        let info = userinfo[i];
        let hash = getENC('<?php echo $addsalt;?>');
        let loading = layer.load(2);
        this.$http.post('/apisub.php?act=get', { cid: this.cid, userinfo: info, hash }, { emulateJSON: true })
            .then((data) => {
                layer.close(loading);
                this.show1 = true;
                this.row.push(data.body);
            })
            .catch((error) => {
                layer.close(loading);
                this.show1 = true;
                this.row.push({ userName: '', userinfo: '', msg: '查询失败，请刷新页面' });
                setTimeout(() => {
                    window.location.href = window.location.href; // 重新加载当前页面
                }, 2000);
            });
    }
},
        add: function() {
            if (this.cid == '') {
                layer.msg("请先查课");
                return false;
            }
            if (this.check_row.length < 1) {
                layer.msg("请先选择课程");
                return false;
            }
            var loading = layer.load(2);
            score = $("#range_02").val();
            shichang = $("#range_01").val();
            this.$http.post("/apisub.php?act=add", {
                    cid: this.cid,
                    data: this.check_row,
                    shichang: shichang,
                    score: score
                }, { emulateJSON: true })
                .then(function(data) {
                    layer.close(loading);
                    if (data.data.code == 1) {
                        this.row = [];
                        this.check_row = [];
                        layer.alert(data.data.msg, { icon: 1, title: "小猿提示" }, function() {
                            setTimeout(function() { window.location.href = "" });
                        });
                    } else {
                        layer.alert(data.data.msg, { icon: 2, title: "温馨提示" });
                    }
                }.bind(this));
        },

        fenlei: async function(id) {
            var loading = layer.load(2);
            this.selectedButton = id;
            try {
                let response = await this.$http.post("/apisub.php?act=getclassfl", { id: id }, { emulateJSON: true });
                layer.close(loading);
                if (response.data.code === 1) {
                    this.class1 = response.body.data;
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error(error);
            }
        },
        saveclass: function() {
        this.$http.post("/apisub.php?act=add_favorite", { cid: this.cid }, { emulateJSON: true }).then(function(response) {
            if (response.data.code == 1) {
                layer.msg("收藏成功", { icon: 1 });
            } else {
                layer.msg(response.data.msg, { icon: 2 });
            }
        });
    },  
        dellclass: function() {
        this.$http.post("/apisub.php?act=remove_favorite", { cid: this.cid }, { emulateJSON: true }).then(function(response) {
            if (response.data.code == 1) {
                layer.msg("移除成功", { icon: 1 });
            } else {
                layer.msg(response.data.msg, { icon: 2 });
            }
        });
    },
    shareclass: function() {
        this.$http.post("/apisub.php?act=shareclass",  { cid: this.cid  }, { emulateJSON: true }).then(function(response) {
    if (response.data.code  == 1) {
        layer.open({ 
            content: '<textarea style="width:280px;height:200px;">' + response.data.msg  + '</textarea>', // 直接使用可编辑的文本框
            icon: 1,
            time: 0, // 弹窗不自动关闭
            btn: ['保存并复制', '关闭'], // 合并保存和复制按钮
            yes: function(index, layero) {
                // 保存并复制功能
                var editedMsg = layero.find('textarea').val();  // 获取编辑后的内容
                response.data.msg  = editedMsg; // 更新msg内容
                navigator.clipboard.writeText(editedMsg).then(function()  {
                    layer.msg('复制成功 快去分享吧！', { icon: 1 });
                }, function() {
                    layer.msg(' 复制失败，内容已保存，请手动复制', { icon: 2 });
                });
            },
            cancel: function(index, layero) {
                // 关闭按钮功能
                layer.close(index); 
            }
        });
    } else {
        layer.msg(response.data.msg,  { icon: 2 });
    }
});
},
        hoverButton: function(id) {
            if (this.selectedButton !== id) {
                document.querySelector(`button[data-id="${id}"]`).classList.add('layui-btn-hover');
            }
        },

        unhoverButton: function(id) {
            document.querySelector(`button[data-id="${id}"]`).classList.remove('layui-btn-hover');
        },

        scsz: function(min, max, from) {
            $("#range_01").ionRangeSlider({
                min: min,
                max: max,
                from: from,
            });
        },

        scoresz: function(min, max, from) {
            $("#range_02").ionRangeSlider({
                min: min,
                max: max,
                from: from,
            });
        },

        checkResources: function(userinfo, userName, rs, id, name) {
            for (let i = 0; i < rs.length; i++) {
                if (id === "") {
                    if (rs[i].name === name) {
                        aa = rs[i];
                    }
                } else {
                    if (rs[i].id === id && rs[i].name === name) {
                        aa = rs[i];
                    }
                }
            }
            let data = { userinfo, userName, data: aa };
            if (this.check_row.length < 1) {
                vm.check_row.push(data);
            } else {
                let a = 0;
                for (let i = 0; i < this.check_row.length; i++) {
                    if (vm.check_row[i].userinfo === data.userinfo && vm.check_row[i].data.name === data.data.name) {
                        a = 1;
                        vm.check_row.splice(i, 1);
                    }
                }
                if (a === 0) {
                    vm.check_row.push(data);
                }
            }
        },
        
        // 全选按钮
        selectAll: function(userinfo, userName, rs, key) {
            if (this.isAllSelected(userinfo, rs)) {
                // 如果已经全选，则取消全选
                this.check_row = this.check_row.filter(item => item.userinfo !== userinfo);
            } else {
                // 否则全选
                rs.forEach(res => {
                    this.checkResources(userinfo, userName, rs, res.id, res.name);
                });
            }
        },

        // 判断是否全选
        isAllSelected: function(userinfo, rs) {
            return rs.every(res => this.check_row.some(item => item.userinfo === userinfo && item.data.name === res.name));
        },

        // 判断某个资源是否被选中
        isChecked: function(userinfo, name) {
            return this.check_row.some(item => item.userinfo === userinfo && item.data.name === name);
        },
        copyQueryInfo: function() {
    let infoToCopy = "亲亲，我们已经将您账号的课程找好啦！\n" +
                     "请告诉我需要代看的课程序号【数字】，我们马上为您安排哈！\n" +
                     "------------------------\n";
                     
    
    let courseNumber = 1;

    // 如果有选中的课程，只复制选中的课程
    this.check_row.forEach((selectedCourse) => {
        infoToCopy += `课程${courseNumber}：${selectedCourse.data.name}\n`;
        courseNumber++;
    });

    // 如果没有选中的课程，复制所有课程
    this.row.forEach((rs) => {
        rs.data.forEach((course) => {
            infoToCopy += `课程${courseNumber}：${course.name}\n`;
            courseNumber++;
        });
    });

    infoToCopy += "------------------------";
    
    // 创建一个临时的 textarea 元素来复制文本
    var tempTextArea = document.createElement('textarea');
    tempTextArea.value = infoToCopy;
    document.body.appendChild(tempTextArea);
    tempTextArea.select();
    document.execCommand('copy');
    document.body.removeChild(tempTextArea);

    layer.msg('查询信息已复制到剪贴板', {icon: 1});
},
tips: function (message) {
        $("#score").hide();
        $("#shic").hide();
    

    for (var i = 0; this.class1.length > i; i++) {
        if (this.class1[i].cid == message) {
            this.show = true;
            this.content = this.class1[i].content;
            this.categoryIntro = this.class1[i].categoryIntro;
            return false;
        }
    }
}

	}
});
</script>