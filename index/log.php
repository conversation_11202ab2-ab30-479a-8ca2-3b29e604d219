<?php
$mod='blank';
$title='日志列表';
require_once('head.php');
?>
     <div class="app-content-body ">
        <div class="wrapper-md control">
        	
	    <div class="panel panel-default" id="loglist">
		    <div class="panel-heading font-bold layui-bg-gray"  style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">操作日志</div>
				 <div class="panel-body"  style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
				     <div class="el-form layui-row layui-col-space10">
				 			<div class="form-inline">	
<div class="form-group">
    <el-input v-model="type" placeholder="请输入类型"></el-input>
</div>
			            <div class="form-group">			          
				               <el-select class="select"  v-model="types" style="scroll 99%;width:100%">
				                <el-option label="请选择查询条件" value=""></el-option>
				                <el-option label="用户UID" value="1"></el-option>
	 		   				    <el-option label="操作内容" value="2"></el-option>
	 		   				    <el-option label="沙子变动" value="3"></el-option>
	 		   				    <el-option label="操作时间" value="4"></el-option>
				              </el-select>              			               
			             </div>  
			              <div class="form-group">
				                <el-input v-model="qq" placeholder="请输入查询内容"></el-input>
			              </div> 
			              <div class="form-group">				              
				               <el-button type="primary" icon="el-icon-search" @click="get(1,1)">查询</el-button>
			              </div>			              
			          </div>
		      <div class="table-responsive">
		        <table class="table table-striped">
		          <thead><tr>
		          <th>
                    <label class="lyear-checkbox checkbox-info">
                      <input type="checkbox" id="check-all">
                    </label>
                  </th>
		          <th>用户ID</th><th>类型</th><th>沙子变动</th><th>沙子</th><th>操作内容</th><th>操作时间</th><th>操作IP</th></tr></thead>
		          <tbody>
		            <tr v-for="res in row.data">
		            	<td>
                            <label class="lyear-checkbox checkbox-info">
                            </label>
                        </td>
		            	<td>{{res.uid}}</td>
		            	<td>
		            	    <span class="btn btn-xs btn-success" v-if="res.type=='批量提交' ||res.type=='添加任务' || res.type=='API添加任务'">{{res.type}}</span>
		            	    <span class="btn btn-xs btn-danger" v-else-if="res.type=='删除订单信息'">{{res.type}}</span>
		            	    <span class="btn btn-xs btn-warning" v-else-if="res.type=='查课' || res.type=='API查课'">{{res.type}}</span>
		            	    <span class="btn btn-xs btn-warning" v-else-if="res.type=='代理充值'">代理充值</span>
		            	    <span class="btn btn-xs btn-success" v-else-if="res.type=='上级充值'">上级充值</span>
		            	    <span class="btn btn-xs btn-primary" v-else-if="res.type=='添加商户'">添加商户</span>
		            	    <span class="btn btn-xs btn-info" v-else-if="res.type=='修改费率' || res.type=='登录'">{{res.type}}</span>
		            	    <span class="btn btn-xs btn-default" v-else="">{{res.type}}</span></td>
		            	<td>{{res.money}}</td>
		            	<td>{{res.smoney}}</td>
		            	<td>{{res.text}}</td>
		            	<td>{{res.addtime}}</td>
		            	<td>{{res.ip}}</td>           	          	
		            </tr>
		          </tbody>
		        </table>
		      </div>
		      
			     <ul class="pagination" v-if="row.last_page>0">
			         <li class="disabled"><a @click="get(1)">首页</a></li>
			         <li class="disabled"><a @click="row.current_page>1?get(row.current_page-1):''">&laquo;</a></li>
		            <li  @click="get(row.current_page-3)" v-if="row.current_page-3>=1"><a>{{ row.current_page-3 }}</a></li>
						    <li  @click="get(row.current_page-2)" v-if="row.current_page-2>=1"><a>{{ row.current_page-2 }}</a></li>
						    <li  @click="get(row.current_page-1)" v-if="row.current_page-1>=1"><a>{{ row.current_page-1 }}</a></li>
						    <li :class="{'active':row.current_page==row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
						    <li  @click="get(row.current_page+1)" v-if="row.current_page+1<=row.last_page"><a>{{ row.current_page+1 }}</a></li>
						    <li  @click="get(row.current_page+2)" v-if="row.current_page+2<=row.last_page"><a>{{ row.current_page+2 }}</a></li>
						    <li  @click="get(row.current_page+3)" v-if="row.current_page+3<=row.last_page"><a>{{ row.current_page+3 }}</a></li>		       			     
			         <li class="disabled"><a @click="row.last_page>row.current_page?get(row.current_page+1):''">&raquo;</a></li>
			         <li class="disabled"><a @click="get(row.last_page)">尾页</a></li>	    
			     </ul>      
		    </div>
		  </div>
     
    </div>
   </div>
 </div>
 
<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script>
new Vue({
    el:"#loglist",
    data:{
        row:null,
        sex:[],
        type:'',
        types:'',
        qq:''
    },
    methods:{
        get:function(page,a){
            const params = new URLSearchParams();
            params.append('page', page);
            params.append('type', this.type);
            params.append('types', this.types);
            params.append('qq', this.qq);
            axios.post("/apisub.php?act=loglist", params).then(response => {	
              	if(response.data.code==1){			                     	
              		this.row=response.data;			             			                     
              	}else{
                    layer.msg(response.data.msg,{icon:2});
              	}
            }).catch(error => {
                layer.msg('请求失败',{icon:2});
            });
        }
    },
    mounted(){
        this.get(1);
    }
});
</script>