<?php
$title = '公告管理系统';
require_once('head.php');
?>
<style>
.liclass {
    font-size: 14px;
    text-indent: 2em;
    margin: 5px;
}
.null {
    font-size: 18px;
    text-align: center;
}
.short-title {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.history-dialog {
    box-shadow: none;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    max-width: 90%;
    margin: 0 auto;
    height: 300px;
}
.history-content {
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
}
</style>
<div class="app-content-body ">
    <div class="wrapper-md control" id="gonggaolist" v-cloak>
        <div class="col-sm-12">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <div class="panel-heading font-bold" style="border-top-left-radius: 8px; border-top-right-radius: 8px;background-color:#fff;">
                        <el-button
                            type="primary"
                            style="background: linear-gradient(135deg, #6a11cb, #2575fc); border: none;"
                            size="medium"
                            @click="showAddNoticeForm"
                        >
                            <i class="el-icon-circle-plus-outline" style="margin-right:5px"></i>
                            添加公告
                        </el-button> 公告管理系统（公告添加后下级可见）
                        <el-dialog :title="editMode ? '编辑公告' : '添加公告'" :visible.sync="addNoticeFormVisible" width="90%">
                            <el-form :model="newNoticeForm">
                                <el-form-item label="公告标题">
                                    <el-input v-model="newNoticeForm.title" placeholder="请输入公告标题"></el-input>
                                </el-form-item>
                                <el-form-item label="公告内容">
                                    <el-input type="textarea" v-model="newNoticeForm.content" :rows="8" placeholder="请输入公告内容（1000字以内）"></el-input>
                                </el-form-item>
                                <el-form-item label="是否置顶">
                                    <el-switch v-model="newNoticeForm.zhiding" active-value="1" inactive-value="0"></el-switch>
                                </el-form-item>
                                <el-form-item label="状态">
                                    <el-switch v-model="newNoticeForm.status" active-value="1" inactive-value="0" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                            </el-form>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="addNoticeFormVisible = false">取 消</el-button>
                                <el-button type="primary" @click="submitNotice">确 定</el-button>
                            </div>
                        </el-dialog>
                    </div>
                </div>
                <div class="text item">
                    <div class="search-box">
                        <el-select v-model="statusFilter" placeholder="筛选状态" style="width: 300px;">
                            <el-option label="选择状态" value=""></el-option>
                            <el-option label="生效中" value="1"></el-option>
                            <el-option label="已禁用" value="0"></el-option>
                        </el-select>
                        <el-input v-model="searchQuery" placeholder="可搜索标题/内容" style="width: 300px;"></el-input>
                        <el-date-picker
                            v-model="addtimeRange"
                            type="daterange"
                            range-separator="~"
                            start-placeholder="添加时间开始"
                            end-placeholder="添加时间结束"
                            value-format="yyyy-MM-dd"
                            style="width: 300px;"
                        ></el-date-picker>
                        <el-button type="primary" @click="get">搜索</el-button>
                    </div>
                    <el-table
                        :data="notices"
                        size="small"
                        header-cell-style="text-align:center;font-weight:1500"
                        cell-style="text-align:center"
                        empty-text="暂无数据"
                        highlight-current-row
                        border
                        @selection-change="handleSelectionChange"
                    >
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column property="id" label="ID" width="50"></el-table-column>
                        <el-table-column property="uid" label="上传者" width="80"></el-table-column>
                        <el-table-column property="title" label="标题" width="200">
                            <template slot-scope="scope">
                                <el-popover
                                    placement="top-start"
                                    trigger="hover"
                                    width="350"
                                    :content="scope.row.title"
                                >
                                    <span slot="reference" class="short-title">{{ scope.row.title | truncate(25) }}</span>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column property="content" label="内容" width="300">
                            <template slot-scope="scope">
                                <el-popover
                                    placement="top-start"
                                    trigger="hover"
                                    width="350"
                                    :content="scope.row.content"
                                >
                                    <span slot="reference" class="short-title">{{ scope.row.content | truncate(25) }}</span>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column property="status" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag type="success" size="small" v-if="scope.row.status == '1'" effect="plain">生效中</el-tag>
                                <el-tag type="danger" size="small" v-else-if="scope.row.status == '0'" effect="plain">已禁用</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column property="zhiding" label="置顶" width="80">
                            <template slot-scope="scope">
                                <el-tag type="primary" size="small" v-if="scope.row.zhiding == '1'" effect="plain">是</el-tag>
                                <el-tag type="info" size="small" v-else effect="plain">否</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column property="time" label="添加时间" width="150" sortable></el-table-column>
                        <el-table-column label="操作" width="150">
                            <template slot-scope="scope">
                                <el-button type="primary" size="mini" @click="editNotice(scope.row)">编辑</el-button>
                                <el-popconfirm
                                    placement="top-start"
                                    confirm-button-text='确定删除'
                                    cancel-button-text='取消'
                                    cancel-button-type="danger" 
                                    icon="el-icon-info"
                                    icon-color="red"
                                    title="是否删除该公告？"
                                    @confirm="deleteNotice(scope.row.id)"
                                >
                                    <el-button type="danger" size="mini" slot="reference" plain>删除</el-button>
                                </el-popconfirm>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="block">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page.sync="currentPage"
                        :page-size="pageSize"
                        :page-sizes="[10, 20, 50, 100, 200]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                    >
                    </el-pagination>
                </div>
            </el-card>
        </div>
    </div>
</div>
<?php require_once("footer.php");?>
<script>
Vue.filter('truncate', function(value, length) {
    if (!value) return '';
    length = length || 20;
    if (value.length <= length) {
        return value;
    }
    return value.substring(0, length) + '...';
});

var vm = new Vue({
    el: "#gonggaolist",
    data: {
        notices: [],
        searchQuery: '',
        statusFilter: '',
        addtimeRange: [],
        currentPage: 1,
        pageSize: 10,
        total: 0,
        addNoticeFormVisible: false,
        editMode: false,
        selectedNotices: [],
        newNoticeForm: {
            id: null,
            title: '',
            content: '',
            zhiding: '0',
            status: '1' // 默认启用
        }
    },
    methods: {
        handleSelectionChange(selection) {
            this.selectedNotices = selection.map(row => row.id);
        },
        showAddNoticeForm() {
            this.editMode = false;
            this.newNoticeForm = { id: null, title: '', content: '', zhiding: '0', status: '1' };
            this.addNoticeFormVisible = true;
        },
        editNotice(row) {
            this.editMode = true;
            this.newNoticeForm = {
                id: row.id,
                title: row.title,
                content: row.content,
                zhiding: row.zhiding,
                status: row.status
            };
            this.addNoticeFormVisible = true;
        },
        submitNotice() {
            if (!this.newNoticeForm.title) {
                this.$message.error('请输入公告标题');
                return;
            }
            if (!this.newNoticeForm.content) {
                this.$message.error('请输入公告内容');
                return;
            }
            if (this.newNoticeForm.content.length > 1000) {
                this.$message.error('公告内容超过1000字限制');
                return;
            }
            const load = layer.load(2);
            const type = this.editMode ? 'edit' : 'add';
            this.$http.post("/apisub.php?act=gonggao", {
                id: this.newNoticeForm.id,
                title: this.newNoticeForm.title,
                content: this.newNoticeForm.content,
                zhiding: this.newNoticeForm.zhiding,
                status: this.newNoticeForm.status,
                type: type
            }, { emulateJSON: true }).then(response => {
                layer.close(load);
                if (response.data.code == 1) {
                    this.addNoticeFormVisible = false;
                    this.get();
                    this.$message.success(`公告${type == 'add' ? '新增' : '修改'}成功`);
                } else {
                    this.$message.error(response.data.msg || `公告${type == 'add' ? '新增' : '修改'}失败`);
                }
            }).catch(error => {
                console.error(error);
                layer.close(load);
                this.$message.error(`公告${type == 'add' ? '新增' : '修改'}失败`);
            });
        },
        deleteNotice(id) {
            this.$http.post("/apisub.php?act=gonggaoshan", { id }, { emulateJSON: true }).then(response => {
                if (response.data.code == 1) {
                    this.get();
                    this.$message.success('删除成功');
                } else {
                    this.$message.error(response.data.msg || '删除失败');
                }
            }).catch(error => {
                console.error(error);
                this.$message.error('删除失败');
            });
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.get();
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            this.get();
        },
        get() {
            const data = {
                searchQuery: this.searchQuery,
                statusFilter: this.statusFilter,
                addtime: this.addtimeRange ? this.addtimeRange.join(',') : '',
                page: this.currentPage,
                limit: this.pageSize
            };
            this.$http.post("/apisub.php?act=gonggaolist", data, { emulateJSON: true }).then(response => {
                if (response.data.code == 1) {
                    this.notices = response.data.data;
                    this.total = parseInt(response.data.total);
                } else {
                    layer.msg(response.data.msg || '获取数据失败', { icon: 2 });
                }
            }).catch(error => {
                console.error(error);
                layer.msg('获取数据失败', { icon: 2 });
            });
        }
    },
    mounted() {
        this.get();
    }
});
</script>