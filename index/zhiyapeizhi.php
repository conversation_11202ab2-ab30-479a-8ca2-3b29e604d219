<?php
$mod='blank';
$title='质押配置';
if($userrow['uid']!=1){
setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
exit("<script language='javascript'>window.location.href='login.php';</script>");}
?>

<div class="app-content-body" id="app">
    <el-container>
        <el-main>
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>分类质押配置管理</span>
                    <el-button style="float: right" size="small" type="primary" @click="handleAdd">添加配置</el-button>
                </div>

                <!-- 表格 -->
                <el-table 
                    :data="row.data" 
                    stripe 
                    v-loading="loading"
                    border
                    style="width: 100%">
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="category_name" label="分类名称" width="150"></el-table-column>
                    <el-table-column prop="amount" label="质押金额" width="120">
                        <template slot-scope="scope">
                            {{scope.row.amount}}元
                        </template>
                    </el-table-column>
                    <el-table-column prop="discount_rate" label="折扣率">
                        <template slot-scope="scope">
                            {{(scope.row.discount_rate * 10).toFixed(1)}}折
                        </template>
                    </el-table-column>
                    <el-table-column prop="days" label="质押天数">
                        <template slot-scope="scope">
                            {{scope.row.days}}天
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" >
                        <template slot-scope="scope">
                            <el-switch
                                v-model="scope.row.status"
                                :active-value="1"
                                :inactive-value="0"
                                @change="handleStatusChange(scope.row)"
                                active-color="#13ce66"
                                inactive-color="#ff4949">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column prop="addtime" label="添加时间" width="160"  show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="handleViewUsers(scope.row)">
                                用户
                            </el-button>
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="handleEdit(scope.row)">
                                编辑
                            </el-button>
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="handleDelete(scope.row)"
                                :disabled="scope.row.status === 1">
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        共 {{row.total}} 条记录
                    </div>
                    <el-pagination
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-size="pageSize"
                        layout="prev, pager, next"
                        :total="row.total">
                    </el-pagination>
                </div>
            </el-card>
        </el-main>
    </el-container>

    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
        <el-form :model="form" label-width="100px" :rules="rules" ref="form">
            <el-form-item label="分类" prop="category_id">
                <el-select v-model="form.category_id" placeholder="请选择分类">
                    <el-option
                        v-for="item in categories"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="质押金额" prop="amount">
                <el-input v-model="form.amount" type="number">
                    <template slot="append">元</template>
                </el-input>
            </el-form-item>
            <el-form-item label="折扣率" prop="discount_rate">
                <el-input v-model.number="form.discount_rate" type="number" min="0" max="1" step="0.1">
                    <template slot="append">乘法</template>
                </el-input>
            </el-form-item>
            <el-form-item label="质押天数" prop="days">
                <el-input v-model.number="form.days" type="number" min="1">
                    <template slot="append">天</template>
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
    </el-dialog>

    <!-- 用户列表弹窗 -->
    <el-dialog 
        title="质押用户列表" 
        :visible.sync="userListVisible" 
        width="800px">
        <div v-if="currentZhiya" class="zhiya-info">
            <div class="info-item">
                <span class="label">分类：</span>
                <span class="value">{{currentZhiya.category_name}}</span>
            </div>
            <div class="info-item">
                <span class="label">质押金额：</span>
                <span class="value">{{currentZhiya.amount}}元</span>
            </div>
            <div class="info-item">
                <span class="label">折扣率：</span>
                <span class="value">{{(currentZhiya.discount_rate * 10).toFixed(1)}}乘法</span>
            </div>
            <div class="info-item">
                <span class="label">质押天数：</span>
                <span class="value">{{currentZhiya.days}}天</span>
            </div>
        </div>
        <el-table 
            :data="zhiyaUsers" 
            stripe 
            v-loading="userLoading"
            border
            style="width: 100%">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="user" label="用户名" width="150"></el-table-column>
            <el-table-column prop="addtime" label="质押时间" width="160"></el-table-column>
            <el-table-column prop="endtime" label="到期时间" width="160"></el-table-column>
            <el-table-column label="状态">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status == 2 ? 'info' : 'success'">
                        {{scope.row.status == 2 ? '已到期' : '生效中'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                    <el-button 
                        v-if="scope.row.status == 1" 
                        type="danger" 
                        size="mini" 
                        @click="handleCancelZhiya(scope.row)">
                        取消质押
                    </el-button>
                    <span v-else style="color:#aaa;">已到期</span>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</div>

<?php require_once('footer.php');?>

<script>
new Vue({
    el: '#app',
    data() {
        // Adding custom validation rule for discount rate
        const validateDiscount = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入折扣率'));
            } else if (value <= 0 || value > 1) {
                callback(new Error('折扣率必须在0-1之间'));
            } else {
                callback();
            }
        };
        return {
            row: {},
            loading: false,
            currentPage: 1,
            pageSize: 10,
            dialogVisible: false,
            dialogTitle: '添加配置',
            categories: [],
            form: {
                id: '',
                category_id: '',
                amount: '',
                discount_rate: '',
                days: 30, // Default 30 days
                cancel_fee: 0 // Default 0%
            },
            rules: {
                category_id: [
                    { required: true, message: '请选择分类', trigger: 'change' }
                ],
                amount: [
                    { required: true, message: '请输入质押金额', trigger: 'blur' }
                ],
                discount_rate: [
                    { required: true, message: '请输入折扣率', trigger: 'blur' },
                    { validator: validateDiscount, trigger: 'blur' }
                ],
                days: [
                    { required: true, message: '请输入质押天数', trigger: 'blur' },
                    { type: 'number', min: 1, message: '天数必须大于0', trigger: 'blur' }
                ],
            },
            userListVisible: false, // User list dialog visibility
            zhiyaUsers: [], // Pledged users list
            userLoading: false, // User list loading state
            currentZhiya: null // Current pledge configuration
        }
    },
    methods: {
        getList() {
            this.loading = true;
            this.$http.post("/apisub.php?act=zhiyalist", {
                page: this.currentPage,
                limit: this.pageSize
            }).then(res => {
                if(res.data.code === 1) {
                    if(res.data.data && res.data.data.length) {
                        res.data.data = res.data.data.map(item => ({
                            ...item,
                            status: parseInt(item.status) // Convert string to number
                        }));
                    }
                    this.row = res.data;
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        handleAdd() {
            this.dialogTitle = '添加配置';
            this.form = {
                id: '',
                category_id: '',
                amount: '',
                discount_rate: '',
                days: 30, // Default 30 days
                cancel_fee: 0 // Default 0%
            };
            this.dialogVisible = true;
        },
        handleEdit(row) {
            this.dialogTitle = '编辑配置';
            this.form = {
                ...row
            };
            this.dialogVisible = true;
        },
        submitForm() {
            this.$refs.form.validate(valid => {
                if(valid) {
                    const action = this.form.id ? '2' : '1';
                    this.$http.post("/apisub.php?act=zhiya_config", {
                        active: action,
                        data: this.form
                    }).then(res => {
                        if(res.data.code === 1) {
                            this.$message.success(res.data.msg);
                            this.dialogVisible = false;
                            this.getList();
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }
            });
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getList();
        },
        handleStatusChange(row) {
            this.$http.post("/apisub.php?act=zhiya_config", {
                active: '4',
                data: {
                    id: row.id,
                    status: row.status
                }
            }).then(res => {
                if(res.data.code === 1) {
                    this.$message.success(res.data.msg);
                } else {
                    this.$message.error(res.data.msg);
                    // Revert status on error
                    row.status = row.status === 1 ? 0 : 1;
                }
            }).catch(() => {
                // Revert status on error
                row.status = row.status === 1 ? 0 : 1;
            });
        },
        handleDelete(row) {
            this.$confirm('确认要删除该质押配置吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.post("/apisub.php?act=zhiya_config", {
                    active: '3',
                    data: {
                        id: row.id
                    }
                }).then(res => {
                    if(res.data.code === 1) {
                        this.$message.success(res.data.msg);
                        this.getList();
                    } else {
                        this.$message.error(res.data.msg);
                    }
                });
            }).catch(() => {});
        },
        handleViewUsers(row) {
            this.currentZhiya = row;
            this.userListVisible = true;
            this.getZhiyaUsers(row);
        },
        getZhiyaUsers(row) {
            this.userLoading = true;
            this.$http.post("/apisub.php?act=get_zhiya_users", {
                config_id: row.id
            }).then(res => {
                if(res.data.code === 1) {
                    this.zhiyaUsers = res.data.data;
                }
            }).finally(() => {
                this.userLoading = false;
            });
        },
        handleCancelZhiya(row) {
            this.$confirm('确认要取消该质押记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.userLoading = true;
                this.$http.post("/apisub.php?act=cancel_zhiya_record", {
                    record_id: row.id
                }).then(res => {
                    if(res.data.code === 1) {
                        this.$message.success(res.data.msg);
                        this.getZhiyaUsers(this.currentZhiya); // Refresh user list
                    } else {
                        this.$message.error(res.data.msg);
                    }
                }).finally(() => {
                    this.userLoading = false;
                });
            }).catch(() => {});
        }
    },
    mounted() {
        this.getList();
        this.$http.post("/apisub.php?act=get_categories").then(res => {
            if(res.data.code === 1) {
                this.categories = res.data.data;
            }
        });
    }
});
</script>

