<?php
include('../confing/common.php');
include('../confing/intercept.php');
$sql = "SELECT uid, user, yqm, money, notice FROM qingka_wangke_user WHERE uid = ?";
$params = [$userrow['uuid']];
$sj = $DB->prepare_getrow($sql, $params);
$ck = $DB->prepare_count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API查课' AND uid=?", [$userrow['uid']]);
$xd = $DB->prepare_count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API添加任务' AND uid=?", [$userrow['uid']]);
if ($ck == 0 || $xd == 0) {
    $xdb = 100;
} else {
    $xdb = round($xd / $ck, 4) * 100;
}
?>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?=$conf['sitename']?></title>
<meta name="keywords" content="<?=$conf['keywords'];?>" />
<meta name="description" content="<?=$conf['description'];?>" />
<link rel="icon" href="../favicon.ico" type="image/ico">
<meta name="author" content=" ">
<link rel="stylesheet" href="../assets/css/index.css">
<link rel="stylesheet" href="../assets/yqsladmin/css/index.css">
<link rel="stylesheet" href="../assets/css/apps.css" type="text/css" />
<link rel="stylesheet" href="../assets/css/app.css" type="text/css" />
<link rel="stylesheet" href="../assets/layer/theme/default/layer.css" type="text/css" />
<link rel="stylesheet" href="../assets/layui/css/layui.css" type="text/css" />
<link rel="stylesheet" href="../assets/LightYear/js/bootstrap-multitabs/multitabs.min.css">
<link href="../assets/LightYear/css/bootstrap.min.css" rel="stylesheet">
<link href="../assets/LightYear/css/style.min.css" rel="stylesheet">
<link href="../assets/LightYear/css/materialdesignicons.min.css" rel="stylesheet">
<script src="../assets/yqsladmin/js/tools.js"></script>



</head>
<?php
if($userrow['active']=="0"){
alert('您的账号已被封禁！','login');
}
// if($userrow['zcz']=="0"){
// alert('账号已限制登陆！','login');
// }
?>
<body>