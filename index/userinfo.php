<?php
include('head.php');
?>
<style>
.key-button {
    width: 100%;
    padding: 8px;
    font-size: 14px;
    text-align: center;
    background: #f0f8ff;
    border: 1px solid #cce5ff;
    border-radius: 4px;
    cursor: pointer;
}
.key-button:hover {
    background: #e6f0ff;
}
</style>

<div id="userindex">
  <div class="container-fluid p-t-15">
    <div class="row">
      <div class="col-lg-12">
        <div class="card">
          <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
            个人资料
          </div>
          <div class="card-body">
            <form class="site-form">
              <div class="row">
                <div class="col-md-6 form-group">
                  <label>昵称</label>
                  <input type="text" class="form-control" :value="row.name" disabled="disabled" />
                </div>
                <div class="col-md-6 form-group">
                  <label>账号</label>
                  <input type="text" class="form-control" :value="row.user" disabled="disabled" />
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 form-group">
                  <label>剩余积分</label>
                  <input type="text" class="form-control" :value="row.money" disabled="disabled" />
                </div>
                <div class="col-md-6 form-group">
                  <label>总充值</label>
                  <input type="text" class="form-control" :value="row.zcz" disabled="disabled" />
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 form-group">
                  <label>费率</label>
                  <input type="text" class="form-control" :value="row.addprice" disabled="disabled" />
                </div>
                <div class="col-md-6 form-group">
                  <label>等级名</label>
                  <input type="text" class="form-control" :value="row.djname == '' ? '无' : row.djname" disabled="disabled" />
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 form-group">
                  <label>邀请码</label>
                  <input type="text" class="form-control" :value="row.yqm == '' ? '无' : row.yqm" disabled="disabled" />
                </div>
                <div class="col-md-6 form-group">
                  <label>邀请费率</label>
                  <input type="text" class="form-control" :value="row.yqprice == '' ? '无' : row.yqprice" disabled="disabled" />
                </div>
              </div>
            </form>

            <!-- 按钮区域 -->
            <div class="layui-col-md-12 button-spacing">
              <button v-if="row.yqm == 0" @click="szyqprice" class="key-button">开通邀请码</button>
              <button v-else class="key-button" @click="szyqprice">重设邀请码</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>
<script type="text/javascript">
    var vm = new Vue({
        el: "#userindex",
        data: {
            row: null,
            inte: '',
            useCustomYqm: false,
            customYqm: ''
        },
        methods: {
            userinfo: function() {
                var load = layer.load(2);
                axios.post("/apisub.php?act=userinfo")
                    .then(response => {
                        layer.close(load);
                        const data = response.data;
                        if (data.code == 1) {
                            this.row = data;
                        } else {
                            layer.alert(data.msg, {
                                icon: 2
                            });
                        }
                    })
                    .catch(error => {
                        layer.close(load);
                        console.error(error);
                        layer.alert('请求失败，请检查网络', {
                            icon: 2
                        });
                    });
            },
            szyqprice: function() {
               let promptContent = '<div style="display: flex; flex-direction: column; gap: 15px; max-width: 300px; padding: 10px;">' +
                    '<div style="display: flex; align-items: center; gap: 10px;">' +
                    '<label style="width: 150px;">默认费率：</label>' +
                    '<input type="number" class="layui-input" id="yqprice" placeholder="请输入费率" step="0.05" min="0.2" style="width: 150px;">' +
                    '</div>' +
                    '<div style="display: flex; align-items: center; gap: 10px;">' +
                    '<input type="checkbox" id="useCustomYqmCheckbox" lay-skin="primary">' +
                    '<label>自定义邀请码（需额外扣费5积分）</label>' +
                    '</div>' +
                    '<div style="display: flex; align-items: center; gap: 10px;">' +
                    '<label style="width: 150px;">邀请码：</label>' +
                    '<input type="text" class="layui-input" id="customYqmInput" placeholder="4-8位字母或数字" maxlength="8" style="width: 150px; display: none;">' +
                    '</div>' +
                    '</div>';
                layer.open({
                    type: 1,
                    title: '设置邀请信息',
                    content: promptContent,
                    area: ['350px', '300px'],
                    btn: ['确定', '取消'],
                    success: function(layero, index) {
                        $('#useCustomYqmCheckbox').on('change', function () {
                            if ($(this).is(':checked')) {
                                $('#customYqmInput').show();
                            } else {
                                $('#customYqmInput').hide();
                            }
                        });
                    },
                    yes: function(index, layero) {
                        const yqprice = $('#yqprice').val();
                        const useCustomYqm = $('#useCustomYqmCheckbox').is(':checked');
                        const customYqm = $('#customYqmInput').val();
                        if (useCustomYqm && (!customYqm || !/^[a-zA-Z0-9]{4,8}$/.test(customYqm))) {
                            layer.msg("邀请码必须是4-8位字母或数字", { icon: 2 });
                            return false;
                        }
                        layer.close(index);
                        var load = layer.load(2);
                        const params = new URLSearchParams();
                        params.append('yqprice', yqprice);
                        if (useCustomYqm && customYqm) {
                            params.append('yqm', customYqm);
                        }
                        axios.post("/apisub.php?act=yqprice", params)
                            .then(response => {
                                layer.close(load);
                                const data = response.data;
                                if (data.code == 1) {
                                    vm.userinfo();
                                    layer.alert(data.msg, {
                                        icon: 1
                                    });
                                } else {
                                    layer.msg(data.msg, {
                                        icon: 2
                                    });
                                }
                            })
                            .catch(error => {
                                layer.close(load);
                                console.error(error);
                                layer.alert('请求失败，请检查网络', {
                                    icon: 2
                                });
                            });
                        return false;
                    }
                });
            }
        },
        mounted() {
            this.userinfo();
        }
    });
</script>
</body>
</html>