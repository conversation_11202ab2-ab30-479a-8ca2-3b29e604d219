<?php
include('../confing/common.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?=$conf['sitename']?></title>
    <meta name="keywords" content="<?=$conf['keywords'];?>"/>
    <meta name="description" content="<?=$conf['description'];?>"/>
    <link rel="stylesheet" href="../assets/layer/theme/default/layer.css "/>
    <style>
        :root {
            /* COLORS */
            --white: #e9e9e9;
            --gray: #333;
            --blue: #0367a6;
            --lightblue: #008997;

            /* RADII */
            --button-radius: 0.7rem;

            /* SIZES */
            --max-width: 758px;
            --max-height: 420px;

            font-size: 16px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
                Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .form__title {
            font-weight: 300;
            margin: 0;
            margin-bottom: 1.25rem;
        }

        .link {
            color: var(--gray);
            font-size: 0.8rem;
            margin: 1.0rem 0;
            display: inline-block; /* 原来的 display 是 block */
            text-decoration: none;
        }
        .container {
            background-color: var(--white);
            border-radius: var(--button-radius);
            box-shadow: 0 0.9rem 1.7rem rgba(0, 0, 0, 0.25),
                0 0.7rem 0.7rem rgba(0, 0, 0, 0.22);
            height: var(--max-height);
            max-width: var(--max-width);
            overflow: hidden;
            position: relative;
            width: 100%;
        }

        .container__form {
            height: 100%;
            position: absolute;
            top: 0;
            transition: all 0.6s ease-in-out;
        }

        .container--signin {
            left: 0;
            width: 50%;
            z-index: 2;
        }

        .container.right-panel-active .container--signin {
            transform: translateX(100%);
        }

        .container--signup {
            left: 0;
            opacity: 0;
            width: 50%;
            z-index: 0;
        }

        .container.right-panel-active .container--signup {
            animation: show 0.6s;
            opacity: 1;
            transform: translateX(100%);
            z-index: 5;
        }

        .container__overlay {
            height: 100%;
            left: 50%;
            overflow: hidden;
            position: absolute;
            top: 0;
            transition: transform 0.6s ease-in-out;
            width: 50%;
            z-index: 100;
        }

        .container.right-panel-active .container__overlay {
            transform: translateX(-100%);
        }

        .overlay {
            background-color: var(--lightblue);
            background: url("https://cdn.pixabay.com/photo/2018/08/14/13/23/ocean-3605547_1280.jpg");
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            height: 100%;
            left: -100%;
            position: relative;
            transform: translateX(0);
            transition: transform 0.6s ease-in-out;
            width: 200%;
        }

        .container.right-panel-active .overlay {
            transform: translateX(50%);
        }

        .overlay__panel {
            align-items: center;
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: center;
            position: absolute;
            text-align: center;
            top: 0;
            transform: translateX(0);
            transition: transform 0.6s ease-in-out;
            width: 50%;
        }

        .overlay--left {
            transform: translateX(-20%);
        }

        .container.right-panel-active .overlay--left {
            transform: translateX(0);
        }

        .overlay--right {
            right: 0;
            transform: translateX(0);
        }

        .container.right-panel-active .overlay--right {
            transform: translateX(20%);
        }

        .btn {
            background-color: var(--blue);
            background-image: linear-gradient(90deg, var(--blue) 0%, var(--lightblue) 74%);
            border-radius: 20px;
            border: 1px solid var(--blue);
            color: var(--white);
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: bold;
            letter-spacing: 0.1rem;
            padding: 0.9rem 4rem;
            text-transform: uppercase;
            transition: transform 80ms ease-in;
        }

        .form > .btn {
            margin-top: 1.5rem;
        }

        .btn:active {
            transform: scale(0.95);
        }

        .btn:focus {
            outline: none;
        }

        .form {
            background-color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 0 3rem;
            height: 100%;
            text-align: center;
        }

        .input {
            background-color: #fff;
            border: none;
            padding: 0.9rem 0.9rem;
            margin: 0.5rem 0;
            width: 100%;
        }

        @keyframes show {
            0%,
            49.99% {
                opacity: 0;
                z-index: 1;
            }

            50%,
            100% {
                opacity: 1;
                z-index: 5;
            }
        }

        @media (max-width: 768px) {
            .container {
                width: 90%;
                max-width: 400px;
                height: auto;
                display: flex;
                flex-direction: column;
            }

            .container__form {
                position: static;
                width: 100%;
                height: auto;
            }

            .container--signin,
            .container--signup {
                width: 100%;
                opacity: 1;
                transform: none;
                z-index: auto;
                padding-top: 2rem;
                padding-bottom: 2rem;
            }

            .container.right-panel-active .container--signin,
            .container.right-panel-active .container--signup {
                transform: none;
            }

            .container__overlay {
                display: none;
            }

            .link {
                margin: 1rem 0;
            }

            .container--signup {
                display: none;
            }

            .container--signin {
                display: block;
            }

            .container.right-panel-active .container--signin {
                display: none;
            }

            .container.right-panel-active .container--signup {
                display: block;
            }
        }
    </style>
<script>
    if (self !== top) {
        top.location.href = window.location.href;
    }
</script>
</head>
<body>
    <div class="container" ref="container">
        <!-- Sign Up -->
        <div class="container__form container--signup">
            <form action="#" class="form" id="form1">
                <h2 class="form__title">Sign Up</h2>
                <input type="text" placeholder="昵称" class="input" v-model="reg.name" />
                <input type="number" placeholder="账号（账号必须为QQ号）" class="input" v-model="reg.user" />
                <input type="password" placeholder="密码" class="input" v-model="reg.pass" />
                <input type="text" placeholder="邀请码" class="input" v-model="reg.yqm" />
                <button class="btn" @click="register">Sign Up</button>
            </form>
        </div>

        <!-- Sign In -->
        <div class="container__form container--signin">
            <form action="#" class="form" id="form2">
                <h2 class="form__title">Sign In</h2>
                <input type="text" placeholder="请输入账号" class="input" v-model="dl.user" />
                <input type="password" placeholder="请输入密码" class="input" v-model="dl.pass" />
                <a href="#" class="link" @click="showSignUp">注册账号</a>
                <button class="btn" @click="login">Sign In</button>
            </form>
        </div>

        <!-- Overlay -->
        <div class="container__overlay">
            <div class="overlay">
                <div class="overlay__panel overlay--left">
                    <button class="btn" id="signIn" @click="showSignIn">Sign In</button>
                </div>
                <div class="overlay__panel overlay--right">
                    <button class="btn" id="signUp" @click="showSignUp">Sign Up</button>
                </div>
            </div>
        </div>
    </div>


<?php require_once("lightyearfooter.php");?>	
<?php require_once("footer.php");?>
<script>
    var vm = new Vue({
        el: ".container",
        data: {
            dl: {},
            reg: {}
        },
        methods: {
            login: function(event) {
                event.preventDefault();
                if (!this.dl.user || !this.dl.pass) {
                    layer.msg('账号密码不能为空', {
                        icon: 2
                    });
                    return;
                }
                var loading = layer.load();
                axios.post("/apisub.php?act=login", {
                    user: this.dl.user,
                    pass: this.dl.pass
                }, {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    transformRequest: [function (data) {
                        let ret = '';
                        for (let it in data) {
                            ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
                        }
                        return ret;
                    }]
                }).then(function(response) {
                    layer.close(loading);
                    if (response.data.code == 1) {
                        layer.msg(response.data.msg, {
                            icon: 1
                        });
                        setTimeout(function() {
                            window.location.href = "index"
                        }, 1000);
                    } else if (response.data.code == 5) {
                        vm.login2();
                    } else {
                        layer.msg(response.data.msg, {
                            icon: 2
                        });
                    }
                });
            },
            register: function(event) {
                event.preventDefault();
                if (!this.reg.user || !this.reg.pass || !this.reg.name || !this.reg.yqm) {
                    layer.msg('所有项不能为空', {
                        icon: 2
                    });
                    return;
                }
                var loading = layer.load();
                axios.post("/apisub.php?act=register", {
                    name: this.reg.name,
                    user: this.reg.user,
                    pass: this.reg.pass,
                    yqm: this.reg.yqm
                }, {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    transformRequest: [function (data) {
                        let ret = '';
                        for (let it in data) {
                            ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
                        }
                        return ret;
                    }]
                }).then(function(response) {
                    layer.close(loading);
                    if (response.data.code == 1) {
                        this.loginType = true;
                        this.dl.user = this.reg.user;
                        this.dl.pass = this.reg.pass;
                        layer.msg(response.data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(response.data.msg, {
                            icon: 2
                        });
                    }
                }.bind(this));
            },
            showSignUp: function(event) {
                    event.preventDefault();
                    this.$refs.container.classList.add("right-panel-active");
                },

                // 显示登录表单
                showSignIn: function(event) {
                    event.preventDefault();
                    this.$refs.container.classList.remove("right-panel-active");
                },
            login2: function() {
                layer.prompt({
                    title: '管理二次验证',
                    formType: 3
                }, function(pass2, index) {
                    var loading = layer.load();
                    axios.post("/apisub.php?act=login", {
                        user: vm.dl.user,
                        pass: vm.dl.pass,
                        pass2: pass2
                    }, {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        transformRequest: [function (data) {
                            let ret = '';
                            for (let it in data) {
                                ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
                            }
                            return ret;
                        }]
                    }).then(function(response) {
                        layer.close(loading);
                        if (response.data.code == 1) {
                            layer.msg(response.data.msg, {
                                icon: 1
                            });
                            setTimeout(function() {
                                window.location.href = "index"
                            }, 1000);
                        } else {
                            layer.msg(response.data.msg, {
                                icon: 2
                            });
                        }
                    });
                });
            }
        }
    });



    // 随机选择背景图片
    const backgroundImages = [
            "http://p.ananas.chaoxing.com/star3/origin/a6605899f406299cfa411ad493d0873c.png",
            "http://p.ananas.chaoxing.com/star3/origin/3973fb580e8b140e5626a9c19687c45c.png",
            "http://p.ananas.chaoxing.com/star3/origin/dc9a0f75710d01738a96e6645a9cca32.png",
            "http://p.ananas.chaoxing.com/star3/origin/a656f03c554ec38758468dced8b8794e.png",
            "http://p.ananas.chaoxing.com/star3/origin/d951290eae93bbf944d30f1ce2e2a54f.png",
            "http://p.ananas.chaoxing.com/star3/origin/64d6616552d7bd3dfe8e0ec654983625.png",
            "http://p.ananas.chaoxing.com/star3/origin/b13fb0b51a98d0ec78f79b7a04b58269.png",
            "https://s21.ax1x.com/2025/04/27/pETKGkD.jpg",
            "https://s21.ax1x.com/2025/04/27/pETKNpd.jpg",
            "https://s21.ax1x.com/2025/04/27/pETKJte.jpg",
            "https://s21.ax1x.com/2025/04/27/pETKYfH.jpg",
            "https://s21.ax1x.com/2025/04/27/pETKa6I.jpg",
            "https://s21.ax1x.com/2025/04/27/pETKU1A.jpg",
            "https://s21.ax1x.com/2025/04/27/pETKdXt.jpg"
    ];

    function changeBackground() {
        const randomIndex = Math.floor(Math.random() * backgroundImages.length);
        const img = new Image();
        img.src = backgroundImages[randomIndex];
        img.referrerPolicy = 'no-referrer';
        img.onload = () => {
            document.body.style.backgroundImage = `url(${img.src})`;
        };
    }

    // 初始加载时随机选择一个背景图片
    changeBackground();
</script>
</body>
</html>