<?php
$title = '数据统计与巅峰排行榜';
include('head.php');

// Restrict access to admin (uid = 1)
if ($userrow['uid'] != 1) {
    setcookie("admin_token", "", time() - 90000, "/", "", true, true);
    exit("<script language='javascript'>window.location.href='login.php';</script>");
}

// Define $jtdate as the start of the current day
$jtdate = date('Y-m-d 00:00:00');
?>

<div class="app-content-body">
    <div class="wrapper-md control" id="userindex">
        <div class="row">
            <div class="col-lg-6">
                <div class="card" style="box-shadow: 18px 18px 30px #d1d9e6, -18px -18px 30px #fff;border-radius: 8px;">
                    <div class="panel-heading font-bold bg-white">数据统计</div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <tbody>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-yen"></i> 总用户:
                                        <span class="badge label label-black">
                                            <?php
                                            $total_users = $DB->count("SELECT COUNT(*) FROM qingka_wangke_user");
                                            echo $total_users . "人";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-yen"></i> 今日新增用户:
                                        <span class="badge label label-black">
                                            <?php
                                            $today_new_users = $DB->count("SELECT COUNT(*) FROM qingka_wangke_user WHERE addtime > '$jtdate'");
                                            echo $today_new_users . "人";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-ok"></i> 今日订单:
                                        <span class="badge label label-black">
                                            <?php
                                            $today_orders = $DB->count("SELECT COUNT(*) FROM qingka_wangke_order WHERE addtime > '$jtdate'");
                                            echo $today_orders . "条";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-yen"></i> 今日销售:
                                        <span class="badge label label-black">
                                            <?php
                                            $today_sales = 0;
                                            $result = $DB->query("SELECT fees FROM qingka_wangke_order WHERE addtime > '$jtdate'");
                                            while ($row = $DB->fetch($result)) {
                                                $today_sales += $row['fees'];
                                            }
                                            echo $today_sales . "元";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-yen"></i> 代理余额总和:
                                        <span class="badge label label-black">
                                            <?php
                                            $agent_balance = $DB->count("SELECT SUM(money) FROM qingka_wangke_user WHERE uid != 1");
                                            echo $agent_balance . "元";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-yen"></i> 今日在线充值:
                                        <span class="badge label label-black">
                                            <?php
                                            $today_online_recharge = 0;
                                            $result = $DB->query("SELECT money FROM qingka_wangke_log WHERE type='在线充值' AND addtime > '$jtdate'");
                                            while ($row = $DB->fetch($result)) {
                                                $today_online_recharge += $row['money'];
                                            }
                                            echo $today_online_recharge . "元";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-yen"></i> 今日总充值:
                                        <span class="badge label label-black">
                                            <?php
                                            $today_total_recharge = 0;
                                            $result = $DB->query("SELECT money FROM qingka_wangke_log WHERE type='上级充值' AND addtime > '$jtdate'");
                                            while ($row = $DB->fetch($result)) {
                                                $today_total_recharge += $row['money'];
                                            }
                                            echo $today_total_recharge . "元";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-ok"></i> 昨日订单:
                                        <span class="badge label label-black">
                                            <?php
                                            $yesterday = date('Y-m-d', strtotime("-1 day"));
                                            $yesterday_orders = $DB->count("SELECT COUNT(*) FROM qingka_wangke_order WHERE DATE(addtime) = '$yesterday'");
                                            echo $yesterday_orders . "条";
                                            ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="glyphicon glyphicon-ok"></i> 最近7天内总订单:
                                        <span class="badge label label-black">
                                            <?php
                                            $seven_days_ago = date('Y-m-d', strtotime("-7 days"));
                                            $last_7_days_orders = $DB->count("SELECT COUNT(*) FROM qingka_wangke_order WHERE DATE(addtime) >= '$seven_days_ago'");
                                            echo $last_7_days_orders . "条";
                                            ?>
                                        </span>
                                    </li>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card" style="box-shadow: 18px 18px 30px #d1d9e6, -18px -18px 30px #fff;border-radius: 8px;">
                    <div class="panel-heading font-bold bg-white">巅峰排行</div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <tbody>
                                    <ul class="list-group">
                                        <?php
                                        $query = "SELECT u.uid AS uid, COUNT(o.uid) AS order_count FROM qingka_wangke_order AS o JOIN qingka_wangke_user AS u ON o.uid = u.uid WHERE YEARWEEK(o.addtime, 1) = YEARWEEK(NOW(), 1) GROUP BY o.uid ORDER BY order_count DESC LIMIT 10";
                                        $result = $DB->query($query);
                                        $rank = 1;
                                        while ($row = $DB->fetch($result)) {
                                            $uid = $row['uid'];
                                            $orderCount = $row['order_count'];
                                            echo '<li class="list-group-item">排名: ' . $rank . ' UID: ' . $uid . '（订单数量：' . $orderCount . '单）</li>';
                                            $rank++;
                                        }
                                        ?>
                                    </ul>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card" style="box-shadow: 18px 18px 30px #d1d9e6, -18px -18px 30px #fff;border-radius: 8px;">
                    <div class="panel-heading font-bold bg-white">单个统计</div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>平台名称</th>
                                        <th>今日单量</th>
                                        <th>总单量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $result = $DB->query("SELECT cid, name FROM qingka_wangke_class WHERE status != 0 ORDER BY cid");
                                    while ($rs = $DB->fetch($result)) {
                                        $count1 = $DB->count("SELECT COUNT(*) FROM qingka_wangke_order WHERE cid = '{$rs['cid']}' AND addtime > '$jtdate'");
                                        $count2 = $DB->count("SELECT COUNT(*) FROM qingka_wangke_order WHERE cid = '{$rs['cid']}'");
                                        echo "<tr>
                                                <td>" . $rs['cid'] . "</td>
                                                <td>" . $rs['name'] . "</td>
                                                <td>" . $count1 . "</td>
                                                <td>" . $count2 . "</td>
                                              </tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row"></div>
    </div>
</div>
<?php require_once("footer.php"); ?>
</body>
</html>