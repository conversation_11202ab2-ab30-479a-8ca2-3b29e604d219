<?php
$title='编辑分类';
require_once('head.php');
if($userrow['uid']!=1){
setcookie("admin_token", "", time() - 90000, "/", "", $secure, true);
exit("<script language='javascript'>window.location.href='login.php';</script>");}
$uid=$_GET['uid'];
?>
     <div class="app-content-body ">
        <div class="wrapper-md control" id="orderlist">
        
    <div class="panel panel-default" >
    <div class="panel-heading font-bold layui-bg-black">分类列表&nbsp;<button class="btn btn-xs btn-light" data-toggle="modal" data-target="#modal-add">添加</button></div>
 <div class="panel-body">
      <div class="table-responsive">
        <table class="table table-striped">
          <thead><tr><th>ID</th><th>排序</th><th>分类名称</th><th>分类介绍</th><th>状态</th><th>添加时间</th><th>操作</th></tr></thead>
          <tbody>
            <tr v-for="res in row.data">
             <td>{{res.id}}</td>
             <td>{{res.sort}}</td>
             <td>{{res.name}}</td>
             <td>{{res.text}}</td>
             <td><span class="btn btn-xs btn-success" v-if="res.status==1">已启用</span><span class="btn btn-xs btn-danger" v-else-if="res.status==0" @click="ktapi(res.uid)">未启用</span></td>
             <td>{{res.time}}</td>
             <td><button class="btn btn-xs btn-primary" data-toggle="modal" data-target="#modal-update" @click="upm=res">编辑</button>
             &nbsp;<button class="btn btn-xs btn-danger" @click="del(res.id)">删除</button>
             </td>
               </tr>
          </tbody>
        </table>
      </div>
     
     <ul class="pagination" v-if="row.last_page>1">
         <li class="disabled"><a @click="get(1)">首页</a></li>
         <li class="disabled"><a @click="row.current_page>1?get(row.current_page-1):''">&laquo;</a></li>
             <li @click="get(row.current_page-3)" v-if="row.current_page-3>=1"><a>{{ row.current_page-3 }}</a></li>
    <li @click="get(row.current_page-2)" v-if="row.current_page-2>=1"><a>{{ row.current_page-2 }}</a></li>
    <li @click="get(row.current_page-1)" v-if="row.current_page-1>=1"><a>{{ row.current_page-1 }}</a></li>
    <li :class="{'active':row.current_page==row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
    <li @click="get(row.current_page+1)" v-if="row.current_page+1<=row.last_page"><a>{{ row.current_page+1 }}</a></li>
    <li @click="get(row.current_page+2)" v-if="row.current_page+2<=row.last_page"><a>{{ row.current_page+2 }}</a></li>
    <li @click="get(row.current_page+3)" v-if="row.current_page+3<=row.last_page"><a>{{ row.current_page+3 }}</a></li>
         <li class="disabled"><a @click="row.last_page>row.current_page?get(row.current_page+1):''">&raquo;</a></li>
         <li class="disabled"><a @click="get(row.last_page)">尾页</a></li>
     </ul>
    </div>
  </div>
 
 
 
        <div class="modal fade primary" id="modal-update">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span>
                        </button>
                        <h4 class="modal-title">分类修改</h4>
                    </div>
          
                    <div class="modal-body">
                        <form class="form-horizontal" id="form-update">
                            <input type="hidden" name="action" value="update"/>
                            <div class="form-group">
                               <label class="col-sm-3 control-label">排序</label>
                                <div class="col-sm-9">
                                  <input type="text" v-model="upm.sort" class="form-control" :value="upm.sort">
                               </div>
                            </div>
                            <div class="form-group">
                               <label class="col-sm-3 control-label">分类名称</label>
                                <div class="col-sm-9">
                                  <input type="text" v-model="upm.name" class="form-control" :value="upm.name" >
                               </div>
                            </div>
                            <div class="form-group">
                               <label class="col-sm-3 control-label">分类介绍</label>
                                <div class="col-sm-9">
                                  <input type="text" v-model="upm.text" class="form-control">
                               </div>
                            </div>
                            <div class="form-group">
                             <label class="col-sm-3 control-label">状态</label>
                             <div class="col-sm-9">
                            <select v-model="upm.status" :value="upm.status" class="layui-select" style="background: url('../index/arrow.png')no-repeat scroll 99%;width:100%">
                                <option value="1">启用</option>
                                <option value="0">关闭</option>
                            </select>
                            </div>
                            </div>
                         </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" data-dismiss="modal" @click="up_m">确定</button>
                    </div>
                </div>
            </div>
        </div>
 
 
        <div class="modal fade primary" id="modal-add">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span>
                        </button>
                        <h4 class="modal-title">分类添加</h4>
                    </div>
          
                    <div class="modal-body">
                        <form class="form-horizontal" id="form-add">
                            <input type="hidden" name="action" value="add"/>
                            <div v-for="(category, index) in addm.categories" :key="index" class="form-group">
                                <div class="col-sm-12">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <label class="control-label">排序</label>
                                            <input type="text" v-model="category.sort" class="form-control" placeholder="请输入排序">
                                        </div>
                                        <div class="col-sm-4">
                                            <label class="control-label">分类名称</label>
                                            <input type="text" v-model="category.name" class="form-control" placeholder="请输入分类名称">
                                        </div>
                                        <div class="col-sm-3">
                                            <label class="control-label">分类介绍</label>
                                            <input type="text" v-model="category.text" class="form-control" placeholder="请输入分类介绍">
                                        </div>
                                        <div class="col-sm-1">
                                            <button type="button" class="btn btn-xs btn-danger" @click="removeCategory(index)" v-if="addm.categories.length > 1">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-xs btn-primary" @click="addCategory">添加一行</button>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" data-dismiss="modal" @click="add_m">确定</button>
                    </div>
                </div>
            </div>
        </div>
  <!-- 验证码输入模态框 -->
<div class="modal fade" id="modal-captcha" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">安全风险警告</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="captcha">管理二次验证</label>
                    <input type="text" class="form-control" id="captcha" v-model="captcha" placeholder="请输入验证码">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" @click="confirmDelete">确定</button>
            </div>
        </div>
    </div>
</div>
 
 
 
    </div>
   </div>
 </div>
<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>
<script>
vm = new Vue({
    el: "#orderlist",
    data: {
        row: null,
        uid: "<?php echo $uid ?>",
        storeInfo: {},
        addm: {
            uid: "<?php echo $uid ?>",
            categories: [{ sort: '', name: '', text: '' }]
        },
        upm: {},
        captcha: '',
        deleteId: null
    },
    methods: {
        get: function(page) {
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=fllist", { uid: this.uid, page: page }, { emulateJSON: true }).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    this.row = data.body;
                } else {
                    layer.msg(data.data.msg, { icon: 2 });
                }
            });
        },
        addCategory: function() {
            this.addm.categories.push({ sort: '', name: '', text: '' });
        },
        removeCategory: function(index) {
            this.addm.categories.splice(index, 1);
        },
        add_m: function() {
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=fl", { data: this.addm.categories, active: 1 }, { emulateJSON: true }).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    vm.get(1);
                    layer.msg(data.data.msg, { icon: 1 });
                    this.addm.categories = [{ sort: '', name: '', text: '' }]; // Reset form
                } else {
                    layer.msg(data.data.msg, { icon: 2 });
                }
            });
        },
        up_m: function() {
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=fl", { data: this.upm, active: 2 }, { emulateJSON: true }).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    vm.get(1);
                    layer.msg(data.data.msg, { icon: 1 });
                } else {
                    layer.msg(data.data.msg, { icon: 2 });
                }
            });
        },
        del: function(id) {
            this.deleteId = id;
            $('#modal-captcha').modal('show');
        },
        confirmDelete: function() {
            if (!this.captcha) {
                layer.msg('请输入验证码', { icon: 2 });
                return;
            }
            var load = layer.load(2);
            this.$http.post("/apiadmin.php?act=fl_del", { id: this.deleteId, authcode: this.captcha }, { emulateJSON: true }).then(function(data) {
                layer.close(load);
                if (data.data.code == 1) {
                    vm.get(1);
                    layer.msg(data.data.msg, { icon: 1 });
                    $('#modal-captcha').modal('hide');
                    this.captcha = '';
                } else {
                    layer.msg(data.data.msg, { icon: 2 });
                }
            });
        }
    },
    mounted() {
        this.get(1);
    }
});
</script>