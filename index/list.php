<?php
$mod='blank';
$title='订单列表';
$nsgsb='123';
require_once('head.php');
?>
     <div class="app-content-body ">
        <div class="wrapper-md control">
        <div class="panel panel-default" id="orderlist" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
            <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">订单列表</div>
                 <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                 	<div class="form-horizontal devform">
                 	    <!-- 移动端搜索区域收起/展开控制 -->
                 	    <div class="mobile-search-toggle" @click="toggleMobileSearch">
                 	        <i class="el-icon-search"></i>
                 	        <span>搜索筛选</span>
                 	        <i :class="mobileSearchExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                 	    </div>

                 	    <div class="search-form-container" :class="{'mobile-collapsed': !mobileSearchExpanded}">
                 		   <div class="el-form layui-row layui-col-space10">
    <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
    <el-select id="select" v-model="cx.cid" filterable placeholder="项目名称" style="width:100%">
        <el-option label="项目名称" value=""></el-option>
        <?php
            $a = $DB->query("select * from qingka_wangke_class where status!=2 ");
            while ($row = $DB->fetch($a)) {
                // 移除名称截断，让CSS处理显示
                echo '<el-option label="' . htmlspecialchars($row['name']) . '" value="' . $row['cid'] . '"></el-option>';
            }
        ?>
    </el-select>
</div>
                              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">	
                 		   				<el-select id="select" v-model="cx.status_text" filterable placeholder="订单状态" style="width:100%">
                 		   				    <el-option label="订单状态" value=""></el-option>
                 		   				    <el-option label="待处理" value="待处理"></el-option>
                 		   				    <el-option label="进行中" value="进行中"></el-option>
                 		   				    <el-option label="已完成" value="已完成"></el-option>
                 		   				    <el-option label="补刷中" value="补刷中"></el-option>
                 		   				    <el-option label="异常" value="异常"></el-option>
                 		   				</el-select>	 
                              </div>
                              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">	
                 		   				<el-select id="select" v-model="cx.dock" filterable placeholder="处理状态" style="width:100%">
                 		   				    <el-option label="提交状态" value=""></el-option>
                 		   				    <el-option label="提交成功" value="1"></el-option>
                 		   				    <el-option label="等待提交" value="0"></el-option>
                 		   				    <el-option label="提交失败" value="2"></el-option>
                 		   				    <el-option label="重复提交" value="3"></el-option>
                 		   				    <el-option label="已经取消" value="4"></el-option>
                 		   				    <el-option label="提交中断" value="5"></el-option>
                 		   				</el-select>	 
                                </div>
                             <div class="layui-col-md3 layui-col-sm3 layui-col-xs9">
                            <el-date-picker
                                v-model="cx.dateRange"
                                type="datetimerange"
                                range-separator="~"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                style="width:100%"
                            >
                                        </el-date-picker>
                                 </div>
                                <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">	
                 		   				<el-select id="select" v-model="cx.pagesize" filterable placeholder="50条/页" style="width:100%">
                 		   				    <el-option label="50条/页" value="50"></el-option>
                 		   				    <el-option label="100条/页" value="100"></el-option>
                 		   				    <el-option label="200条/页" value="200"></el-option>
                 		   				    <el-option label="300条/页" value="300"></el-option>
                 		   				    <el-option label="400条/页" value="400"></el-option>
                 		   				    <el-option label="1000条/页（慎用）" value="1000"></el-option>
                 		   				    <el-option v-if="row.uid==1" label="5000条/页" value="5000"></el-option>
                 		   				</el-select>	 
                                    </div>
                            </div>
                        <div class="form-horizontal devform">	
                 	          <div class="layui-row layui-col-space10">
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">	
                 		   				<el-select id="select" v-model="cx.hid" filterable placeholder="所属渠道" style="width:100%">
        <el-option label="所属渠道" value=""></el-option>
        <?php
            $a = $DB->query("select * from qingka_wangke_huoyuan where status=1 ");
            while ($row = $DB->fetch($a)) {
                $shortName = strlen($row['name']) > 50 ? substr($row['name'], 0, 50) . '...' : $row['name'];
                echo '<el-option label="' . $shortName . '" value="' . $row['hid'] . '"></el-option>';
            }
        ?>
    </el-select>
</div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.school" placeholder="请输入学校关键字"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.qq" placeholder="请输入下单账号"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.status_text" placeholder="请输入状态关键字"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6">
                 	                  <el-input v-model="cx.kcname" placeholder="请输入下单课程名称"></el-input>
                 	              </div>
                 	              </div>
                 	              </div>
                 	          <div class="form-horizontal devform">	
                 	          <div class="layui-row layui-col-space10">
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.oid" placeholder="请输入订单ID"></el-input>
                 	              </div>
                 	               <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.mima" placeholder="请输入密码"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.remarks" placeholder="请输入进度"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3 layui-col-xs6" v-if="row.uid==1">
                 	                  <el-input v-model="cx.uid" placeholder="请输入用户UID"></el-input>
                 	              </div>
                 	              <div class="layui-col-md2 layui-col-sm3  layui-col-xs6" >
                                    <el-button type="primary" size="medium" icon="el-icon-search" value="查询" @click="get(1)" >查询</el-button>&nbsp;
                                    <el-button type="primary" size="medium" icon="el-icon-printer" value="导出" @click="showExportDialog" >导出</el-button>
                                 </div>
                         </div>
                         </div>
                      </div>
                      </div> <!-- 关闭 search-form-container -->
                      <br>
                        <div >
                            <el-collapse>
                                <el-collapse-item>
                                    <template slot="title"><i class="header-icon el-icon-edit"></i>&nbsp;修改任务显示状态</template>
                                    <div>
                                    <el-button type="warning" size="mini" icon="el-icon-time" @click="status_text('待处理')">待处理</el-button>
                                    <el-button type="success" size="mini" icon="el-icon-circle-check" @click="status_text('已完成')">已完成</el-button>
                                    <el-button type="primary" size="mini" icon="el-icon-loading" @click="status_text('进行中')">进行中</el-button>
                                    <el-button type="danger" size="mini" icon="el-icon-warning-outline" @click="status_text('异常')">异常</el-button>
                                    <el-button size="mini" icon="el-icon-circle-close" @click="status_text('已取消')">已取消</el-button>
                                </div>
                              </el-collapse-item>
                              <div v-if="row.uid==1" >
                              <el-collapse-item>
                                    <template slot="title"><i class="header-icon el-icon-edit"></i>&nbsp;处理状态操作</template>
                                        <div>
                                            <el-button type="warning" size="mini" @click="dock(0)">待处理</el-button>
                                            <el-button type="success" size="mini" @click="dock(1)">处理成功</el-button>
                                            <el-button type="danger" size="mini" @click="dock(2)">处理失败</el-button>
                                            <el-button type="info" size="mini" @click="dock(3)">重复下单</el-button>
                                            <el-button type="default" size="mini" @click="dock(4)">已取消</el-button>
                                            <el-button type="default" size="mini" @click="dock(99)">自营订单</el-button>
                                            <el-button type="danger" size="mini" @click="tk(sex)">订单退款</el-button><br><br>
                                            <el-button type="danger" size="mini" @click="sc(sex)">订单删除</el-button>
                                        </div>
                                </el-collapse-item>
                            </el-collapse>
                        </div>

                        <!-- 移动端批量操作区域收起/展开控制 -->
                        <div class="mobile-batch-toggle" @click="toggleMobileBatch">
                            <i class="el-icon-setting"></i>
                            <span>批量操作</span>
                            <i :class="mobileBatchExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                        </div>

                        <div class="batch-operations-container" :class="{'mobile-collapsed': !mobileBatchExpanded}">
                            <div >
                                <el-button size="mini" type="primary" @click="plzt(sex)" icon="el-icon-sort">批量更新</el-button>
                                <el-button size="mini" type="warning" @click="plbs(sex)" icon="el-icon-edit-outline">批量补单</el-button>
                                <el-button size="mini" type="info" @click="pltx(sex)" icon="el-icon-close">批量停止</el-button>
                                <el-button size="mini" type="danger" @click="plms(sex)" icon="el-icon-lightning">批量转秒</el-button>

                                <!-- 进度更新控制按钮 -->
                                <div style="margin-top: 10px; margin-bottom: 10px;">
                                    <el-button size="mini" type="success" @click="manualRefresh()" icon="el-icon-refresh">手动刷新页面</el-button>
                                    <el-button size="mini" type="primary" @click="updateProgressData()" icon="el-icon-refresh-right">更新进度</el-button>
                                    <el-button size="mini" :type="timer ? 'warning' : 'info'" @click="toggleAutoRefresh()" :icon="timer ? 'el-icon-video-pause' : 'el-icon-video-play'">
                                        {{ timer ? '停止自动更新' : '开启自动更新' }}
                                    </el-button>
                                    <el-button size="mini" type="default" @click="showRefreshSettings()" icon="el-icon-setting">设置间隔</el-button>
                                    <span v-if="timer" style="color: #67C23A; font-size: 12px; margin-left: 10px;">
                                        <i class="el-icon-time"></i> 进度自动更新中 ({{ autoRefreshInterval }}秒/次)
                                        <span v-if="lastRefreshTime" style="margin-left: 10px;">
                                            最后更新: {{ lastRefreshTime }}
                                        </span>
                                    </span>
                                </div>

                                <p>订单如有问题请自行检查后反馈即可,请不要重复下相同订单</p>
                            </div>
                        </div>
                                <!--
                                <a class="btn btn-xs btn btn-primary purple" id="checkboxAll" @click="selectAll()">全选</a>
                                <a class="btn btn-xs btn-info purple" @click="plzt(sex)"><i class="fa fa-send"></i>同步状态入队</a>
                                <a class="btn btn-xs btn-success purple" @click="plbs(sex)"><i class="fa fa-send"></i>补刷订单入队</a>-->
                            </div>
                          </el-collapse-item>
                        </el-collapse>
                      <br>
              <!-- 桌面端表格显示 -->
              <div class="desktop-table-view">
                <div class="el-table-column-fixed  table-responsive table-condensed" lay-size="sm" >
                  <table class="table table-striped">
                    <thead style="white-space:nowrap"><tr><!--<th>#</th>-->
                    	<th><label class="lyear-checkbox checkbox-inline checkbox-info">
                             <input type="checkbox" id="checkboxAll"  @click="selectAll()"><span></span>
                         </label>
                      </th>
                    	<th>操作</th>
                    	<th>详细</th>
                    	<th>订单所属平台</th>
                    	<th>账号</th>
                    	<th>备注</th>
                    	<th>任务名称</th>
                    	<th>状态</th>
                    	<th>%</th>
                    	<th>订单详细信息</th>
                    	<th>时间</th>
                    	<th v-if="row.uid==1">状态</th>
                    	<th v-if="row.uid==1">UID</th>
                    	<th v-if="row.uid==1">扣费</th>
                    	</tr></thead>
                    <tbody>
                      <!--<a class="btn btn-xs btn-dark purplek"@click="plbs('待重刷')" >批量重刷</a>-->
                      <tr v-for="res in row.data">
                                    <!--<td>
                                    	<span class="checkbox checkbox-success">
                            <input type="checkbox" id="checkboxAll" :value="res.oid" v-model="sex"><label for="checkbox1"></label></span>
                                    </td>-->
                   <td style="white-space:nowrap" >
                      <label class="lyear-checkbox checkbox-inline checkbox-info">
                      <input type="checkbox" id="checkboxAll" :value="res.oid" v-model="sex"><span v-if="row.uid==1"></span><span v-else>-</span>
                              </label>
                         </td>
  <td style="white-space:nowrap">
    <el-dropdown @command="handleCommand($event, res.oid)">
      <el-button type="primary" size="small" style="background-color: transparent; border: 1px solid #ccc; color: #6B6B6B;">
        操作<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="up">刷新</el-dropdown-item>
        <el-dropdown-item command="bs">补单</el-dropdown-item>
        <el-dropdown-item command="zt">停止</el-dropdown-item>
        <el-dropdown-item command="feedback">一键反馈</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </td>
                       <td><span class="layui-btn layui-btn-xs layui-btn-radius layui-btn-normal" @click="ddinfo(res)"><i class="layui-icon layui-icon-search"></i></span>
                      <span><button v-if="res.cid==12624" @click="jietu(res.user)" class="btn btn-xs btn-default" >截图</button></span>
                      <span><button v-if="res.cid==12624" @click="zhengshu(res.user)" class="btn btn-xs btn-primary" >证书</button></span>
                  </td>
                       <td style="white-space:nowrap, width: 300">{{res.ptname}}
                       <span v-if="res.miaoshua=='1'" style="color: red;">&nbsp;秒单</span>
                       </td>	            	      	
<td style="white-space:nowrap; width: 225">
  <button @click="copyToClipboard(res.school)" class="layui-btn layui-btn-xs  layui-btn-primary layui-border-black">复制学校</button>&nbsp;
  <span>{{ res.school }}</span><br>
  <button @click="copyToClipboard(res.user)" class="layui-btn layui-btn-xs  layui-btn-primary layui-border-black">复制账号</button>&nbsp;
  <span>{{ res.user }}</span><br>
  <button @click="copyToClipboard(res.pass)" class="layui-btn layui-btn-xs  layui-btn-primary layui-border-black">复制密码</button>&nbsp;
  <span>{{ res.pass }}</span><br>
  <button @click="copyAllToClipboard(res)" class="layui-btn layui-btn-xs layui-btn-primary layui-border-black">全部复制</button>&nbsp;
<span>备注：{{res.chapterCount}}</span>
</td>
                    	<td style="white-space:nowrap, width: 100">
                    	<span class="layui-btn layui-btn-xs layui-btn-radius layui-btn-normal" @click="tianjiabeizhu(res.oid)"><i class="layui-icon layui-icon-edit"></i></span>
                    	</td>   
                    	<td style="white-space:nowrap, width: 225">{{res.kcname}}</td>
                    	<td style="white-space:nowrap" @click="ddinfo(res)">
                    		<el-button v-if="res.status=='待处理'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待上号'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='排队中'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已暂停'" type="warning" size="mini" icon="el-icon-video-pause">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已停止'" type="warning" size="mini" icon="el-icon-error">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已完成'" type="success" size="mini" icon="el-icon-circle-check">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待更新'" type="warning" size="mini" icon="el-icon-download">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='异常'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='失败'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='密码错误'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已提取'" type="primary" size="mini" icon="el-icon-upload">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='已提交'" type="primary" size="mini" icon="el-icon-upload">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='进行中'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='上号中'" type="primary" size="mini" icon="el-icon-monitor">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='考试中'" type="primary" size="mini" icon="el-icon-edit-outline">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='队列中'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待考试'" type="primary" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='待重启'" type="primary" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='等待下周'" type="primary" size="mini" icon="el-icon-alarm-clock">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='请自行处理'" type="success" size="mini" icon="el-icon-question">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='平时分'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='平时分中'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button v-else-if="res.status=='补刷中'" type="info" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                    		<el-button  v-else style="color: white;"  type="info" size="mini" >{{res.status}}</el-button>
                    	</td>
                    	<td>{{res.process}}
                    	    <div class="layui-progress layui-progress-big" lay-showpercent="true">
                    	        <div class="layui-progress-bar layui-bg-blue"  lay-percent="res.process" v-bind:style="'width:'+(res.process)+';' ">
                    	        </div>
                    	    </div>
                    	    </td>
                    	<td style="white-space:nowrap, width: 400">{{res.remarks}}</td>         	
                    	<td style="white-space:nowrap, width: 180">{{res.addtime}}</td>
                    	<td style="white-space:nowrap" v-if="row.uid==1">
                    		<span @click="duijie(res.oid)" v-if="res.dockstatus==0" class="el-button el-button--warning el-button--mini">等待处理</span>
                    		<span v-if="res.dockstatus==1" class="el-button el-button--success el-button--mini">处理成功</span>
                    		<span @click="duijie(res.oid)" v-if="res.dockstatus==2" class="el-button el-button--danger el-button--mini">处理失败</span>
                    		<span v-if="res.dockstatus==3" class="el-button el-button--info el-button--mini">重复下单</span>
                    		<span v-if="res.dockstatus==4" class="el-button el-button--default is-plain el-button--mini">已取消</span>
                    		<span v-if="res.dockstatus==99" class="el-button el-button--default is-plain el-button--mini">自营订单</span></td>   
                        <td v-if="row.uid==1">{{res.uid}}</td>
                        <td style="white-space:nowrap" v-if="row.uid==1">{{res.fees}}</td>
                        </tr>
                    </tbody>
                  </table>
                  </div>
                </div>

                <!-- 移动端卡片式显示 -->
                <div class="mobile-card-view">
                  <div class="mobile-select-all">
                    <label class="lyear-checkbox checkbox-inline checkbox-info">
                      <input type="checkbox" id="checkboxAllMobile" @click="selectAll()"><span></span>
                      <span style="margin-left: 5px;">全选</span>
                    </label>
                  </div>

                  <div class="order-card" v-for="res in row.data" :key="res.oid">
                    <div class="card-header">
                      <div class="card-checkbox">
                        <label class="lyear-checkbox checkbox-inline checkbox-info">
                          <input type="checkbox" :value="res.oid" v-model="sex"><span></span>
                        </label>
                      </div>
                      <div class="card-title">
                        <span class="platform-name">{{res.ptname}}</span>
                        <span v-if="res.miaoshua=='1'" class="miao-tag">秒单</span>
                      </div>
                      <div class="card-actions">
                        <el-dropdown @command="handleCommand($event, res.oid)" size="mini">
                          <el-button type="primary" size="mini">
                            操作<i class="el-icon-arrow-down el-icon--right"></i>
                          </el-button>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="up">刷新</el-dropdown-item>
                            <el-dropdown-item command="bs">补单</el-dropdown-item>
                            <el-dropdown-item command="zt">停止</el-dropdown-item>
                            <el-dropdown-item command="feedback">一键反馈</el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                        <span class="detail-btn layui-btn layui-btn-xs layui-btn-radius layui-btn-normal" @click="ddinfo(res)">
                          <i class="layui-icon layui-icon-search"></i>
                        </span>
                      </div>
                    </div>

                    <div class="card-content">
                      <div class="info-row">
                        <div class="info-label">📚 课程名称:</div>
                        <div class="info-value course-name">{{res.kcname}}</div>
                      </div>

                      <div class="info-row">
                        <div class="info-label">👤 账号信息:</div>
                        <div class="info-value account-info">
                          <div class="account-item">
                            <button @click="copyToClipboard(res.school)" class="copy-btn">复制</button>
                            <span class="account-text">{{res.school}}</span>
                          </div>
                          <div class="account-item">
                            <button @click="copyToClipboard(res.user)" class="copy-btn">复制</button>
                            <span class="account-text">{{res.user}}</span>
                          </div>
                          <div class="account-item">
                            <button @click="copyToClipboard(res.pass)" class="copy-btn">复制</button>
                            <span class="account-text">{{res.pass}}</span>
                          </div>
                          <button @click="copyAllToClipboard(res)" class="copy-all-btn">全部复制</button>
                        </div>
                      </div>

                      <div class="info-row">
                        <div class="info-label">📊 状态:</div>
                        <div class="info-value">
                          <el-button v-if="res.status=='待处理'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='待上号'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='排队中'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='已暂停'" type="warning" size="mini" icon="el-icon-video-pause">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='已停止'" type="warning" size="mini" icon="el-icon-error">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='已完成'" type="success" size="mini" icon="el-icon-circle-check">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='待更新'" type="warning" size="mini" icon="el-icon-download">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='异常'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='失败'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='密码错误'" type="danger" size="mini" icon="el-icon-warning-outline">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='已提取'" type="primary" size="mini" icon="el-icon-upload">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='已提交'" type="primary" size="mini" icon="el-icon-upload">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='进行中'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='上号中'" type="primary" size="mini" icon="el-icon-monitor">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='考试中'" type="primary" size="mini" icon="el-icon-edit-outline">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='队列中'" type="warning" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='待考试'" type="primary" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='待重启'" type="primary" size="mini" icon="el-icon-time">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='等待下周'" type="primary" size="mini" icon="el-icon-alarm-clock">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='请自行处理'" type="success" size="mini" icon="el-icon-question">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='平时分'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='平时分中'" type="primary" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                          <el-button v-else-if="res.status=='补刷中'" type="info" size="mini" icon="el-icon-loading">{{res.status}}</el-button>
                          <el-button v-else style="color: white;" type="info" size="mini">{{res.status}}</el-button>
                        </div>
                      </div>

                      <div class="info-row">
                        <div class="info-label">⚡ 进度:</div>
                        <div class="info-value progress-info">
                          <span class="progress-text">{{res.process}}</span>
                          <div class="progress-bar-container">
                            <div class="progress-bar" :style="'width:' + res.process"></div>
                          </div>
                        </div>
                      </div>

                      <div class="info-row" v-if="res.remarks">
                        <div class="info-label">📝 备注:</div>
                        <div class="info-value remarks">{{res.remarks}}</div>
                      </div>

                      <div class="info-row">
                        <div class="info-label">🕒 时间:</div>
                        <div class="info-value time">{{res.addtime}}</div>
                      </div>

                      <div class="info-row" v-if="row.uid==1">
                        <div class="info-label">处理状态:</div>
                        <div class="info-value">
                          <span @click="duijie(res.oid)" v-if="res.dockstatus==0" class="status-btn status-warning">等待处理</span>
                          <span v-if="res.dockstatus==1" class="status-btn status-success">处理成功</span>
                          <span @click="duijie(res.oid)" v-if="res.dockstatus==2" class="status-btn status-danger">处理失败</span>
                          <span v-if="res.dockstatus==3" class="status-btn status-info">重复下单</span>
                          <span v-if="res.dockstatus==4" class="status-btn status-default">已取消</span>
                          <span v-if="res.dockstatus==99" class="status-btn status-default">自营订单</span>
                        </div>
                      </div>

                      <div class="info-row" v-if="row.uid==1">
                        <div class="info-label">UID:</div>
                        <div class="info-value">{{res.uid}}</div>
                      </div>

                      <div class="info-row" v-if="row.uid==1">
                        <div class="info-label">扣费:</div>
                        <div class="info-value">{{res.fees}}</div>
                      </div>
                    </div>
                  </div>
                </div>
                 <ul class="pagination no-border" v-if="row.last_page>0" > 
                     <li><span>共 {{ row.total_count }} 条</span></li>
                     <li><a @click="get(1)">首页</a></li>
                     <li><a @click="row.current_page>1?get(row.current_page-1):''">&laquo;</a></li>
                     <li  @click="get(row.current_page-3)" v-if="row.current_page-3>=1"><a>{{ row.current_page-3 }}</a></li>
                     <li  @click="get(row.current_page-2)" v-if="row.current_page-2>=1"><a>{{ row.current_page-2 }}</a></li>
                     <li  @click="get(row.current_page-1)" v-if="row.current_page-1>=1"><a>{{ row.current_page-1 }}</a></li>
                     <li :class="{'active':row.current_page==row.current_page}" @click="get(row.current_page)" v-if="row.current_page"><a>{{ row.current_page }}</a></li>
                     <li  @click="get(row.current_page+1)" v-if="row.current_page+1<=row.last_page"><a>{{ row.current_page+1 }}</a></li>
                     <li  @click="get(row.current_page+2)" v-if="row.current_page+2<=row.last_page"><a>{{ row.current_page+2 }}</a></li>
                     <li  @click="get(row.current_page+3)" v-if="row.current_page+3<=row.last_page"><a>{{ row.current_page+3 }}</a></li>		       			     
                     <li><a @click="row.last_page>row.current_page?get(row.current_page+1):''">&raquo;</a></li>
                     <li><a @click="get(row.last_page)">尾页</a></li>
                 </ul>

<div id="exportDialog" style="display: none; padding: 20px;">
    <div style="margin-bottom: 15px;">
        <label>导出格式：</label>
        <el-radio-group v-model="exportFormat">
            <el-radio label="txt">TXT文本</el-radio>
            <el-radio label="excel">Excel文件</el-radio>
        </el-radio-group>
    </div>
    <div style="margin-bottom: 15px;">
        <label>选择字段：</label>
        <el-checkbox-group v-model="exportFields">
            <el-checkbox label="school">学校</el-checkbox>
            <el-checkbox label="user">账号</el-checkbox>
            <el-checkbox label="pass">密码</el-checkbox>
            <el-checkbox label="kcname">课程名</el-checkbox>
            <el-checkbox label="status">状态</el-checkbox>
            <el-checkbox label="process">进度</el-checkbox>
            <el-checkbox label="remarks">备注</el-checkbox>
            <el-checkbox label="fees" v-if="row.uid==1">扣费</el-checkbox>
        </el-checkbox-group>
    </div>
    <div style="text-align: center; margin-top: 20px;">
        <el-button type="primary" @click="doExport" :disabled="exportFields.length === 0">确认导出</el-button>
        <el-button @click="closeExportDialog">取消</el-button>
    </div>
</div>

                <!-- 桌面端订单详情弹窗 -->
                <div id="ddinfo2-desktop" style="display: none;" class="desktop-order-detail">
                    <div class="desktop-detail-container">
                        <!-- 订单头部信息 -->
                        <div class="desktop-detail-header">
                            <div class="desktop-header-content">
                                <div class="desktop-platform-info">
                                    <h2 class="desktop-platform-name">{{ddinfo3.info.ptname}}</h2>
                                    <span v-if="ddinfo3.info.miaoshua=='1'" class="desktop-speed-badge">⚡ 秒刷</span>
                                </div>
                                <div class="desktop-order-meta">
                                    <span class="desktop-order-id">订单 #{{ddinfo3.info.oid}}</span>
                                    <span class="desktop-order-time">{{ddinfo3.info.addtime}}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 状态和进度区域 -->
                        <div class="desktop-status-progress-section">
                            <div class="desktop-status-card">
                                <div class="desktop-card-icon desktop-status-icon">
                                    <i class="el-icon-info"></i>
                                </div>
                                <div class="desktop-card-content">
                                    <div class="desktop-card-label">订单状态</div>
                                    <div class="desktop-status-display">
                                        <span class="desktop-status-badge" :class="getStatusClass(ddinfo3.info.status)">
                                            {{ddinfo3.info.status}}
                                        </span>
                                        <button v-if="ddinfo3.info.dockstatus!='99'" @click="up(ddinfo3.info.oid)" class="desktop-refresh-btn">
                                            <i class="el-icon-refresh"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="desktop-progress-card">
                                <div class="desktop-card-icon desktop-progress-icon">
                                    <i class="el-icon-pie-chart"></i>
                                </div>
                                <div class="desktop-card-content">
                                    <div class="desktop-card-label">完成进度</div>
                                    <div class="desktop-progress-display">
                                        <div class="desktop-progress-bar-wrapper">
                                            <div class="desktop-progress-bar-track">
                                                <div class="desktop-progress-bar-fill" :style="'width:' + ddinfo3.info.process"></div>
                                            </div>
                                            <span class="desktop-progress-percentage">{{ddinfo3.info.process}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主要信息区域 -->
                        <div class="desktop-main-info-section">
                            <!-- 课程信息卡片 -->
                            <div class="desktop-info-card">
                                <div class="desktop-card-header">
                                    <div class="desktop-card-icon">
                                        <i class="el-icon-reading"></i>
                                    </div>
                                    <h3 class="desktop-card-title">课程信息</h3>
                                </div>
                                <div class="desktop-card-body">
                                    <div class="desktop-info-row">
                                        <span class="desktop-info-label">课程名称</span>
                                        <span class="desktop-info-value desktop-course-name">{{ddinfo3.info.kcname}}</span>
                                    </div>
                                    <div class="desktop-info-row" v-if="ddinfo3.info.name && ddinfo3.info.name!='null'">
                                        <span class="desktop-info-label">学生姓名</span>
                                        <span class="desktop-info-value">{{ddinfo3.info.name}}</span>
                                    </div>
                                    <div class="desktop-info-row" v-if="ddinfo3.info.kcid">
                                        <span class="desktop-info-label">课程ID</span>
                                        <span class="desktop-info-value">{{ddinfo3.info.kcid}}</span>
                                    </div>
                                    <div class="desktop-info-row" v-if="ddinfo3.info.yid">
                                        <span class="desktop-info-label">上游订单ID</span>
                                        <span class="desktop-info-value">{{ddinfo3.info.yid}}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 账号信息卡片 -->
                            <div class="desktop-info-card">
                                <div class="desktop-card-header">
                                    <div class="desktop-card-icon">
                                        <i class="el-icon-user"></i>
                                    </div>
                                    <h3 class="desktop-card-title">账号信息</h3>
                                </div>
                                <div class="desktop-card-body">
                                    <div class="desktop-account-info-grid">
                                        <div class="desktop-account-item">
                                            <div class="desktop-account-row">
                                                <span class="desktop-account-label">学校</span>
                                                <div class="desktop-account-value-group">
                                                    <span class="desktop-account-value">{{ddinfo3.info.school}}</span>
                                                    <button @click="copyToClipboard(ddinfo3.info.school)" class="desktop-copy-btn">
                                                        <i class="el-icon-document-copy"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="desktop-account-item">
                                            <div class="desktop-account-row">
                                                <span class="desktop-account-label">账号</span>
                                                <div class="desktop-account-value-group">
                                                    <span class="desktop-account-value">{{ddinfo3.info.user}}</span>
                                                    <button @click="copyToClipboard(ddinfo3.info.user)" class="desktop-copy-btn">
                                                        <i class="el-icon-document-copy"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="desktop-account-item">
                                            <div class="desktop-account-row">
                                                <span class="desktop-account-label">密码</span>
                                                <div class="desktop-account-value-group">
                                                    <span class="desktop-account-value">{{ddinfo3.info.pass}}</span>
                                                    <button @click="copyToClipboard(ddinfo3.info.pass)" class="desktop-copy-btn">
                                                        <i class="el-icon-document-copy"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="desktop-copy-all-section">
                                        <button @click="copyAllAccountInfo(ddinfo3.info)" class="desktop-copy-all-btn">
                                            <i class="el-icon-files"></i>
                                            <span>复制全部账号信息</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 时间信息区域 -->
                        <div class="desktop-time-info-section" v-if="ddinfo3.info.courseStartTime || ddinfo3.info.courseEndTime || ddinfo3.info.examStartTime || ddinfo3.info.examEndTime">
                            <div class="desktop-info-card">
                                <div class="desktop-card-header">
                                    <div class="desktop-card-icon">
                                        <i class="el-icon-time"></i>
                                    </div>
                                    <h3 class="desktop-card-title">时间信息</h3>
                                </div>
                                <div class="desktop-card-body">
                                    <div class="desktop-time-grid">
                                        <div class="desktop-time-item" v-if="ddinfo3.info.courseStartTime">
                                            <div class="desktop-time-label">课程开始</div>
                                            <div class="desktop-time-value">{{ddinfo3.info.courseStartTime}}</div>
                                        </div>
                                        <div class="desktop-time-item" v-if="ddinfo3.info.courseEndTime">
                                            <div class="desktop-time-label">课程结束</div>
                                            <div class="desktop-time-value">{{ddinfo3.info.courseEndTime}}</div>
                                        </div>
                                        <div class="desktop-time-item" v-if="ddinfo3.info.examStartTime">
                                            <div class="desktop-time-label">考试开始</div>
                                            <div class="desktop-time-value">{{ddinfo3.info.examStartTime}}</div>
                                        </div>
                                        <div class="desktop-time-item" v-if="ddinfo3.info.examEndTime">
                                            <div class="desktop-time-label">考试结束</div>
                                            <div class="desktop-time-value">{{ddinfo3.info.examEndTime}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注信息 -->
                        <div class="desktop-remarks-section" v-if="ddinfo3.info.remarks">
                            <div class="desktop-info-card">
                                <div class="desktop-card-header">
                                    <div class="desktop-card-icon">
                                        <i class="el-icon-edit-outline"></i>
                                    </div>
                                    <h3 class="desktop-card-title">备注信息</h3>
                                </div>
                                <div class="desktop-card-body">
                                    <div class="desktop-remarks-content">{{ddinfo3.info.remarks}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->
                        <div class="desktop-action-section" v-if="ddinfo3.info.dockstatus!='4'">
                            <div class="desktop-action-buttons">
                                <button @click="up(ddinfo3.info.oid)" class="desktop-action-btn desktop-refresh">
                                    <i class="el-icon-refresh"></i>
                                    <span>刷新状态</span>
                                </button>
                                <button @click="bs(ddinfo3.info.oid)" class="desktop-action-btn desktop-primary">
                                    <i class="el-icon-refresh-right"></i>
                                    <span>补单</span>
                                </button>
                                <button @click="ms(ddinfo3.info.oid)" class="desktop-action-btn desktop-warning">
                                    <i class="el-icon-lightning"></i>
                                    <span>秒刷</span>
                                </button>
                                <button @click="xgmm(ddinfo3.info.oid)" class="desktop-action-btn desktop-info">
                                    <i class="el-icon-edit"></i>
                                    <span>改密码</span>
                                </button>
                                <button @click="quxiao(ddinfo3.info.oid)" class="desktop-action-btn desktop-danger">
                                    <i class="el-icon-close"></i>
                                    <span>取消订单</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 移动端订单详情弹窗 -->
                <div id="ddinfo2-mobile" style="display: none;" class="mobile-order-detail">
                    <div class="mobile-detail-container">
                        <!-- 订单头部信息 -->
                        <div class="mobile-detail-header">
                            <div class="mobile-header-left">
                                <h3 class="mobile-order-title">{{ddinfo3.info.ptname}}</h3>
                                <span v-if="ddinfo3.info.miaoshua=='1'" class="mobile-speed-tag">⚡ 秒刷</span>
                            </div>
                            <div class="mobile-header-right">
                                <span class="mobile-order-id">#{{ddinfo3.info.oid}}</span>
                            </div>
                        </div>

                        <!-- 移动端状态和进度区域 -->
                        <div class="mobile-status-progress-section">
                            <div class="mobile-status-info">
                                <div class="mobile-status-label">订单状态</div>
                                <div class="mobile-status-value">
                                    <span class="mobile-status-badge" :class="getStatusClass(ddinfo3.info.status)">
                                        {{ddinfo3.info.status}}
                                    </span>
                                    <button v-if="ddinfo3.info.dockstatus!='99'" @click="up(ddinfo3.info.oid)" class="mobile-refresh-btn">
                                        <i class="el-icon-refresh"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mobile-progress-info">
                                <div class="mobile-progress-label">完成进度</div>
                                <div class="mobile-progress-container">
                                    <div class="mobile-progress-bar-modern">
                                        <div class="mobile-progress-fill" :style="'width:' + ddinfo3.info.process"></div>
                                    </div>
                                    <span class="mobile-progress-text">{{ddinfo3.info.process}}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 移动端课程信息区域 -->
                        <div class="mobile-info-section">
                            <h4 class="mobile-section-title">📚 课程信息</h4>
                            <div class="mobile-info-grid">
                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">课程名称</div>
                                    <div class="mobile-info-value">{{ddinfo3.info.kcname}}</div>
                                </div>
                                <div class="mobile-info-item" v-if="ddinfo3.info.name!='null'">
                                    <div class="mobile-info-label">学生姓名</div>
                                    <div class="mobile-info-value">{{ddinfo3.info.name}}</div>
                                </div>
                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">课程ID</div>
                                    <div class="mobile-info-value">{{ddinfo3.info.kcid}}</div>
                                </div>
                                <div class="mobile-info-item">
                                    <div class="mobile-info-label">上游YID</div>
                                    <div class="mobile-info-value">{{ddinfo3.info.yid}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 移动端账号信息区域 -->
                        <div class="mobile-info-section">
                            <h4 class="mobile-section-title">👤 账号信息</h4>
                            <div class="mobile-account-detail">
                                <div class="mobile-account-item">
                                    <span class="mobile-account-label">学校:</span>
                                    <span class="mobile-account-value">{{ddinfo3.info.school}}</span>
                                    <button @click="copyToClipboard(ddinfo3.info.school)" class="mobile-copy-btn-small">复制</button>
                                </div>
                                <div class="mobile-account-item">
                                    <span class="mobile-account-label">账号:</span>
                                    <span class="mobile-account-value">{{ddinfo3.info.user}}</span>
                                    <button @click="copyToClipboard(ddinfo3.info.user)" class="mobile-copy-btn-small">复制</button>
                                </div>
                                <div class="mobile-account-item">
                                    <span class="mobile-account-label">密码:</span>
                                    <span class="mobile-account-value">{{ddinfo3.info.pass}}</span>
                                    <button @click="copyToClipboard(ddinfo3.info.pass)" class="mobile-copy-btn-small">复制</button>
                                </div>
                                <button @click="copyAllAccountInfo(ddinfo3.info)" class="mobile-copy-all-btn-detail">
                                    <i class="el-icon-document-copy"></i> 复制全部账号信息
                                </button>
                            </div>
                        </div>

                        <!-- 移动端时间信息区域 -->
                        <div class="mobile-info-section">
                            <h4 class="mobile-section-title">🕒 时间信息</h4>
                            <div class="mobile-time-grid">
                                <div class="mobile-time-item">
                                    <div class="mobile-time-label">下单时间</div>
                                    <div class="mobile-time-value">{{ddinfo3.info.addtime}}</div>
                                </div>
                                <div class="mobile-time-item">
                                    <div class="mobile-time-label">最后同步</div>
                                    <div class="mobile-time-value">{{ddinfo3.info.lastuptime}}</div>
                                </div>
                                <div class="mobile-time-item" v-if="ddinfo3.info.courseStartTime">
                                    <div class="mobile-time-label">课程开始</div>
                                    <div class="mobile-time-value">{{ddinfo3.info.courseStartTime}}</div>
                                </div>
                                <div class="mobile-time-item" v-if="ddinfo3.info.courseEndTime">
                                    <div class="mobile-time-label">课程结束</div>
                                    <div class="mobile-time-value">{{ddinfo3.info.courseEndTime}}</div>
                                </div>
                                <div class="mobile-time-item" v-if="ddinfo3.info.examStartTime">
                                    <div class="mobile-time-label">考试开始</div>
                                    <div class="mobile-time-value">{{ddinfo3.info.examStartTime}}</div>
                                </div>
                                <div class="mobile-time-item" v-if="ddinfo3.info.examEndTime">
                                    <div class="mobile-time-label">考试结束</div>
                                    <div class="mobile-time-value">{{ddinfo3.info.examEndTime}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 移动端备注信息 -->
                        <div class="mobile-info-section" v-if="ddinfo3.info.remarks">
                            <h4 class="mobile-section-title">📝 备注信息</h4>
                            <div class="mobile-remarks-content">{{ddinfo3.info.remarks}}</div>
                        </div>

                        <!-- 移动端操作按钮区域 -->
                        <div class="mobile-action-section" v-if="ddinfo3.info.dockstatus!='4'">
                            <h4 class="mobile-section-title">⚙️ 订单操作</h4>
                            <div class="mobile-action-buttons">
                                <button @click="ms(ddinfo3.info.oid)" class="mobile-action-btn mobile-danger">
                                    <i class="el-icon-lightning"></i> 秒刷
                                </button>
                                <button @click="xgmm(ddinfo3.info.oid)" class="mobile-action-btn mobile-info">
                                    <i class="el-icon-edit"></i> 改密码
                                </button>
                                <button @click="bs(ddinfo3.info.oid)" class="mobile-action-btn mobile-primary">
                                    <i class="el-icon-refresh-right"></i> 补单
                                </button>
                                <button @click="quxiao(ddinfo3.info.oid)" class="mobile-action-btn mobile-default">
                                    <i class="el-icon-close"></i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
          </div>
    </div>
   </div>
 </div>
<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<style>
/* 移动端显示控制 */
@media (min-width: 769px) {
    .mobile-card-view {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .desktop-table-view {
        display: none !important;
    }
}

/* ===== 桌面端订单详情样式 ===== */
.desktop-order-detail {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: #f8fafc;
}

.desktop-detail-container {
    background: #f8fafc;
    border-radius: 16px;
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

/* 桌面端订单头部 */
.desktop-detail-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 24px 32px;
    position: relative;
    overflow: hidden;
}

.desktop-detail-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.desktop-header-content {
    position: relative;
    z-index: 1;
}

.desktop-platform-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.desktop-platform-name {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.desktop-speed-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.desktop-order-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    opacity: 0.9;
}

.desktop-order-id {
    font-size: 16px;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 12px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.desktop-order-time {
    font-size: 14px;
    opacity: 0.8;
}

/* 桌面端状态和进度区域 */
.desktop-status-progress-section {
    padding: 12px 24px;
    background: #f8fafc;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.desktop-status-card, .desktop-progress-card {
    background: #f9fafb;
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.desktop-status-card:hover, .desktop-progress-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.desktop-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.desktop-status-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.desktop-progress-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.desktop-card-label {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.desktop-status-display {
    display: flex;
    align-items: center;
    gap: 12px;
}

.desktop-status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: default;
}

.desktop-status-badge.success {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}
.desktop-status-badge.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}
.desktop-status-badge.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}
.desktop-status-badge.info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}
.desktop-status-badge.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.desktop-refresh-btn {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.desktop-refresh-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.desktop-progress-display {
    width: 100%;
}

.desktop-progress-bar-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.desktop-progress-bar-track {
    flex: 1;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.desktop-progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 4px;
    position: relative;
    transition: width 0.3s ease;
}

.desktop-progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: desktop-shimmer 2s infinite;
}

@keyframes desktop-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.desktop-progress-percentage {
    font-size: 16px;
    font-weight: 700;
    color: #10b981;
    min-width: 50px;
    text-align: right;
}

/* 桌面端主要信息区域 */
.desktop-main-info-section {
    padding: 12px 24px;
    background: #f8fafc;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.desktop-time-info-section, .desktop-remarks-section {
    padding: 0 24px 12px;
    background: #f8fafc;
}

.desktop-info-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.desktop-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.desktop-card-header {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 10px 12px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 8px;
}

.desktop-card-header .desktop-card-icon {
    width: 28px;
    height: 28px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    color: white;
    font-size: 14px;
}

.desktop-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.desktop-card-body {
    padding: 8px 12px;
}

.desktop-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid #f3f4f6;
}

.desktop-info-row:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.desktop-info-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
    min-width: 80px;
}

.desktop-info-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
    text-align: right;
    word-break: break-all;
    max-width: 200px;
}

.desktop-course-name {
    color: #3b82f6;
    font-weight: 700;
}

/* 桌面端账号信息网格 */
.desktop-account-info-grid {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.desktop-account-item {
    background: #f9fafb;
    border-radius: 8px;
    padding: 7px 10px;
    transition: all 0.3s ease;
}

.desktop-account-item:hover {
    background: #f3f4f6;
    transform: translateY(-1px);
}

.desktop-account-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.desktop-account-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
    min-width: 60px;
}

.desktop-account-value-group {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    justify-content: flex-end;
}

.desktop-account-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.desktop-copy-btn {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    border: none;
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.desktop-copy-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.desktop-copy-all-section {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
    text-align: center;
}

.desktop-copy-all-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.desktop-copy-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

/* 桌面端时间网格 */
.desktop-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.desktop-time-item {
    background: #f9fafb;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.desktop-time-item:hover {
    background: #f3f4f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.desktop-time-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.desktop-time-value {
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* 桌面端备注内容 */
.desktop-remarks-content {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 1px solid #f59e0b;
    border-radius: 12px;
    padding: 20px;
    font-size: 14px;
    line-height: 1.6;
    color: #92400e;
    font-weight: 500;
    word-break: break-all;
}

/* 桌面端操作按钮区域 */
.desktop-action-section {
    background: #f8fafc;
    padding: 12px 24px;
    border-top: 1px solid #e5e7eb;
}

.desktop-action-buttons {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
}

.desktop-action-btn {
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    min-height: 70px;
    justify-content: center;
}

.desktop-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.desktop-action-btn:hover::before {
    left: 100%;
}

.desktop-action-btn i {
    font-size: 20px;
    margin-bottom: 4px;
}

.desktop-action-btn span {
    font-size: 12px;
    font-weight: 500;
}

.desktop-action-btn.desktop-refresh {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.desktop-action-btn.desktop-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.desktop-action-btn.desktop-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.desktop-action-btn.desktop-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.desktop-action-btn.desktop-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.desktop-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.desktop-action-btn:active {
    transform: translateY(-1px);
}

/* ===== 移动端订单详情样式（限定在弹窗内） ===== */
.layui-layer .mobile-order-detail {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    height: auto;
    overflow: visible;
    margin: 0;
    padding: 0;
}

.layui-layer .mobile-detail-container {
    padding: 0;
    margin: 0;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    height: auto;
    min-height: auto;
    display: block;
}

/* 移动端订单头部 */
.layui-layer .mobile-detail-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    position: relative;
    overflow: hidden;
}

.layui-layer .mobile-header-left {
    flex: 1;
}

.layui-layer .mobile-order-title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
}

.layui-layer .mobile-speed-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    margin-left: 8px;
}

.layui-layer .mobile-header-right {
    text-align: right;
}

.layui-layer .mobile-order-id {
    font-size: 12px;
    opacity: 0.9;
    font-weight: 500;
}

/* 移动端状态和进度区域 */
.layui-layer .mobile-status-progress-section {
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.layui-layer .mobile-status-info, .layui-layer .mobile-progress-info {
    margin-bottom: 12px;
}

.layui-layer .mobile-status-info:last-child, .layui-layer .mobile-progress-info:last-child {
    margin-bottom: 0;
}

.layui-layer .mobile-status-label, .layui-layer .mobile-progress-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.layui-layer .mobile-status-value {
    display: flex;
    align-items: center;
    gap: 8px;
}

.layui-layer .mobile-status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-align: center;
}

.layui-layer .mobile-status-badge.success {
    background: linear-gradient(135deg, #10b981, #059669);
}
.layui-layer .mobile-status-badge.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}
.layui-layer .mobile-status-badge.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}
.layui-layer .mobile-status-badge.info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}
.layui-layer .mobile-status-badge.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.layui-layer .mobile-refresh-btn {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
}

.layui-layer .mobile-progress-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.layui-layer .mobile-progress-bar-modern {
    flex: 1;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
}

.layui-layer .mobile-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.layui-layer .mobile-progress-text {
    font-size: 12px;
    font-weight: 600;
    color: #10b981;
    min-width: 35px;
    text-align: right;
}

/* 移动端信息区域 */
.layui-layer .mobile-info-section {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
}

.layui-layer .mobile-section-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 6px;
}

.layui-layer .mobile-info-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.layui-layer .mobile-info-item {
    background: #f9fafb;
    border-radius: 8px;
    padding: 8px 12px;
}

.layui-layer .mobile-info-label {
    font-size: 11px;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.layui-layer .mobile-info-value {
    font-size: 13px;
    color: #1f2937;
    font-weight: 600;
    word-break: break-all;
}

/* 移动端账号信息 */
.layui-layer .mobile-account-detail {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.layui-layer .mobile-account-item {
    background: #f9fafb;
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.layui-layer .mobile-account-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    min-width: 40px;
}

.layui-layer .mobile-account-value {
    font-size: 13px;
    color: #1f2937;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    flex: 1;
    word-break: break-all;
}

.layui-layer .mobile-copy-btn-small {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
    font-weight: 500;
}

.layui-layer .mobile-copy-all-btn-detail {
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    width: 100%;
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

/* 移动端时间信息 */
.layui-layer .mobile-time-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.layui-layer .mobile-time-item {
    background: #f9fafb;
    border-radius: 8px;
    padding: 8px;
    text-align: center;
}

.layui-layer .mobile-time-label {
    font-size: 10px;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.layui-layer .mobile-time-value {
    font-size: 11px;
    color: #1f2937;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

/* 移动端备注信息 */
.layui-layer .mobile-remarks-content {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 12px;
    font-size: 12px;
    line-height: 1.4;
    color: #92400e;
    font-weight: 500;
    word-break: break-all;
}

/* 移动端操作按钮 */
.layui-layer .mobile-action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.layui-layer .mobile-action-btn {
    padding: 10px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.layui-layer .mobile-action-btn.mobile-danger {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.layui-layer .mobile-action-btn.mobile-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.layui-layer .mobile-action-btn.mobile-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.layui-layer .mobile-action-btn.mobile-default {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

/* 移动端卡片布局样式 - 现代化美观设计 */
.mobile-card-view {
    display: none;
}

@media (max-width: 768px) {
    .mobile-card-view {
        display: block !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    }

    .mobile-select-all {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 16px;
        border-radius: 12px;
        margin-bottom: 16px;
        border: none;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        font-size: 14px;
        font-weight: 500;
    }

    .mobile-select-all label {
        color: white;
        font-size: 14px;
        font-weight: 500;
    }

    .order-card {
        background: #fff;
        border: none;
        border-radius: 12px;
        margin-bottom: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .order-card:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 16px 18px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-checkbox {
        flex-shrink: 0;
        margin-right: 12px;
    }

    .card-title {
        flex: 1;
        margin: 0 12px;
        min-width: 0;
    }

    .platform-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 16px;
        display: block;
        word-break: break-all;
        line-height: 1.4;
        margin-bottom: 2px;
    }

    .miao-tag {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        margin-left: 8px;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    }

    .card-actions {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-shrink: 0;
    }

    .detail-btn {
        padding: 10px 16px !important;
        font-size: 14px !important;
        border-radius: 10px !important;
        font-weight: 500 !important;
        min-width: 50px !important;
    }

    /* 操作下拉按钮优化 */
    .card-actions .el-dropdown .el-button {
        padding: 10px 16px !important;
        font-size: 14px !important;
        border-radius: 10px !important;
        font-weight: 500 !important;
        min-width: 80px !important;
        background: linear-gradient(135deg, #409eff, #3a8ee6) !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 3px 12px rgba(64, 158, 255, 0.3) !important;
    }

    .card-content {
        padding: 18px;
        background: #fff;
    }

    .info-row {
        display: flex;
        margin-bottom: 16px;
        align-items: flex-start;
    }

    .info-row:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 13px;
        min-width: 80px;
        flex-shrink: 0;
        margin-right: 12px;
        line-height: 1.5;
    }

    .info-value {
        flex: 1;
        font-size: 14px;
        color: #343a40;
        word-break: break-all;
        line-height: 1.5;
    }

    .course-name {
        font-weight: 500;
        color: #2c3e50;
        font-size: 14px;
        line-height: 1.4;
    }

    .account-info {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 6px 8px;
        border-radius: 6px;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .account-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        gap: 6px;
        padding: 2px 0;
    }

    .account-item:last-of-type {
        margin-bottom: 4px;
    }

    .copy-btn {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 500;
        cursor: pointer;
        flex-shrink: 0;
        box-shadow: 0 1px 4px rgba(0, 123, 255, 0.2);
        transition: all 0.2s ease;
        min-width: 35px;
    }

    .copy-btn:active {
        transform: scale(0.95);
    }

    .copy-all-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 11px;
        font-weight: 500;
        cursor: pointer;
        width: 100%;
        box-shadow: 0 1px 6px rgba(40, 167, 69, 0.2);
        transition: all 0.2s ease;
        margin-top: 4px;
    }

    .copy-all-btn:active {
        transform: scale(0.98);
    }

    .account-text {
        flex: 1;
        font-size: 12px;
        word-break: break-all;
        color: #495057;
        font-weight: 400;
        line-height: 1.3;
    }

    .progress-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .progress-text {
        font-weight: 600;
        color: #007bff;
        min-width: 45px;
        flex-shrink: 0;
        font-size: 14px;
    }

    .progress-bar-container {
        flex: 1;
        height: 12px;
        background: #e9ecef;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-left: 8px;
        min-width: 100px;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #0056b3);
        transition: width 0.4s ease;
        border-radius: 6px;
        min-width: 2px;
    }

    .remarks {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        padding: 10px 12px;
        border-radius: 10px;
        border: 1px solid rgba(255, 193, 7, 0.3);
        font-size: 13px;
        color: #856404;
        line-height: 1.4;
    }

    .time {
        color: #6c757d;
        font-size: 13px;
        font-weight: 400;
    }

    .status-btn {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        display: inline-block;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .status-warning {
        background: linear-gradient(135deg, #ffc107, #ffb300);
        color: #212529;
    }

    .status-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .status-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
    }

    .status-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }

    .status-default {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
    }
}

/* 移动端订单列表优化样式 */
@media (max-width: 768px) {
    /* 整体容器优化 */
    .wrapper-md {
        padding: 10px !important;
    }

    /* 面板优化 */
    .panel {
        margin-bottom: 15px !important;
        border-radius: 8px !important;
    }

    .panel-heading {
        padding: 10px 15px !important;
        font-size: 16px !important;
    }

    .panel-body {
        padding: 15px 10px !important;
    }

    /* 移动端搜索区域控制 */
    .mobile-search-toggle,
    .mobile-batch-toggle {
        display: none;
    }

    /* 搜索表单优化 */
    .layui-row.layui-col-space10 {
        margin: 0 -5px !important;
    }

    .layui-col-xs6, .layui-col-xs9 {
        padding: 0 5px !important;
        margin-bottom: 10px !important;
    }

    @media (max-width: 768px) {
        /* 移动端搜索切换按钮 */
        .mobile-search-toggle {
            display: flex !important;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #409eff, #3a8ee6);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
        }

        /* 移动端批量操作切换按钮 */
        .mobile-batch-toggle {
            display: flex !important;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #67c23a, #5daf34);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
            transition: all 0.3s ease;
        }

        .mobile-search-toggle:active,
        .mobile-batch-toggle:active {
            transform: scale(0.98);
        }

        .mobile-search-toggle i:first-child,
        .mobile-batch-toggle i:first-child {
            margin-right: 8px;
            font-size: 16px;
        }

        .mobile-search-toggle i:last-child,
        .mobile-batch-toggle i:last-child {
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        /* 搜索表单容器 */
        .search-form-container,
        .batch-operations-container {
            overflow: hidden;
            transition: all 0.4s ease;
            max-height: 2000px;
            opacity: 1;
        }

        .search-form-container.mobile-collapsed,
        .batch-operations-container.mobile-collapsed {
            max-height: 0;
            opacity: 0;
            margin-bottom: 0;
        }

        .search-form-container.mobile-collapsed .el-form,
        .batch-operations-container.mobile-collapsed > div {
            padding: 0 !important;
        }

        /* 批量操作按钮容器优化 */
        .batch-operations-container > div {
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 16px;
        }

        .batch-operations-container .el-button--mini {
            margin: 4px 2px !important;
            padding: 8px 12px !important;
            font-size: 12px !important;
        }

        .batch-operations-container p {
            margin: 15px 0 5px 0 !important;
            font-size: 13px !important;
            color: #666 !important;
            line-height: 1.4 !important;
        }
    }

    /* Element UI 组件优化 */
    .el-select, .el-input, .el-date-picker {
        width: 100% !important;
    }

    .el-button {
        width: 100% !important;
        margin-bottom: 5px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        padding: 10px 16px !important;
        border: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.2s ease !important;
    }

    .el-button--primary {
        background: linear-gradient(135deg, #409eff, #3a8ee6) !important;
    }

    .el-button--success {
        background: linear-gradient(135deg, #67c23a, #5daf34) !important;
    }

    .el-button--warning {
        background: linear-gradient(135deg, #e6a23c, #cf9236) !important;
    }

    .el-button--danger {
        background: linear-gradient(135deg, #f56c6c, #f04747) !important;
    }

    .el-button--info {
        background: linear-gradient(135deg, #909399, #82848a) !important;
    }

    /* 折叠面板优化 */
    .el-collapse {
        border: none !important;
    }

    .el-collapse-item__header {
        padding: 10px 15px !important;
        font-size: 14px !important;
    }

    .el-collapse-item__content {
        padding: 10px 15px !important;
    }

    /* 批量操作按钮优化 */
    .el-button--mini {
        margin: 3px !important;
        padding: 8px 12px !important;
        font-size: 13px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        border: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.2s ease !important;
    }

    .el-button--mini.el-button--primary {
        background: linear-gradient(135deg, #409eff, #3a8ee6) !important;
        color: white !important;
    }

    .el-button--mini.el-button--success {
        background: linear-gradient(135deg, #67c23a, #5daf34) !important;
        color: white !important;
    }

    .el-button--mini.el-button--warning {
        background: linear-gradient(135deg, #e6a23c, #cf9236) !important;
        color: white !important;
    }

    .el-button--mini.el-button--danger {
        background: linear-gradient(135deg, #f56c6c, #f04747) !important;
        color: white !important;
    }

    .el-button--mini.el-button--info {
        background: linear-gradient(135deg, #909399, #82848a) !important;
        color: white !important;
    }

    /* 表格容器优化 */
    .table-responsive {
        border: none !important;
        margin-bottom: 0 !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    /* 表格优化 */
    .table {
        margin-bottom: 0 !important;
        min-width: 800px !important; /* 确保表格有最小宽度，可以横向滚动 */
    }

    .table th, .table td {
        padding: 8px 4px !important;
        font-size: 12px !important;
        white-space: nowrap !important;
        vertical-align: middle !important;
    }

    /* 表头优化 */
    .table thead th {
        background-color: #f8f9fa !important;
        border-bottom: 2px solid #dee2e6 !important;
        font-weight: 600 !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
    }

    /* 操作列优化 */
    .table td:nth-child(2) { /* 操作列 */
        min-width: 80px !important;
    }

    .table td:nth-child(3) { /* 详细列 */
        min-width: 60px !important;
    }

    .table td:nth-child(4) { /* 平台列 */
        min-width: 120px !important;
        max-width: 150px !important;
    }

    .table td:nth-child(5) { /* 账号列 */
        min-width: 200px !important;
        max-width: 250px !important;
    }

    .table td:nth-child(7) { /* 任务名称列 */
        min-width: 150px !important;
        max-width: 200px !important;
    }

    .table td:nth-child(8) { /* 状态列 */
        min-width: 80px !important;
    }

    .table td:nth-child(10) { /* 订单详细信息列 */
        min-width: 200px !important;
        max-width: 300px !important;
    }

    /* 按钮优化 */
    .layui-btn-xs {
        padding: 6px 10px !important;
        font-size: 12px !important;
        margin: 2px !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }

    .layui-btn-primary {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        color: #495057 !important;
        border: 1px solid #dee2e6 !important;
    }

    .layui-btn-normal {
        background: linear-gradient(135deg, #007bff, #0056b3) !important;
        color: white !important;
        border: none !important;
    }

    .el-dropdown .el-button--small {
        padding: 8px 12px !important;
        font-size: 13px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        border: none !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        color: #495057 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    /* 进度条优化 */
    .layui-progress {
        margin: 5px 0 !important;
        height: 8px !important;
    }

    /* 复制按钮优化 */
    .layui-btn-primary {
        padding: 1px 4px !important;
        font-size: 10px !important;
        margin: 1px !important;
        border: 1px solid #ddd !important;
    }

    /* 分页优化 */
    .pagination {
        margin: 20px 0 !important;
        text-align: center !important;
        padding: 0 10px !important;
    }

    .pagination li {
        display: inline-block !important;
        margin: 0 3px !important;
    }

    .pagination li a {
        padding: 10px 14px !important;
        font-size: 13px !important;
        font-weight: 500 !important;
        border: none !important;
        border-radius: 8px !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        color: #495057 !important;
        transition: all 0.2s ease !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        text-decoration: none !important;
    }

    .pagination li a:hover {
        background: linear-gradient(135deg, #409eff, #3a8ee6) !important;
        color: white !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
    }

    .pagination li.active a {
        background: linear-gradient(135deg, #409eff, #3a8ee6) !important;
        color: white !important;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
    }

    /* 移动端弹窗优化 */
    .layui-layer {
        max-width: 95% !important;
        border-radius: 16px !important;
        overflow: hidden !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .layui-layer-title {
        font-size: 16px !important;
        font-weight: 600 !important;
        padding: 16px 20px !important;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        color: #2c3e50 !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
    }

    .layui-layer-content {
        padding: 20px !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        background: white !important;
    }

    /* 移动端弹窗定位和尺寸优化 */
    @media (max-width: 768px) {
        .layui-layer {
            max-width: 90% !important;
            min-width: 280px !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            position: fixed !important;
            margin: 0 !important;
        }

        .layui-layer-dialog {
            max-width: 90% !important;
            min-width: 280px !important;
        }

        .layui-layer-title {
            font-size: 15px !important;
            padding: 12px 16px !important;
            text-align: center !important;
        }

        .layui-layer-content {
            padding: 16px !important;
            font-size: 13px !important;
            text-align: center !important;
            word-wrap: break-word !important;
            max-height: 60vh !important;
            overflow-y: auto !important;
        }

        /* 按钮区域优化 */
        .layui-layer-btn {
            text-align: center !important;
            padding: 12px 16px 16px 16px !important;
            background: #f8f9fa !important;
        }

        .layui-layer-btn a {
            min-width: 80px !important;
            padding: 10px 16px !important;
            margin: 0 6px !important;
            border-radius: 8px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            line-height: 1.4 !important;
            text-align: center !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            vertical-align: middle !important;
        }

        .layui-layer-btn0 {
            background: linear-gradient(135deg, #409eff, #3a8ee6) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
        }

        .layui-layer-btn1 {
            background: linear-gradient(135deg, #909399, #82848a) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3) !important;
        }

        /* 确保按钮文字完全居中 */
        .layui-layer-btn a * {
            vertical-align: middle !important;
        }

        .layui-layer-btn a:before,
        .layui-layer-btn a:after {
            display: none !important;
        }

        /* 消息提示优化 */
        .layui-layer-msg {
            max-width: 80% !important;
            min-width: 200px !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
        }

        .layui-layer-msg .layui-layer-content {
            padding: 12px 20px !important;
            font-size: 14px !important;
            text-align: center !important;
            background: white !important;
            border-radius: 12px !important;
        }

        /* 确保弹窗在视口内 */
        .layui-layer-wrap {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            height: 100vh !important;
            overflow: auto !important;
        }

        /* 遮罩层优化 */
        .layui-layer-shade {
            background: rgba(0, 0, 0, 0.5) !important;
        }

        /* 移动端弹窗特殊优化 */
        .layui-layer-dialog {
            margin: 0 !important;
        }

        .layui-layer-dialog .layui-layer-content {
            overflow: visible !important;
        }

        /* Element UI 下拉菜单移动端优化 */
        .el-dropdown-menu {
            border-radius: 12px !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
            border: none !important;
            padding: 8px 0 !important;
            min-width: 140px !important;
        }

        .el-dropdown-menu__item {
            padding: 12px 20px !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            line-height: 1.4 !important;
            color: #333 !important;
            transition: all 0.2s ease !important;
        }

        .el-dropdown-menu__item:hover {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe) !important;
            color: #409eff !important;
        }

        .el-dropdown-menu__item:not(.is-disabled):focus {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe) !important;
            color: #409eff !important;
        }

        .el-dropdown-menu__item--divided {
            border-top: 1px solid #f0f0f0 !important;
            margin-top: 4px !important;
            padding-top: 16px !important;
        }
    }




    .desktop-refresh-btn {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
    }

    .desktop-refresh-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    }

    .desktop-progress-display {
        width: 100%;
    }

    .desktop-progress-bar-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .desktop-progress-bar-track {
        flex: 1;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }

    .desktop-progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 4px;
        transition: width 0.6s ease;
        position: relative;
    }

    .desktop-progress-bar-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: desktop-shimmer 2s infinite;
    }

    @keyframes desktop-shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .desktop-progress-percentage {
        font-size: 16px;
        font-weight: 700;
        color: #10b981;
        min-width: 50px;
        text-align: right;
    }

    /* 桌面端主要信息区域 */
    .desktop-main-info-section {
        padding: 24px 32px;
        background: white;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
    }

    .desktop-time-info-section, .desktop-remarks-section {
        padding: 0 32px 24px;
        background: white;
    }

    .desktop-info-card {
        background: white;
        border-radius: 16px;
        border: 1px solid #e5e7eb;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .desktop-info-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #d1d5db;
    }

    .desktop-card-header {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        padding: 20px 24px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .desktop-card-header .desktop-card-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        margin-bottom: 0;
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        color: white;
    }

    .desktop-card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
    }

    .desktop-card-body {
        padding: 24px;
    }

    .desktop-info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f3f4f6;
    }

    .desktop-info-row:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .desktop-info-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 600;
        min-width: 100px;
    }

    .desktop-info-value {
        font-size: 14px;
        color: #1f2937;
        font-weight: 500;
        text-align: right;
        word-break: break-all;
        max-width: 200px;
    }

    .desktop-course-name {
        color: #3b82f6;
        font-weight: 600;
    }

    /* 桌面端账号信息网格 */
    .desktop-account-info-grid {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .desktop-account-item {
        background: #f9fafb;
        border-radius: 12px;
        padding: 16px;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .desktop-account-item:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
    }

    .desktop-account-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .desktop-account-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 600;
        min-width: 60px;
    }

    .desktop-account-value-group {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        justify-content: flex-end;
    }

    .desktop-account-value {
        font-size: 14px;
        color: #1f2937;
        font-weight: 500;
        word-break: break-all;
        text-align: right;
        max-width: 200px;
    }

    .desktop-copy-btn {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        color: white;
        border: none;
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 32px;
        box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
    }

    .desktop-copy-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
    }

    .desktop-copy-all-section {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
    }

    .desktop-copy-all-btn {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        width: 100%;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .desktop-copy-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
    }

    /* 桌面端时间网格 */
    .desktop-time-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .desktop-time-item {
        background: #f9fafb;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        text-align: center;
    }

    .desktop-time-item:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
        transform: translateY(-1px);
    }

    .desktop-time-label {
        font-size: 12px;
        color: #6b7280;
        font-weight: 600;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .desktop-time-value {
        font-size: 14px;
        color: #1f2937;
        font-weight: 600;
        word-break: break-all;
    }

    /* 桌面端备注内容 */
    .desktop-remarks-content {
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        padding: 20px;
        border-radius: 12px;
        border-left: 4px solid #f59e0b;
        font-size: 14px;
        line-height: 1.6;
        color: #92400e;
        word-break: break-all;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
    }

    /* 桌面端操作按钮区域 */
    .desktop-action-section {
        background: #f8fafc;
        padding: 24px 32px;
        border-top: 1px solid #e5e7eb;
    }

    .desktop-action-buttons {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 12px;
    }

    .desktop-action-btn {
        padding: 12px 16px;
        border: none;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 6px;
        color: white;
        white-space: nowrap;
        min-height: 80px;
        position: relative;
        overflow: hidden;
    }

    .desktop-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .desktop-action-btn:hover::before {
        left: 100%;
    }

    .desktop-action-btn i {
        font-size: 20px;
        margin-bottom: 4px;
    }

    .desktop-action-btn span {
        font-size: 12px;
        font-weight: 600;
    }

    .desktop-action-btn.desktop-refresh {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    .desktop-action-btn.desktop-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .desktop-action-btn.desktop-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }

    .desktop-action-btn.desktop-info {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
    }

    .desktop-action-btn.desktop-danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .desktop-action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .desktop-action-btn:active {
        transform: translateY(-1px);
    }

    /* 桌面端样式优化 - 确保新的订单详情样式只在桌面端生效 */
    @media (min-width: 769px) {
        /* 桌面端订单详情弹窗优化 */
        .modern-order-detail {
            background: #f8fafc;
        }

        .detail-container {
            background: #f8fafc;
            border-radius: 16px;
            overflow: hidden;
            max-width: 900px;
            margin: 0 auto;
            max-height: 85vh;
            overflow-y: auto;
        }

        /* 确保桌面端使用新的卡片布局 */
        .main-info-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .status-progress-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .action-buttons {
            grid-template-columns: repeat(5, 1fr);
        }

        /* 桌面端字体大小优化 */
        .platform-name {
            font-size: 24px;
        }

        .card-title {
            font-size: 18px;
        }

        .info-label, .account-label, .info-value, .account-value {
            font-size: 14px;
        }

        .time-label {
            font-size: 12px;
        }

        .time-value {
            font-size: 14px;
        }

        .action-btn {
            min-height: 80px;
            font-size: 14px;
        }

        .action-btn i {
            font-size: 20px;
        }

        .action-btn span {
            font-size: 12px;
        }

        .info-value {
            font-size: 14px;
        }

        .account-detail {
            padding: 16px;
        }

        .account-item {
            padding: 8px 12px;
            margin-bottom: 12px;
        }

        .account-label {
            font-size: 12px;
            min-width: 50px;
            margin-right: 12px;
        }

        .account-value {
            font-size: 13px;
            margin-right: 8px;
        }

        .copy-btn-small {
            font-size: 10px;
            padding: 4px 8px;
        }

        .copy-all-btn-detail {
            font-size: 13px;
            padding: 10px 16px;
            gap: 8px;
        }

        .time-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .time-item {
            padding: 12px 16px;
        }

        .time-label {
            font-size: 11px;
            margin-bottom: 4px;
        }

        .time-value {
            font-size: 13px;
        }

        .remarks-content {
            padding: 16px;
            font-size: 14px;
        }

        .action-section {
            padding: 16px 24px;
        }

        .action-buttons {
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .action-btn {
            padding: 12px 16px;
            font-size: 13px;
            gap: 6px;
        }
    }

        .detail-header {
    .mobile-order-detail {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        height: auto;
        overflow: visible;
        margin: 0;
        padding: 0;
    }

    .mobile-detail-container {
        padding: 0;
        margin: 0;
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        height: auto;
        min-height: auto;
        display: block;
    }

    /* 移动端订单头部 */
    .mobile-detail-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
    }

    .mobile-header-left {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .mobile-order-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.3;
        word-break: break-all;
        text-align: left;
    }

    .mobile-speed-tag {
        background: rgba(255, 255, 255, 0.2);
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
        margin-left: 6px;
        backdrop-filter: blur(10px);
        flex-shrink: 0;
    }

    .mobile-header-right {
        flex-shrink: 0;
        margin-left: 8px;
    }

    .mobile-order-id {
        font-size: 12px;
        opacity: 0.9;
        font-weight: 500;
        text-align: right;
    }

    /* 移动端状态和进度区域 */
    .mobile-status-progress-section {
        padding: 10px 16px;
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        border-bottom: 1px solid #e9ecef;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        flex-shrink: 0;
    }

    .mobile-status-info, .mobile-progress-info {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .mobile-status-label, .mobile-progress-label {
        font-size: 10px;
        color: #666;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        text-align: center;
    }

    .mobile-status-value {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;
    }

    .mobile-status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        text-align: center;
        white-space: nowrap;
    }

    .mobile-status-badge.success { background: #d4edda; color: #155724; }
    .mobile-status-badge.warning { background: #fff3cd; color: #856404; }
    .mobile-status-badge.danger { background: #f8d7da; color: #721c24; }
    .mobile-status-badge.info { background: #d1ecf1; color: #0c5460; }
    .mobile-status-badge.primary { background: #cce7ff; color: #004085; }

    .mobile-refresh-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 9px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
    }

    .mobile-refresh-btn:hover {
        transform: scale(1.05);
    }

    /* 移动端进度条 */
    .mobile-progress-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .mobile-progress-bar-modern {
        flex: 1;
        height: 6px;
        background: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
        min-width: 60px;
    }

    .mobile-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #0056b3);
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .mobile-progress-text {
        font-size: 10px;
        font-weight: 600;
        color: #007bff;
        min-width: 30px;
        text-align: center;
    }

    /* 移动端信息区域 */
    .mobile-info-section {
        padding: 8px 16px;
        border-bottom: 1px solid #f0f0f0;
        flex-shrink: 0;
    }

    .mobile-info-section:last-child {
        border-bottom: none;
        flex: 1;
    }

    .mobile-section-title {
        margin: 0 0 8px 0;
        font-size: 12px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        text-align: center;
    }

    /* 移动端信息网格 */
    .mobile-info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .mobile-info-item {
        background: #f8f9fa;
        padding: 6px 8px;
        border-radius: 6px;
        border-left: 3px solid #409eff;
        text-align: center;
    }

    .mobile-info-label {
        font-size: 9px;
        color: #666;
        font-weight: 600;
        margin-bottom: 2px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        text-align: center;
    }

    .mobile-info-value {
        font-size: 11px;
        color: #2c3e50;
        font-weight: 500;
        word-break: break-all;
        line-height: 1.3;
        text-align: center;
    }

    /* 移动端账号详情 */
    .mobile-account-detail {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 8px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .mobile-account-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
        padding: 4px 6px;
        background: white;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .mobile-account-item:last-of-type {
        margin-bottom: 8px;
    }

    .mobile-account-label {
        font-size: 9px;
        color: #666;
        font-weight: 600;
        min-width: 30px;
        margin-right: 6px;
        text-align: left;
        flex-shrink: 0;
    }

    .mobile-account-value {
        flex: 1;
        font-size: 10px;
        color: #2c3e50;
        font-weight: 500;
        word-break: break-all;
        margin-right: 6px;
        text-align: left;
        line-height: 1.2;
    }

    .mobile-copy-btn-small {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
        white-space: nowrap;
    }

    .mobile-copy-btn-small:hover {
        transform: scale(1.05);
    }

    .mobile-copy-all-btn-detail {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 10px;
        font-weight: 500;
        cursor: pointer;
        width: 100%;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
    }

    .mobile-copy-all-btn-detail:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    /* 移动端时间网格 */
    .mobile-time-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .mobile-time-item {
        background: white;
        padding: 6px 8px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
        text-align: center;
    }

    .mobile-time-item:hover {
        border-color: #409eff;
        box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
    }

    .mobile-time-label {
        font-size: 9px;
        color: #666;
        font-weight: 600;
        margin-bottom: 2px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        text-align: center;
    }

    .mobile-time-value {
        font-size: 10px;
        color: #2c3e50;
        font-weight: 500;
        line-height: 1.2;
        word-break: break-all;
        text-align: center;
    }

    /* 移动端备注内容 */
    .mobile-remarks-content {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        padding: 8px 10px;
        border-radius: 6px;
        border-left: 3px solid #ffc107;
        font-size: 10px;
        line-height: 1.4;
        color: #856404;
        word-break: break-all;
        text-align: center;
    }

    /* 移动端操作按钮区域 */
    .mobile-action-section {
        background: #f8f9fa;
        padding: 8px 16px;
        flex-shrink: 0;
    }

    .mobile-action-buttons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .mobile-action-btn {
        padding: 6px 8px;
        border: none;
        border-radius: 6px;
        font-size: 10px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
        color: white;
        white-space: nowrap;
    }

    .mobile-action-btn.mobile-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
    }

    .mobile-action-btn.mobile-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
    }

    .mobile-action-btn.mobile-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
    }

    .mobile-action-btn.mobile-default {
        background: linear-gradient(135deg, #6c757d, #5a6268);
    }

    .mobile-action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .mobile-action-btn:active {
        transform: translateY(0);
    }

        .detail-header {
            padding: 6px 10px;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
        }

        .header-left {
            flex-direction: column;
            align-items: flex-start;
            gap: 2px;
        }

        .order-title {
            font-size: 12px;
            line-height: 1.2;
        }

        .speed-tag {
            font-size: 8px;
            padding: 1px 4px;
            margin-left: 0;
            margin-top: 2px;
        }

        .order-id {
            font-size: 10px;
        }

        .status-progress-section {
            grid-template-columns: 1fr;
            gap: 6px;
            padding: 4px 10px;
        }

        .info-section {
            padding: 4px 10px;
        }

        .section-title {
            font-size: 11px;
            margin-bottom: 4px;
        }

        .info-grid {
            grid-template-columns: 1fr;
            gap: 4px;
        }

        .time-grid {
            grid-template-columns: 1fr;
            gap: 4px;
        }

        .action-buttons {
            grid-template-columns: repeat(2, 1fr);
            gap: 3px;
        }

        .action-btn {
            padding: 4px 5px;
            font-size: 9px;
        }

        .account-detail {
            padding: 4px;
        }

        .account-item {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            gap: 4px;
            padding: 3px 4px;
            margin-bottom: 4px;
        }

        .account-label {
            min-width: 25px;
            margin-right: 4px;
            font-size: 8px;
            text-align: left;
            flex-shrink: 0;
        }

        .account-value {
            flex: 1;
            margin-right: 4px;
            font-size: 9px;
            text-align: left;
        }

        .copy-btn-small {
            padding: 1px 3px;
            font-size: 7px;
            flex-shrink: 0;
        }

        .copy-all-btn-detail {
            padding: 4px 6px;
            font-size: 9px;
            gap: 2px;
        }

        .info-item,
        .time-item {
            padding: 4px 6px;
        }

        .info-label,
        .time-label {
            font-size: 8px;
            margin-bottom: 1px;
        }

        .info-value,
        .time-value {
            font-size: 9px;
        }

        .remarks-content {
            padding: 6px 8px;
            font-size: 9px;
        }

        .status-label,
        .progress-label {
            font-size: 9px;
        }

        .status-badge {
            font-size: 9px;
            padding: 3px 6px;
        }

        .refresh-btn {
            font-size: 8px;
            padding: 2px 4px;
        }

        .progress-text {
            font-size: 9px;
        }
    }

    /* 极小屏幕优化 */
    @media (max-width: 480px) {
        .detail-header {
            padding: 4px 8px;
        }

        .status-progress-section,
        .info-section,
        .action-section {
            padding: 3px 8px;
        }

        .action-buttons {
            grid-template-columns: 1fr;
            gap: 2px;
        }

        .order-title {
            font-size: 11px;
        }

        .section-title {
            font-size: 10px;
            margin-bottom: 3px;
        }

        .info-grid,
        .time-grid {
            grid-template-columns: 1fr;
            gap: 3px;
        }

        .info-item,
        .time-item {
            padding: 3px 5px;
        }

        .account-detail {
            padding: 3px;
        }

        .account-item {
            padding: 2px 3px;
            margin-bottom: 3px;
        }
    }

    /* 导出对话框优化 */
    #exportDialog {
        padding: 20px !important;
        background: white !important;
        border-radius: 12px !important;
    }

    #exportDialog .el-radio-group,
    #exportDialog .el-checkbox-group {
        display: flex !important;
        flex-direction: column !important;
        gap: 10px !important;
    }

    #exportDialog .el-radio,
    #exportDialog .el-checkbox {
        margin: 0 !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        transition: background-color 0.2s ease !important;
    }

    #exportDialog .el-radio:hover,
    #exportDialog .el-checkbox:hover {
        background-color: rgba(64, 158, 255, 0.1) !important;
    }

    /* 文本溢出处理 */
    .table td {
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* 状态按钮优化 */
    .el-button--mini {
        padding: 2px 6px !important;
        font-size: 11px !important;
        border-radius: 3px !important;
    }

    /* 横向滚动提示 */
    .table-responsive::after {
        content: "← 左右滑动查看更多 →";
        display: block;
        text-align: center;
        color: #999;
        font-size: 11px;
        padding: 5px;
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
    }

    /* 隐藏不必要的列在极小屏幕上 */
    @media (max-width: 480px) {
        .table th:nth-child(6), /* 备注列 */
        .table td:nth-child(6) {
            display: none !important;
        }

        .table th:nth-child(9), /* 进度百分比列 */
        .table td:nth-child(9) {
            display: none !important;
        }
    }

    /* 搜索区域收起状态优化 */
    .el-collapse-item__content .el-button {
        width: auto !important;
        margin: 2px 4px !important;
        display: inline-block !important;
    }

    /* 自动更新状态显示优化 */
    .auto-refresh-status {
        font-size: 12px !important;
        margin: 8px 0 !important;
        padding: 8px 12px !important;
        background: linear-gradient(135deg, #e3f2fd, #f0f9ff) !important;
        border-radius: 8px !important;
        border-left: 4px solid #67C23A !important;
        color: #2c3e50 !important;
        font-weight: 500 !important;
    }

    /* 搜索表单美化 */
    .el-form .layui-row {
        background: white !important;
        padding: 16px !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
        margin-bottom: 16px !important;
    }

    /* 输入框美化 */
    .el-input__inner {
        border-radius: 8px !important;
        border: 1px solid #e0e6ed !important;
        font-size: 14px !important;
        padding: 10px 12px !important;
        transition: all 0.2s ease !important;
    }

    .el-input__inner:focus {
        border-color: #409eff !important;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
    }

    /* 下拉框美化 */
    .el-select .el-input__inner {
        cursor: pointer !important;
    }

    /* 日期选择器美化 */
    .el-date-editor .el-range-separator {
        color: #409eff !important;
        font-weight: 500 !important;
    }
}

/* 超小屏幕优化 (小于480px) */
@media (max-width: 480px) {
    .wrapper-md {
        padding: 5px !important;
    }

    .panel-body {
        padding: 10px 5px !important;
    }

    .layui-col-xs6 {
        width: 100% !important;
        margin-bottom: 8px !important;
    }

    .layui-col-xs9 {
        width: 100% !important;
    }

    /* 表格进一步优化 */
    .table {
        min-width: 600px !important;
    }

    .table th, .table td {
        padding: 6px 2px !important;
        font-size: 11px !important;
    }

    /* 按钮进一步缩小 */
    .layui-btn-xs {
        padding: 1px 4px !important;
        font-size: 10px !important;
    }

    .el-button--mini {
        padding: 1px 4px !important;
        font-size: 10px !important;
    }
}

/* 动画和微交互效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@media (max-width: 768px) {
    /* 卡片进入动画 */
    .order-card {
        animation: fadeInUp 0.4s ease-out;
    }

    .order-card:nth-child(1) { animation-delay: 0.1s; }
    .order-card:nth-child(2) { animation-delay: 0.2s; }
    .order-card:nth-child(3) { animation-delay: 0.3s; }
    .order-card:nth-child(4) { animation-delay: 0.4s; }
    .order-card:nth-child(5) { animation-delay: 0.5s; }

    /* 按钮点击动画 */
    .copy-btn:active, .copy-all-btn:active {
        animation: pulse 0.3s ease-out;
    }

    /* 状态按钮动画 */
    .status-btn {
        animation: slideInRight 0.3s ease-out;
    }

    /* 进度条动画 */
    .progress-bar {
        animation: slideInRight 0.8s ease-out;
    }

    /* 悬浮效果增强 */
    .order-card:hover {
        animation: none;
    }

    /* 加载状态优化 */
    .layui-layer-loading .layui-layer-content {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;
    }
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
    /* 触摸反馈 */
    .order-card {
        transition: transform 0.1s ease, box-shadow 0.1s ease;
    }

    .order-card:active {
        transform: scale(0.98);
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    /* 按钮触摸优化 */
    .copy-btn, .copy-all-btn, .detail-btn {
        transition: all 0.1s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .copy-btn:active, .copy-all-btn:active, .detail-btn:active {
        transform: scale(0.95);
        opacity: 0.8;
    }

    /* Element UI 按钮触摸优化 */
    .el-button {
        -webkit-tap-highlight-color: transparent;
        transition: all 0.1s ease;
    }

    .el-button:active {
        transform: scale(0.95);
    }

    /* 状态按钮触摸优化 */
    .status-btn {
        transition: all 0.1s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .status-btn:active {
        transform: scale(0.95);
        opacity: 0.8;
    }

    /* 复选框区域扩大和美化 */
    .lyear-checkbox {
        padding: 10px;
        margin: -10px;
        border-radius: 8px;
        transition: background-color 0.2s ease;
    }

    .lyear-checkbox:hover {
        background-color: rgba(64, 158, 255, 0.1);
    }

    .lyear-checkbox input[type="checkbox"] + span {
        border-radius: 4px;
        border: 2px solid #ddd;
        transition: all 0.2s ease;
    }

    .lyear-checkbox input[type="checkbox"]:checked + span {
        background: linear-gradient(135deg, #409eff, #3a8ee6);
        border-color: #409eff;
    }

    /* 滚动优化 */
    .table-responsive {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* 输入框触摸优化 */
    .el-input__inner, .el-textarea__inner {
        -webkit-appearance: none;
        border-radius: 6px;
    }

    /* 下拉框触摸优化 */
    .el-select .el-input__inner {
        cursor: pointer;
    }

    /* 防止双击缩放 */
    .card-header, .card-content, .info-row {
        touch-action: manipulation;
    }

    /* 长按选择优化 */
    .account-text, .course-name, .remarks {
        -webkit-user-select: text;
        user-select: text;
    }

    /* 滑动提示优化 */
    .table-responsive::after {
        position: sticky;
        left: 0;
        right: 0;
        z-index: 5;
    }

    /* 加载状态优化 */
    .layui-layer-loading {
        background: rgba(0,0,0,0.3) !important;
    }

    .layui-layer-loading .layui-layer-content {
        background: rgba(255,255,255,0.9) !important;
        border-radius: 8px !important;
    }
}

/* 横屏优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .order-card {
        margin-bottom: 10px;
    }

    .card-header {
        padding: 8px 15px;
    }

    .card-content {
        padding: 10px 15px;
    }

    .info-row {
        margin-bottom: 8px;
    }

    .mobile-select-all {
        padding: 8px 15px;
        margin-bottom: 10px;
    }
}

/* 极小屏幕优化 */
@media (max-width: 360px) {
    .wrapper-md {
        padding: 3px !important;
    }

    .order-card {
        margin-bottom: 10px;
        border-radius: 6px;
    }

    .card-header {
        padding: 8px 10px;
        flex-wrap: wrap;
        gap: 5px;
    }

    .card-title {
        width: 100%;
        margin: 5px 0;
    }

    .card-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .card-content {
        padding: 10px;
    }

    .info-label {
        min-width: 60px;
        font-size: 12px;
    }

    .info-value {
        font-size: 12px;
    }

    .account-item {
        flex-wrap: wrap;
        gap: 4px;
    }

    .account-text {
        width: 100%;
        margin-top: 2px;
    }
}
</style>
<script>
vm = new Vue({
    el: "#orderlist",
    data: {
        currentOid: '',
        row: [],
        timer: null,
        autoRefreshInterval: 30, // 自动刷新间隔（秒）
        lastRefreshTime: null, // 最后刷新时间
        phone: '',
        list: '',
        sex: [],
        ddinfo3: {
            status: false,
            info: []
        },
        cx: {
            school: '',
            status_text: '',
            dock: '',
            qq: '',
            oid: '',
            uid: '',
            cid: '',
            kcname: '',
            pagesize: '50',
            remarks: '',
            mima: '',
            hid: '',
            dateRange: []
        },
        logData: [],
        exportScope: 'current',
        exportFormat: 'txt',
        exportFields: [],
        // 移动端相关数据
        mobileMode: false,
        resizeTimer: null,
        mobileSearchExpanded: false,
        mobileBatchExpanded: false
    },
    methods: {
        handleCommand(command, oid) {
            switch (command) {
                case 'up':
                    this.up(oid);
                    break;
                case 'zt':
                    this.zt(oid);
                    break;
                case 'bs':
                    this.bs(oid);
                    break;
                case 'feedback':
                    this.feedback(oid);
                    break;
                default:
                    break;
            }
        },
        async get(page) {
            try {
                let data = { cx: this.cx, page };
                var load = layer.load(2);
                const response = await this.$http.post("/apisub.php?act=orderlist", data, { emulateJSON: true });
                layer.close(load);
                if (response.data.code === 1) {
                    this.row = response.body;
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error(error);
            }
        },
        removepercent: function (text) {
            function isNumeric(value) {
                return !isNaN(parseFloat(value)) && isFinite(value) && typeof value !== 'boolean';
            }
            if (isNumeric(text.split('%').join(""))) {
                return text.split('%').join("");
            }
            return false;
        },
        showExportDialog: function () {
            if (this.row.data.length === 0 && this.exportScope === 'current') {
                layer.msg('当前页面没有数据可导出', { icon: 2 });
                return;
            }
            layer.open({
                type: 1,
                title: '导出选项',
                area: ['500px', '400px'],
                content: $('#exportDialog'),
                cancel: () => {
                    this.exportFields = []; // 清空字段选择
                }
            });
        },
        closeExportDialog: function () {
            layer.closeAll();
            this.exportFields = []; // 清空字段选择
        },
        doExport: function () {
            if (this.exportFields.length === 0) {
                layer.msg('请选择至少一个导出字段', { icon: 2 });
                return;
            }
                this.exportCurrentPage();
        },
        exportCurrentPage: function () {
            let headers = [];
            let data = [];

            // 根据选择的字段生成表头
            if (this.exportFields.includes('school')) headers.push('学校');
            if (this.exportFields.includes('user')) headers.push('账号');
            if (this.exportFields.includes('pass')) headers.push('密码');
            if (this.exportFields.includes('kcname')) headers.push('课程名');
            if (this.exportFields.includes('status')) headers.push('状态');
            if (this.exportFields.includes('process')) headers.push('进度');
            if (this.exportFields.includes('remarks')) headers.push('备注');
            if (this.exportFields.includes('fees')) headers.push('扣费');

            // 准备数据
            this.row.data.forEach(item => {
                let row = [];
                if (this.exportFields.includes('school')) row.push(item.school || '');
                if (this.exportFields.includes('user')) row.push(item.user || '');
                if (this.exportFields.includes('pass')) row.push(item.pass || '');
                if (this.exportFields.includes('kcname')) row.push(item.kcname || '');
                if (this.exportFields.includes('status')) row.push(item.status || '');
                if (this.exportFields.includes('process')) row.push(item.process || '');
                if (this.exportFields.includes('remarks')) row.push(item.remarks || '');
                if (this.exportFields.includes('fees')) row.push(item.fees || '');
                data.push(row);
            });

            this.generateExportFile(headers, data);
        },
        generateExportFile: function (headers, data) {
            if (this.exportFormat === 'txt') {
                this.exportAsTxt(headers, data);
            } else {
                this.exportAsExcel(headers, data);
            }
            layer.closeAll();
            this.exportFields = []; // 清空字段选择
        },
        exportAsTxt: function (headers, data) {
            let content = headers.join('\t') + '\n';
            data.forEach(row => {
                content += row.join('\t') + '\n';
            });

            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '订单数据_' + new Date().toLocaleDateString() + '.txt';
            link.click();
            URL.revokeObjectURL(url);
        },
        // 启动进度自动更新
        startAutoRefresh: function() {
            // 清除现有定时器（如果有的话）
            this.stopAutoRefresh();

            // 设置进度自动更新
            this.timer = setInterval(() => {
                // 只有在没有弹窗打开的情况下才自动更新进度
                if (!this.ddinfo3.status) {
                    // 更新订单进度数据
                    this.updateProgressData();
                }
            }, this.autoRefreshInterval * 1000); // 转换为毫秒

            console.log(`进度自动更新已启动，每${this.autoRefreshInterval}秒更新一次`);
        },

        // 停止进度自动更新
        stopAutoRefresh: function() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
                console.log('进度自动更新已停止');
            }
        },

        // 更新订单进度数据（不刷新整个页面）
        updateProgressData: async function() {
            try {
                // 获取当前页面所有订单的ID
                if (!this.row.data || this.row.data.length === 0) {
                    return;
                }

                const oids = this.row.data.map(item => item.oid);

                // 调用进度更新API
                const response = await this.$http.post("/api/progress_update.php", {
                    oids: oids
                }, { emulateJSON: true });

                if (response.data.code === 1) {
                    const progressData = response.data.data;

                    // 更新每个订单的进度信息
                    this.row.data.forEach(order => {
                        const progressInfo = progressData.find(p => p.oid === order.oid);
                        if (progressInfo) {
                            // 只更新进度相关字段，保持其他数据不变
                            order.status = progressInfo.status;
                            order.process = progressInfo.process;
                            order.name = progressInfo.name;
                            order.remarks = progressInfo.remarks;
                            order.yid = progressInfo.yid;
                            order.dockstatus = progressInfo.dockstatus;
                            order.uptime = progressInfo.uptime;
                        }
                    });

                    // 更新最后更新时间
                    this.lastRefreshTime = new Date().toLocaleTimeString();

                    // 在控制台显示更新时间
                    console.log('订单进度已更新：' + this.lastRefreshTime + '，更新了' + progressData.length + '个订单');
                }
            } catch (error) {
                console.error('进度更新失败:', error);
            }
        },

        // 手动刷新（显示loading）
        manualRefresh: function() {
            this.get(this.row.current_page || 1);
        },

        // 切换进度自动更新状态
        toggleAutoRefresh: function() {
            if (this.timer) {
                this.stopAutoRefresh();
                layer.msg('进度自动更新已停止', { icon: 1 });
            } else {
                this.startAutoRefresh();
                layer.msg(`进度自动更新已开启，每${this.autoRefreshInterval}秒更新一次`, { icon: 1 });
            }
        },

        // 显示进度更新设置对话框
        showRefreshSettings: function() {
            const self = this;
            layer.prompt({
                title: '设置进度更新间隔',
                formType: 0,
                value: this.autoRefreshInterval,
                content: '请输入进度更新间隔（秒），建议10-300秒之间'
            }, function(value, index) {
                const interval = parseInt(value);
                if (isNaN(interval) || interval < 10 || interval > 300) {
                    layer.msg('请输入10-300之间的数字', { icon: 2 });
                    return;
                }

                self.autoRefreshInterval = interval;

                // 如果当前正在自动更新，重新启动以应用新间隔
                if (self.timer) {
                    self.startAutoRefresh();
                    layer.msg(`进度更新间隔已设置为${interval}秒`, { icon: 1 });
                } else {
                    layer.msg(`进度更新间隔已设置为${interval}秒，开启自动更新后生效`, { icon: 1 });
                }

                layer.close(index);
            });
        },

        exportAsExcel: function (headers, data) {
            const wb = XLSX.utils.book_new();
            const wsData = [headers, ...data]; // 表头作为第一行，数据随后
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            XLSX.utils.book_append_sheet(wb, ws, '订单数据');
            XLSX.writeFile(wb, '订单数据_' + new Date().toLocaleDateString() + '.xlsx');
        },
        bs: function (oid) {
            layer.confirm('必须在订单出现异常的情况下使用<br>补刷不能提高订单进行速度<br>不正确补刷会造成不可预测的严重后果！<br>请问是否补刷所选的任务？', {
                title: '温馨提示',
                icon: 3,
                title: '安全警告',
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=bs&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        zt: function (oid) {
            layer.confirm('确定停止任务？<br>停止后可根据需要补单即可启动', {
                title: '温馨提示',
                icon: 3,
                title: '警告',
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=zt&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        up: function (oid) {
            var load = layer.load(2);
            $.get("/apisub.php?act=uporder&oid=" + oid, function (data) {
                layer.close(load);
                if (data.code == 1) {
                    vm.get(vm.row.current_page);
                    setTimeout(function () {
                        for (i = 0; i < vm.row.data.length; i++) {
                            if (vm.row.data[i].oid == oid) {
                                vm.ddinfo3.info = vm.row.data[i];
                                console.log(vm.row.data[i].oid);
                                console.log(vm.row.data[i].status);
                                console.log(vm.ddinfo3.info.status);
                                return true;
                            }
                        }
                    }, 1);
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            });
        },
        plzt: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要批量刷新？<br>新订单批量刷新没有作用，不要做傻事', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=plzt", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        plbs: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要入队补刷？<br>请先点击检查订单，未提交到上游的订单补刷后不退', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=plbs", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        plms: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要批量转秒？ ', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=plmiaoshua", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        pltx: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要批量停止？', {
                title: '温馨提示',
                icon: 3,
                btn: ['确认', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=pltx", {
                    sex: sex
                }, {
                    emulateJSON: true
                }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        duijie: function (oid) {
            layer.confirm('确定处理么?', {
                title: '温馨提示',
                icon: 3,
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=duijie&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        ms: function (oid) {
            layer.confirm('确定要执行该操作吗？需要扣除0.01费用。', {
                title: '温馨提示',
                icon: 3,
                title: '警告',
                btn: ['确定', '取消'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=ms&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        xgmm: function (oid) {
            layer.prompt(
                { title: "修改密码（会扣除0.05积分）", formType: 3 },
                function (xgmm, index) {
                    layer.close(index);
                    var load = layer.load();
                    $.get("/apisub.php?act=xgmm&oid=" + oid, { xgmm }, function (data) {
                        layer.close(load);
                        if (data.code == 1) {
                            vm.get(vm.row.current_page);
                            layer.msg(data.msg, { icon: 1 })
                        } else {
                            layer.msg(data.msg, { icon: 2 });
                        }
                    });
                }
            );
        },
        quxiao: function (oid) {
            layer.confirm('你确定要取消订单吗？', {
                title: '警告：',
                icon: 3,
                btn: ['确定取消', '不用了'] //按钮
            }, function () {
                var load = layer.load(2);
                $.get("/apisub.php?act=qx_order&oid=" + oid, function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.get(vm.row.current_page);
                        layer.alert(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                });
            });
        },
        status_text: function (a) {
            var load = layer.load(2);
            $.post("/apisub.php?act=status_order&a=" + a, { sex: this.sex, type: 1 }, { emulateJSON: true }).then(function (data) {
                layer.close(load);
                if (data.code == 1) {
                    vm.selectAll();
                    vm.get(vm.row.current_page);
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            });
        },
        dock: function (a) {
            var load = layer.load(2);
            $.post("/apisub.php?act=status_order&a=" + a, { sex: this.sex, type: 2 }, { emulateJSON: true }).then(function (data) {
                layer.close(load);
                if (data.code == 1) {
                    vm.selectAll();
                    vm.get(vm.row.current_page);
                    layer.msg(data.msg, { icon: 1 });
                } else {
                    layer.msg(data.msg, { icon: 2 });
                }
            });
        },
        selectAll: function () {
            if (this.sex.length == 0) {
                for (i = 0; i < vm.row.data.length; i++) {
                    vm.sex.push(this.row.data[i].oid)
                }
            } else {
                this.sex = []
            }
        },
        ddinfo: function (a) {
            this.ddinfo3.info = a;
            this.ddinfo3.status = true;
            var load = layer.load(2, { time: 300 });
            setTimeout(function () {
                const isMobile = window.innerWidth <= 768;

                // 根据设备类型选择不同的模板
                const contentElement = isMobile ? $('#ddinfo2-mobile').html() : $('#ddinfo2-desktop').html();

                layer.open({
                    type: 1,
                    title: '订单详情',
                    skin: 'layui-layer-demo',
                    closeBtn: 1,
                    anim: 0, // 无动画，直接显示
                    shadeClose: true,
                    area: isMobile ? ['95%', 'auto'] : ['98%', 'auto'],
                    maxmin: false,
                    resize: false,
                    move: false,
                    offset: 'auto', // 自动居中
                    content: contentElement,
                    success: function(layero, index) {
                        // 弹窗打开成功后的回调
                        if (isMobile) {
                            // 移动端特殊处理 - 完全消除留白
                            $('.layui-layer-content').css({
                                'padding': '0',
                                'overflow': 'visible',
                                'height': 'auto',
                                'max-height': 'none'
                            });

                            // 强制移除所有可能的留白
                            layero.css({
                                'padding': '0',
                                'margin': '0'
                            });

                            // 等待内容渲染完成后精确调整高度
                            setTimeout(function() {
                                const $content = $('#ddinfo2-mobile');
                                const contentHeight = $content.outerHeight(true); // 包含margin
                                const windowHeight = window.innerHeight;
                                const titleHeight = layero.find('.layui-layer-title').outerHeight(true) || 42;

                                // 计算最佳高度，留最小边距
                                const totalHeight = contentHeight + titleHeight;
                                const maxHeight = windowHeight * 0.95; // 使用95%屏幕高度

                                if (totalHeight > maxHeight) {
                                    // 内容过多时，使用最大高度并滚动
                                    const contentMaxHeight = maxHeight - titleHeight;
                                    layero.css({
                                        'height': maxHeight + 'px',
                                        'top': (windowHeight * 0.025) + 'px', // 上下各留2.5%
                                        'margin-top': '0'
                                    });
                                    layero.find('.layui-layer-content').css({
                                        'height': contentMaxHeight + 'px',
                                        'overflow-y': 'auto',
                                        'overflow-x': 'hidden'
                                    });
                                } else {
                                    // 内容适中时，精确匹配内容高度
                                    layero.css({
                                        'height': totalHeight + 'px',
                                        'top': '50%',
                                        'margin-top': -(totalHeight / 2) + 'px'
                                    });
                                    layero.find('.layui-layer-content').css({
                                        'height': contentHeight + 'px',
                                        'overflow': 'visible'
                                    });
                                }

                                // 确保内容容器没有多余空间
                                $content.css({
                                    'margin': '0',
                                    'padding': '0'
                                });
                            }, 150);
                        } else {
                            // 桌面端优化显示
                            $('.layui-layer-content').css({
                                'padding': '0',
                                'overflow': 'auto',
                                'max-height': '92vh',
                                'background': '#f8fafc'
                            });

                            // 立即设置正确的位置和尺寸，避免位置跳动
                            const windowWidth = window.innerWidth;
                            const windowHeight = window.innerHeight;
                            const maxWidth = Math.min(1100, windowWidth * 0.98);

                            layero.css({
                                'max-width': maxWidth + 'px',
                                'width': '98%',
                                'position': 'fixed',
                                'left': '50%',
                                'margin-left': -(maxWidth / 2) + 'px',
                                'top': '50%',
                                'transform': 'translateY(-50%)',
                                'margin-top': '0'
                            });

                            // 确保弹窗在视口内，允许更大的高度
                            setTimeout(function() {
                                const layeroHeight = layero.outerHeight();
                                if (layeroHeight > windowHeight * 0.92) {
                                    layero.css({
                                        'height': windowHeight * 0.92 + 'px',
                                        'top': '4%',
                                        'transform': 'none',
                                        'margin-top': '0'
                                    });
                                    $('.layui-layer-content').css({
                                        'overflow-y': 'auto',
                                        'max-height': (windowHeight * 0.92 - 70) + 'px'
                                    });
                                }
                            }, 10);
                        }
                    },
                    end: function () {
                        $("#ddinfo2-desktop").hide();
                        $("#ddinfo2-mobile").hide();
                        vm.ddinfo3.status = false;
                    }
                });
            }, 100);
        },

        // 获取状态对应的CSS类名
        getStatusClass: function(status) {
            const statusMap = {
                '已完成': 'success',
                '进行中': 'primary',
                '考试中': 'primary',
                '上号中': 'primary',
                '待处理': 'warning',
                '待上号': 'warning',
                '排队中': 'warning',
                '已暂停': 'warning',
                '待更新': 'warning',
                '队列中': 'warning',
                '待考试': 'warning',
                '待重启': 'warning',
                '等待下周': 'info',
                '平时分': 'primary',
                '平时分中': 'primary',
                '补刷中': 'info',
                '异常': 'danger',
                '失败': 'danger',
                '密码错误': 'danger',
                '已停止': 'danger'
            };
            return statusMap[status] || 'info';
        },

        // 复制单个内容到剪贴板
        copyToClipboard: function(text) {
            // 使用现代浏览器的Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    layer.msg('已复制到剪贴板', { icon: 1 });
                }).catch(err => {
                    console.error('复制失败:', err);
                    this.fallbackCopyTextToClipboard(text);
                });
            } else {
                // 降级方案
                this.fallbackCopyTextToClipboard(text);
            }
        },

        // 降级复制方案
        fallbackCopyTextToClipboard: function(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    layer.msg('已复制到剪贴板', { icon: 1 });
                } else {
                    layer.msg('复制失败，请手动复制', { icon: 2 });
                }
            } catch (err) {
                console.error('复制失败:', err);
                layer.msg('复制失败，请手动复制', { icon: 2 });
            }

            document.body.removeChild(textArea);
        },

        // 复制全部账号信息
        copyAllAccountInfo: function(orderInfo) {
            const accountText = `学校: ${orderInfo.school}\n账号: ${orderInfo.user}\n密码: ${orderInfo.pass}`;
            this.copyToClipboard(accountText);
        },
        tk: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要退款吗？陛下，三思三思！！！', {
                title: '温馨提示',
                icon: 3,
                btn: ['确定', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=tk", { sex: sex }, { emulateJSON: true }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        sc: function (sex) {
            if (this.sex == '') {
                layer.msg("请先选择订单！");
                return false;
            }
            layer.confirm('确定要删除此订单信息？', {
                title: '温馨提示',
                icon: 3,
                btn: ['确定', '取消']
            }, function () {
                var load = layer.load();
                $.post("/apisub.php?act=sc", { sex: sex }, { emulateJSON: true }).then(function (data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.selectAll();
                        vm.get(vm.row.current_page);
                        layer.msg(data.msg, { icon: 1 });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            });
        },
        feedback: function (oid) {
            var self = this;
            layer.prompt({
                title: '（当前：' + oid + ') 无效反馈扣服务费1.5/单',
                formType: 2,
                value: '老板帮忙看看这个（1什么问题）' + '，我（2通过什么方式了解到）确实是有这个问题，' + '（3如果有需要可以添加）客户说' + '，帮忙优先处理一下感谢老板！',
                area: ['350px', '250px'],
                btn: ['确定', '取消']
            }, function (feedbackText, index) {
                layer.close(index);
                feedbackText = feedbackText.trim();

                if (feedbackText === '') {
                    layer.msg('反馈内容不能为空', { icon: 2 });
                    return;
                }
                layer.confirm('承诺:全天多次不定时处理，数分钟至数小时内回复' + '<br>我确实遇到了订单上存在的难题√' + '<br>我已定位到该问题并非我个人原因造成√' + '<br>我已初步核查问题，保证反馈的问题绝对真实√' + '<br>若反馈无效问题，我自愿被扣除服务费1.5/单√' + '<br>我希望反馈的问题能尽快解决，请尽快处理。', {
                    icon: 3,
                    title: '二次确认',
                    btn: ['确定并提交反馈', '取消']
                }, function (index) {
                    layer.close(index);

                    var load = layer.load();
                    $.get("/mqgd.php?act=feedback&oid=" + oid, { feedback: feedbackText }, function (data) {
                        layer.close(load);
                        if (data.code === 1) {
                            layer.msg('反馈成功：' + data.msg, { icon: 1 });
                        } else {
                            layer.msg('ERROR' + data.msg, { icon: 2 });
                        }
                    });
                }, function (index) {
                    layer.close(index);
                });
            });
        },
        copyToClipboard(text) {
            // 移动端优化的复制功能
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    // 移动端使用更简洁的提示
                    if (window.innerWidth <= 768) {
                        layer.msg('已复制', { icon: 1, time: 1500 });
                    } else {
                        layer.msg('已复制: ' + (text.length > 20 ? text.substring(0, 20) + '...' : text), { icon: 1, time: 2000 });
                    }
                }, (err) => {
                    layer.msg('复制失败', { icon: 2 });
                    console.error('复制失败:', err);
                });
            } else {
                // 降级方案
                try {
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    textarea.style.position = 'fixed';
                    textarea.style.left = '-999999px';
                    textarea.style.top = '-999999px';
                    document.body.appendChild(textarea);
                    textarea.focus();
                    textarea.select();

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textarea);

                    if (successful) {
                        if (window.innerWidth <= 768) {
                            layer.msg('已复制', { icon: 1, time: 1500 });
                        } else {
                            layer.msg('已复制: ' + (text.length > 20 ? text.substring(0, 20) + '...' : text), { icon: 1, time: 2000 });
                        }
                    } else {
                        layer.msg('复制失败，请手动复制', { icon: 2 });
                    }
                } catch (err) {
                    layer.msg('复制失败，请手动复制', { icon: 2 });
                    console.error('复制失败:', err);
                }
            }
        },
        copyAllToClipboard(res) {
            const combinedText = `学校: ${res.school}\n账号: ${res.user}\n密码: ${res.pass}`;

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(combinedText).then(() => {
                    if (window.innerWidth <= 768) {
                        layer.msg('已复制全部信息', { icon: 1, time: 1500 });
                    } else {
                        layer.msg('已复制全部账号信息', { icon: 1, time: 2000 });
                    }
                }, (err) => {
                    layer.msg('复制失败', { icon: 2 });
                    console.error('复制失败:', err);
                });
            } else {
                try {
                    const textarea = document.createElement('textarea');
                    textarea.value = combinedText;
                    textarea.style.position = 'fixed';
                    textarea.style.left = '-999999px';
                    textarea.style.top = '-999999px';
                    document.body.appendChild(textarea);
                    textarea.focus();
                    textarea.select();

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textarea);

                    if (successful) {
                        if (window.innerWidth <= 768) {
                            layer.msg('已复制全部信息', { icon: 1, time: 1500 });
                        } else {
                            layer.msg('已复制全部账号信息', { icon: 1, time: 2000 });
                        }
                    } else {
                        layer.msg('复制失败，请手动复制', { icon: 2 });
                    }
                } catch (err) {
                    layer.msg('复制失败，请手动复制', { icon: 2 });
                    console.error('复制失败:', err);
                }
            }
        },

        // 切换移动端搜索区域显示
        toggleMobileSearch() {
            this.mobileSearchExpanded = !this.mobileSearchExpanded;
        },

        // 切换移动端批量操作区域显示
        toggleMobileBatch() {
            this.mobileBatchExpanded = !this.mobileBatchExpanded;
        },

        // 移动端优化初始化
        initMobileOptimizations() {
            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // 添加移动端特有的事件监听
                this.addMobileEventListeners();

                // 优化滚动性能
                this.optimizeScrolling();

                // 添加快速操作提示
                this.showMobileTips();

                // 优化移动端弹窗
                this.optimizeMobileDialogs();
            }

            // 监听屏幕方向变化
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    this.handleOrientationChange();
                }, 100);
            });

            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                this.handleResize();
            });
        },

        // 添加移动端事件监听
        addMobileEventListeners() {
            // 防止双击缩放
            document.addEventListener('touchstart', function(event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, { passive: false });

            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
        },

        // 优化滚动性能
        optimizeScrolling() {
            // 为表格容器添加平滑滚动
            const tableContainer = document.querySelector('.table-responsive');
            if (tableContainer) {
                tableContainer.style.webkitOverflowScrolling = 'touch';
                tableContainer.style.scrollBehavior = 'smooth';
            }
        },

        // 显示移动端使用提示 - 已禁用
        showMobileTips() {
            // 不再显示弹窗提示，保持界面简洁
            return;
        },

        // 处理屏幕方向变化
        handleOrientationChange() {
            // 重新计算布局
            this.$nextTick(() => {
                // 触发重新渲染
                this.$forceUpdate();
            });
        },

        // 处理窗口大小变化
        handleResize() {
            // 防抖处理
            clearTimeout(this.resizeTimer);
            this.resizeTimer = setTimeout(() => {
                const isMobile = window.innerWidth <= 768;

                // 根据屏幕大小调整显示模式
                if (isMobile && !this.mobileMode) {
                    this.mobileMode = true;
                    this.optimizeScrolling();
                    this.optimizeMobileDialogs();
                } else if (!isMobile && this.mobileMode) {
                    this.mobileMode = false;
                }
            }, 250);
        },

        // 优化移动端弹窗显示
        optimizeMobileDialogs() {
            // 重写layer的默认配置
            if (typeof layer !== 'undefined') {
                // 保存原始的layer.open方法
                const originalOpen = layer.open;

                layer.open = function(options) {
                    if (window.innerWidth <= 768) {
                        // 移动端弹窗优化配置
                        options = options || {};

                        // 设置移动端适配的area
                        if (!options.area) {
                            options.area = ['90%', 'auto'];
                        }

                        // 设置移动端适配的offset
                        if (!options.offset) {
                            options.offset = 'auto';
                        }

                        // 确保弹窗在视口内
                        options.fixed = true;
                        options.resize = false;
                        options.move = false;

                        // 移动端样式类
                        options.skin = (options.skin || '') + ' mobile-dialog';
                    }

                    return originalOpen.call(this, options);
                };

                // 重写layer.confirm方法
                const originalConfirm = layer.confirm;
                layer.confirm = function(content, options, yes, cancel) {
                    if (window.innerWidth <= 768) {
                        options = options || {};
                        options.area = ['85%', 'auto'];
                        options.offset = 'auto';
                        options.fixed = true;
                        options.resize = false;
                        options.move = false;
                        options.skin = (options.skin || '') + ' mobile-confirm';
                    }

                    return originalConfirm.call(this, content, options, yes, cancel);
                };

                // 重写layer.msg方法
                const originalMsg = layer.msg;
                layer.msg = function(content, options, end) {
                    if (window.innerWidth <= 768) {
                        options = options || {};
                        options.offset = 'auto';
                        options.area = ['auto', 'auto'];
                        options.fixed = true;
                        options.skin = (options.skin || '') + ' mobile-msg';
                    }

                    return originalMsg.call(this, content, options, end);
                };
            }
        }
    },
    mounted() {
        this.get(1);
        // 启动自动刷新定时器，每30秒刷新一次
        this.startAutoRefresh();

        // 移动端优化：添加触摸事件监听
        this.initMobileOptimizations();
    },
    beforeDestroy() {
        // 页面销毁前清除定时器
        this.stopAutoRefresh();
    }
});
</script>