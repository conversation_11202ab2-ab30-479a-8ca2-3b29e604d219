<?php
include('../confing/common.php');
if ($islogin != 1) {
    exit("<script language='javascript'>window.location.href='login.php';</script>");
}
?>
<!DOCTYPE html>
<html>
<head>
    <title><?=$conf['sitename']?></title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <link rel="stylesheet" href="../assets/css/index.css">
    <link rel="stylesheet" href="../assets/yqsladmin/css/index.css">
    <link rel="stylesheet" href="../assets/yqsladmin/css/theme.css">
    <script>
    if (self !== top) {
        top.location.href = window.location.href;
    }
</script>
</head>
<body>
<div class="app" :class="['theme-0', 'theme-' + themeV, (is_fold ? 'fold' : ''), (is_menu_hidden ? 'menu-hidden' : '')]" style="display: none;" :style="'display: block;'">
    <!-- 左 -->
    <div class="nav-left">
        <!-- 左上 -->
        <div class="nav-left-top" :title="title" @click="nativePage = pageList[0]">
            <img src="<?=$conf['logo']?>" class="admin-logo">
            <span class="nav-title" style="font-size: 1.1em;"><?=$conf['sitename']?></span>
        </div>
        <!-- 左下 -->
        <div class="nav-left-bottom">
            <div class="menu-box-1">
                <div class="menu-box-2">
                    <el-menu default-active="2"
                             class="el-menu-style-1"
                             :unique-opened="true"
                             :default-active="default_active"
                             :collapse="is_fold"
                             @select="selectMenu">
                        <!-- 数据看板 -->
                        <el-menu-item index="home">
                            <i class="el-icon-house" title="系统主页"></i>
                            <span class="menu-name">系统主页</span>
                        </el-menu-item>
                        <!-- 站点管理 - 仅 UID=1 显示 -->
                        <?php if ($userrow['uid'] == 1) { ?>
                        <el-submenu index="senior">
                            <template slot="title">
                                <i class="el-icon-setting" title="站点管理"></i>
                                <span class="menu-name">站点管理</span>
                            </template>
                            <el-submenu index="web">
                                <template slot="title">
                                    <i class="el-icon-monitor"></i>
                                    <span>网站管理</span>
                                </template>
                                <el-menu-item index="webest">网站设置</el-menu-item>
                                <el-menu-item index="kmguanli">卡密管理</el-menu-item>
                                <el-menu-item index="version">系统信息</el-menu-item>
                            </el-submenu>
                            <el-submenu index="goods">
                                <template slot="title">
                                    <i class="el-icon-shopping-cart-2"></i>
                                    <span>商品管理</span>
                                </template>
                                <el-menu-item index="fenlei">分类设置</el-menu-item>
                                <el-menu-item index="huoyuan">货源设置</el-menu-item>
                                <el-menu-item index="jkjg">货源监控</el-menu-item>
                                <el-menu-item index="yjdj">快速对接</el-menu-item>
                                <el-menu-item index="class">商品设置</el-menu-item>
                                <el-menu-item index="zhiyapeizhi">质押配置</el-menu-item>
                                <el-menu-item index="mijia">密价列表</el-menu-item>
                            </el-submenu>
                            <el-menu-item index="dengji"><i class="el-icon-user"></i>用户等级</el-menu-item>
                            <el-menu-item index="paylist"><i class="el-icon-wallet"></i>充值记录</el-menu-item>
                            <el-menu-item index="data"><i class="el-icon-data-analysis"></i>数据统计</el-menu-item>
                        </el-submenu>
                        <?php } ?>
                        <!-- 订单提交 -->
                        <el-submenu index="orderadd">
                            <template slot="title">
                                <i class="el-icon-folder-add" title="订单提交"></i>
                                <span class="menu-name">订单提交</span>
                            </template>
                            <el-menu-item index="add1"><i class="el-icon-plus"></i>经典提交</el-menu-item>
                            <el-menu-item index="add2"><i class="el-icon-mobile-phone"></i>小猿提交</el-menu-item>
                            <el-menu-item index="add3"><i class="el-icon-document-checked"></i>无查提交</el-menu-item>
                            <el-menu-item index="tuijianpd"><i class="el-icon-top"></i>畅销排行</el-menu-item>
                        </el-submenu>
                        <!-- 订单管理 -->
                        <el-submenu index="lists">
                            <template slot="title">
                                <i class="el-icon-folder-remove" title="订单管理"></i>
                                <span class="menu-name">订单管理</span>
                            </template>
                            <el-menu-item index="list"><i class="el-icon-document-remove"></i>订单列表</el-menu-item>
                            <el-menu-item index="orderhedui"><i class="el-icon-guide"></i>账号核对</el-menu-item>
                            <el-menu-item index="userbiao"><i class="el-icon-s-data"></i>账号统计</el-menu-item>
                            <el-menu-item index="kcid"><i class="el-icon-s-grid"></i>KCID比对</el-menu-item>
                            <el-menu-item index="userdata"><i class="el-icon-s-marketing"></i>数据中心</el-menu-item>
                        </el-submenu>
                        <!-- 运动打卡 -->
                        <el-submenu index="XYYD">
                            <template slot="title">
                                <i class="el-icon-basketball" title="运动打卡"></i>
                                <span class="menu-name">运动打卡</span>
                            </template>
                            <el-menu-item index="xyyd"></i>运动打卡</el-menu-item>
                        </el-submenu>
                        <!-- 文章撰写 -->
                        <el-submenu index="LWZX">
                            <template slot="title">
                                <i class="el-icon-edit" title="文章撰写"></i>
                                <span class="menu-name">文章撰写</span>
                            </template>
                            <el-menu-item index="lwbj"></i>文章撰写</el-menu-item>
                        </el-submenu>
                        <!-- 代理系统 -->
                        <el-submenu index="charge">
                            <template slot="title">
                                <i class="el-icon-s-custom" title="代理系统"></i>
                                <span class="menu-name">代理系统</span>
                            </template>
                            <el-menu-item index="charge"><i class="el-icon-star-off"></i>我的上级</el-menu-item>
                            <el-menu-item index="userlist"><i class="el-icon-user"></i>我的代理</el-menu-item>
                            <el-menu-item index="gonggao"><i class="el-icon-s-flag"></i>公告设置</el-menu-item>
                        </el-submenu>
                        <!-- API 管理 -->
                        <el-submenu index="center">
                            <template slot="title">
                                <i class="el-icon-link" title="API 管理"></i>
                                <span class="menu-name">API 管理</span>
                            </template>
                            <el-menu-item index="apiinfo">对接信息</el-menu-item>
                            <el-menu-item index="docking">对接文档</el-menu-item>
                            <el-menu-item index="shoppinghelp">商品列表</el-menu-item>
                            <el-menu-item index="fenleibiao">分类列表</el-menu-item>
                            <el-menu-item index="spzxsj">最新上架</el-menu-item>
                        </el-submenu>
                        <!-- 个人中心 -->
                        <el-submenu index="user">
                            <template slot="title">
                                <i class="el-icon-user" title="个人中心"></i>
                                <span class="menu-name">个人中心</span>
                            </template>
                            <el-menu-item index="userinfo">我的信息</el-menu-item>
                            <?php if ($userrow['uuid'] == 1) { ?>
                            <el-menu-item index="pay">充值中心</el-menu-item>
                            <el-menu-item index="zhiya">质押优惠</el-menu-item>
                            <?php } ?>
                            <el-menu-item index="gongdan">工单反馈</el-menu-item>
                            <el-menu-item index="price">价格说明</el-menu-item>
                            <el-menu-item index="tuisong">推送设置</el-menu-item>
                            <el-menu-item index="log">操作日志</el-menu-item>
                        </el-submenu>
                    </el-menu>
                </div>
            </div>
        </div>
    </div>
    <!-- 右边 -->
    <div class="nav-right">
        <div class="nav-right-1">
            <div class="tools-left">
                <span v-if="!is_mobile">
                    <!-- 电脑端：原有折叠/展开逻辑 -->
                    <span title="折叠菜单" class="tool-fox" v-if="is_fold == false" @click="is_fold = true">
                        <i class="el-icon-s-fold"></i>
                    </span>
                    <span title="展开菜单" class="tool-fox" v-if="is_fold == true" @click="is_fold = false">
                        <i class="el-icon-s-unfold"></i>
                    </span>
                </span>
                <span v-else>
                    <!-- 小屏端：切换隐藏/显示（强制折叠） -->
                    <span title="显示菜单" class="tool-fox" v-if="is_menu_hidden" @click="is_menu_hidden = false">
                        <i class="el-icon-s-unfold"></i>
                    </span>
                    <span title="隐藏菜单" class="tool-fox" v-if="!is_menu_hidden" @click="is_menu_hidden = true">
                        <i class="el-icon-s-fold"></i>
                    </span>
                </span>
                <!-- 刷新按钮不变 -->
                <span title="刷新" class="tool-fox" @click="rightPage = nativePage; right_f5();">
                    <i class="el-icon-refresh-right" style="font-weight: bold;"></i>
                </span>
            </div>
            <div class="tools-right">
                <span title="我的信息" class="tool-fox user-info" style="padding: 0;">
                    <el-dropdown @command="handleCommand" trigger="click" size="medium">
                        <span class="el-dropdown-link user-name" style="height: 100%; padding: 0 1em; display: inline-block;">
                            <img src="http://q2.qlogo.cn/headimg_dl?dst_uin=<?=$userrow['user'];?>&spec=100"
                                 class="user-avatar"
                                 style="border-radius: 25px; width: 30px; height: 30px; border: 0px;">
                            <?=$userrow['name'];?>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="sjqy">上级迁移</el-dropdown-item>
                            <el-dropdown-item command="passwd">修改密码</el-dropdown-item>
                            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </span>
                    <span title="主题" class="tool-fox user-info" style="padding: 0;">
                        <el-dropdown @command="toggleTheme" trigger="click" size="medium">
                            <span class="el-dropdown-link" style="height: 100%; padding: 0 1em; display: inline-block;">
                                <i class="el-icon-brush" ></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="t.value" v-for="t in themeList" :key="t.name">
                                    <span :style="themeV == t.value ? 'color: blue' : ''">{{t.name}}</span>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </span>
                    <span title="全屏" class="tool-fox" v-if="is_full_screen == false" @click="is_full_screen = true">
                        <i class="el-icon-rank" style="transform: rotate(45deg)"></i>
                    </span>
                    <span title="退出全屏" class="tool-fox" v-if="is_full_screen == true" @click="is_full_screen = false">
                        <i class="el-icon-bottom-left" ></i>
                    </span>
                </div>
            </div>
            <!-- 第二杠 -->
            <div class="nav-right-2">
                <div class="towards-left" @click="scrollToLeft" title="向左滑">
                    <i class="el-icon-arrow-left"></i>
                </div>
                <div class="towards-middle">
                    <div class="page-title-box" :style="{left: scrollX + 'px'}">
                        <div class="page-title" :class="(page == nativePage ? 'page-native' : '')"
                             v-for="page in pageList"
                             @click="showPage(page)"
                             @contextmenu.prevent="right_click(page, $event)">
                            <i class="el-icon-caret-left"></i>
                            <span>{{page.name}}</span>
                            <i class="el-icon-document-copy" title="多开当前页面" @click.stop="multiOpenPage(page)" v-if="page.id !== 'home'"></i>
                            <i class="el-icon-close" v-if="!page.hide_close" @click.stop="closePage(page)"></i>
                        </div>
                    </div>
                </div>
                <div class="towards-right" @click="scrollToRight" title="向右滑">
                    <i class="el-icon-arrow-right"></i>
                </div>
            </div>
            <!-- 第三杠 -->
            <div class="nav-right-3">
                <span v-for="page in pageList" :key="page.id">
                    <iframe :src="page.url" :class="'iframe' + page.id" :style="page == nativePage ? 'z-index: 101' : null"></iframe>
                </span>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/vue.js"></script>
<script src="../assets/js/index.js"></script>
<script src="../assets/yqsladmin/js/index.js"></script>
<script src="../assets/yqsladmin/js/admin-util.js"></script>
<script src="../assets/js/axios.js"></script>
<script src="../assets/yqsladmin/js/tools.js"></script>
<script src="../assets/js/sy.js"></script>
<?php if ($conf['sykg']==1) {?>
<script type="text/javascript">
watermark('禁止截图，截图封户','昵称 : <?=$userrow['name'];?>','账号:<?=$userrow['user'];?>');
</script>
<?}?>
</body>
</html>