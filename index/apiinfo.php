<?php
include('head.php');
?>
<style>
.ip-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.ip-item {
    flex: 1 1 calc(33.333% - 20px);
    background: #f9f9f9;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}
.ip-item:hover {
    background: #f0f8ff;
    border-color: #cce5ff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.ip-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}
.ip-address {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}
.ip-details {
    font-size: 12px;
    color: #666;
}
.ip-time, .ip-action {
    display: block;
}
.layui-input-block {
    min-width: 300px;
}
.layui-card {
    border-radius: 7px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.key-button {
    width: 100%;
    padding: 8px;
    font-size: 14px;
    text-align: center;
    background: #f0f8ff;
    border: 1px solid #cce5ff;
    border-radius: 4px;
    cursor: pointer;
}
.key-button:hover {
    background: #e6f0ff;
}

.form-row {
    display: flex;
    align-items: center;
    gap: 10px;
}
.form-row .layui-form-label {
    margin: 0;
    padding: 0;
    width: auto;
    min-width: 60px;
}
</style>
<div class="app-content-body">
    <div class="wrapper-md control" id="apiInfo">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">API管理</div>
                    <div class="layui-card-body">
                        <!-- 对接信息卡片 -->
                        <div class="layui-card">
                            <div class="layui-card-header">我的信息</div>
                            <div class="layui-card-body">
                                <div class="form-row">
                                    <label class="layui-form-label">对接账号(UID)：</label>
                                    <span>{{ row.uid }}</span>
                                </div>
                                <br>
                                <button v-if="row.key == 0" @click="ktapi" class="key-button">开通API KEY</button>
                                <button v-else class="key-button" @click="fetchApiKey">获取API KEY</button>
                                <div v-if="row.key != 0" style="margin-top: 10px;">
                                    <button @click="gbapi" class="btn btn-xs btn-warning" style="margin-left: 10px;">关闭KEY</button>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <!-- IP记录信息卡片 -->
                        <div class="layui-card">
                            <div class="layui-card-header">最近使用的IP</div>
                            <div class="layui-card-body">
                                <div class="ip-list">
                                    <div v-for="ip in userIps" :key="ip.ip" class="ip-item">
                                        <div class="ip-info">
                                            <div class="ip-address">{{ ip.ip }}</div>
                                            <div class="ip-details">
                                                <span class="ip-time">最后使用时间: {{ ip.最后使用时间 }}</span>
                                                <span class="ip-action">操作内容: {{ ip.操作内容 }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
require_once("footer.php");
require_once("lightyearfooter.php");
?>
<script type="text/javascript">
    var vm = new Vue({
        el: "#apiInfo",
        data: {
            row: { uid: '', key: 0, tuisongtoken: '' }, // 初始化用户信息
            userIps: [], // IP记录列表
        },
        methods: {
            // 获取用户信息
            userinfo: function () {
                var load = layer.load(2);
                this.$http.post("/apisub.php?act=userinfo")
                    .then(function (data) {
                        layer.close(load);
                        if (data.data.code == 1) {
                            this.row = data.data;
                        } else {
                            layer.alert(data.data.msg, { icon: 2 });
                        }
                    });
            },
            // 获取用户IP记录
            getUserIps: function () {
                var load = layer.load(2);
                axios.get("/apisub.php?act=get_user_ips")
                    .then(function (data) {
                        layer.close(load);
                        if (data.data.code == 1) {
                            vm.userIps = data.data.ips;
                        } else {
                            layer.msg(data.data.msg, { icon: 2 });
                        }
                    });
            },
            // 开通API KEY
            ktapi: function () {
                layer.confirm('后台余额满200免费开通 未满200扣除10积分', {
                    title: '温馨提示',
                    icon: 1,
                    btn: ['确定', '取消']
                }, function () {
                    var load = layer.load(2);
                    axios.get("/apisub.php?act=ktapi&type=1")
                        .then(function (data) {
                            layer.close(load);
                            if (data.data.code == 1) {
                                layer.alert(data.data.msg, { icon: 1, title: "温馨提示" });
                                vm.userinfo(); // 刷新用户信息
                            } else {
                                layer.msg(data.data.msg, { icon: 2 });
                            }
                        });
                });
            },
            // 关闭API KEY
            gbapi: function () {
                layer.confirm('确定关闭KEY吗，关闭之后无法使用对接功能！', {
                    title: '温馨提示',
                    icon: 1,
                    btn: ['确定', '取消']
                }, function () {
                    var load = layer.load(2);
                    axios.get("/apisub.php?act=ktapi&type=4")
                        .then(function (data) {
                            layer.close(load);
                            if (data.data.code == 1) {
                                layer.alert(data.data.msg, { icon: 1, title: "温馨提示" });
                                vm.userinfo(); // 刷新用户信息
                            } else {
                                layer.msg(data.data.msg, { icon: 2 });
                            }
                        });
                });
            },
            // 点击获取API KEY
            fetchApiKey: function () {
                var load = layer.load(2);
                axios.get("/apisub.php?act=ktapi&type=3")
                    .then(function (data) {
                        layer.close(load);
                        if (data.data.code == 1 && data.data.key) {
                            layer.alert('您的API KEY: ' + data.data.key +'<br> 本站不存储明文秘钥，秘钥一次性生成，请立即保存', { icon: 1, title: "API KEY" });
                        } else {
                            layer.msg(data.data.msg, { icon: 2 });
                        }
                    });
            }
        },
        mounted() {
            this.userinfo(); // 页面加载时获取用户信息
            this.getUserIps(); // 页面加载时获取IP记录
        }
    });
</script>
</body>
</html>