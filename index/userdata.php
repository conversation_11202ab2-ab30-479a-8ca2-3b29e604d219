<?php
include('head.php');
// 获取当前日期和前几天的日期
$today = date('Y-m-d');
$tomorrow = date('Y-m-d', strtotime('+1 day'));
$jtdate = $today . ' 00:00:00';
$ztdate = date('Y-m-d', strtotime('-1 day')) . ' 00:00:00';
$qtdate = date('Y-m-d', strtotime('-2 day')) . ' 00:00:00';
$wqtdate = date('Y-m-d', strtotime('-3 day')) . ' 00:00:00';
$wwqtdate = date('Y-m-d', strtotime('-4 day')) . ' 00:00:00';
$wwwqtdate = date('Y-m-d', strtotime('-5 day')) . ' 00:00:00';
$wwwwqtdate = date('Y-m-d', strtotime('-6 day')) . ' 00:00:00';

// 近七天的订单数量
$today_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$jtdate' and addtime < '$tomorrow'");
$today_1_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$ztdate' and addtime < '$jtdate'");
$today_2_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$qtdate' and addtime < '$ztdate'");
$today_3_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$wqtdate' and addtime < '$qtdate'");
$today_4_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$wwqtdate' and addtime < '$wqtdate'");
$today_5_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$wwwqtdate' and addtime < '$wwqtdate'");
$today_6_dd = $DB->count("select count(*) from qingka_wangke_order where uid='{$userrow['uid']}' and addtime >= '$wwwwqtdate' and addtime < '$wwwqtdate'");

// 近七天的每天余额总消费数量 (从 qingka_wangke_log 的 money < 0，取反得到消费)
$today_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$jtdate' AND addtime < '$tomorrow'");
$today_zxh_sum = 0;
while ($today_zxh_row = $DB->fetch($today_zxh_res)) {
    $today_zxh_sum += $today_zxh_row['money'];
}
$today_zxh = -$today_zxh_sum;
$today_1_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$ztdate' AND addtime < '$jtdate'");
$today_1_zxh_sum = 0;
while ($today_1_zxh_row = $DB->fetch($today_1_zxh_res)) {
    $today_1_zxh_sum += $today_1_zxh_row['money'];
}
$today_1_zxh = -$today_1_zxh_sum;
$today_2_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$qtdate' AND addtime < '$ztdate'");
$today_2_zxh_sum = 0;
while ($today_2_zxh_row = $DB->fetch($today_2_zxh_res)) {
    $today_2_zxh_sum += $today_2_zxh_row['money'];
}
$today_2_zxh = -$today_2_zxh_sum;
$today_3_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$wqtdate' AND addtime < '$qtdate'");
$today_3_zxh_sum = 0;
while ($today_3_zxh_row = $DB->fetch($today_3_zxh_res)) {
    $today_3_zxh_sum += $today_3_zxh_row['money'];
}
$today_3_zxh = -$today_3_zxh_sum;
$today_4_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$wwqtdate' AND addtime < '$wqtdate'");
$today_4_zxh_sum = 0;
while ($today_4_zxh_row = $DB->fetch($today_4_zxh_res)) {
    $today_4_zxh_sum += $today_4_zxh_row['money'];
}
$today_4_zxh = -$today_4_zxh_sum;
$today_5_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$wwwqtdate' AND addtime < '$wwqtdate'");
$today_5_zxh_sum = 0;
while ($today_5_zxh_row = $DB->fetch($today_5_zxh_res)) {
    $today_5_zxh_sum += $today_5_zxh_row['money'];
}
$today_5_zxh = -$today_5_zxh_sum;
$today_6_zxh_res = $DB->query("SELECT money FROM qingka_wangke_log WHERE uid='{$userrow['uid']}' AND money < 0 AND addtime >= '$wwwwqtdate' AND addtime < '$wwwqtdate'");
$today_6_zxh_sum = 0;
while ($today_6_zxh_row = $DB->fetch($today_6_zxh_res)) {
    $today_6_zxh_sum += $today_6_zxh_row['money'];
}
$today_6_zxh = -$today_6_zxh_sum;

// 近七天的每天订单余额总消费数量 (从 qingka_wangke_order 的 fees)
$today_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$jtdate' AND addtime < '$tomorrow'");
$today_ddxh_sum = 0;
while ($today_ddxh_row = $DB->fetch($today_ddxh_res)) {
    $today_ddxh_sum += $today_ddxh_row['fees'];
}
$today_ddxh = $today_ddxh_sum;
$today_1_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$ztdate' AND addtime < '$jtdate'");
$today_1_ddxh_sum = 0;
while ($today_1_ddxh_row = $DB->fetch($today_1_ddxh_res)) {
    $today_1_ddxh_sum += $today_1_ddxh_row['fees'];
}
$today_1_ddxh = $today_1_ddxh_sum;
$today_2_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$qtdate' AND addtime < '$ztdate'");
$today_2_ddxh_sum = 0;
while ($today_2_ddxh_row = $DB->fetch($today_2_ddxh_res)) {
    $today_2_ddxh_sum += $today_2_ddxh_row['fees'];
}
$today_2_ddxh = $today_2_ddxh_sum;
$today_3_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$wqtdate' AND addtime < '$qtdate'");
$today_3_ddxh_sum = 0;
while ($today_3_ddxh_row = $DB->fetch($today_3_ddxh_res)) {
    $today_3_ddxh_sum += $today_3_ddxh_row['fees'];
}
$today_3_ddxh = $today_3_ddxh_sum;
$today_4_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$wwqtdate' AND addtime < '$wqtdate'");
$today_4_ddxh_sum = 0;
while ($today_4_ddxh_row = $DB->fetch($today_4_ddxh_res)) {
    $today_4_ddxh_sum += $today_4_ddxh_row['fees'];
}
$today_4_ddxh = $today_4_ddxh_sum;
$today_5_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$wwwqtdate' AND addtime < '$wwqtdate'");
$today_5_ddxh_sum = 0;
while ($today_5_ddxh_row = $DB->fetch($today_5_ddxh_res)) {
    $today_5_ddxh_sum += $today_5_ddxh_row['fees'];
}
$today_5_ddxh = $today_5_ddxh_sum;
$today_6_ddxh_res = $DB->query("SELECT fees FROM qingka_wangke_order WHERE uid='{$userrow['uid']}' AND addtime >= '$wwwwqtdate' AND addtime < '$wwwqtdate'");
$today_6_ddxh_sum = 0;
while ($today_6_ddxh_row = $DB->fetch($today_6_ddxh_res)) {
    $today_6_ddxh_sum += $today_6_ddxh_row['fees'];
}
$today_6_ddxh = $today_6_ddxh_sum;

// 近7天的每天其他余额消费数量 (总消费 - 订单消费)
$today_qtxh = $today_zxh - $today_ddxh;
$today_1_qtxh = $today_1_zxh - $today_1_ddxh;
$today_2_qtxh = $today_2_zxh - $today_2_ddxh;
$today_3_qtxh = $today_3_zxh - $today_3_ddxh;
$today_4_qtxh = $today_4_zxh - $today_4_ddxh;
$today_5_qtxh = $today_5_zxh - $today_5_ddxh;
$today_6_qtxh = $today_6_zxh - $today_6_ddxh;
?>
    <style>
        .layui-card {
            border-radius: 7px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .el-date-editor.el-input {
            width: 200px;
        }
        .el-button {
            margin-left: 10px;
        }
        .el-table {
            margin-top: 10px;
        }
        .layui-col-md6, .layui-col-xs-12 {
            padding: 0 10px;
        }
        .layui-row {
            margin: 0 -10px;
        }
        .layui-col-md12 {
            padding: 0;
        }
        .layui-card-header {
            font-weight: bold;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        .layui-card-body {
            padding: 20px;
        }
        .chart-container {
            height: 300px;
        }
        .date-section {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .date-section .el-button {
            margin-left: 15px;
        }
        .date-section .el-date-editor {
            margin-right: 10px;
        }
    </style>
<div class="app-content-body">
    <div class="wrapper-md control" id="dataCenter">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">数据中心</div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6 layui-col-xs-12">
                                <div class="layui-card">
                                    <div class="layui-card-header">近七天订单数量</div>
                                    <div class="layui-card-body">
                                        <div ref="orderChart" class="chart-container"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-xs-12">
                                <div class="layui-card">
                                    <div class="layui-card-header">近七天积分消费</div>
                                    <div class="layui-card-body">
                                        <div ref="consumeChart" class="chart-container"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <!-- 商品销售数据 -->
                        <div class="layui-card">
                            <div class="layui-card-header">商品购买数据</div>
                            <div class="layui-card-body">
                                <div class="date-section">
                                    <el-date-picker
                                        v-model="orderDate"
                                        type="date"
                                        placeholder="选择日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="pickerOptions">
                                    </el-date-picker>
                                    <el-button type="primary" @click="fetchOrderData">搜索</el-button>
                                </div>
                                <el-table
                                    :data="orderTableData"
                                    style="width: 100%"
                                    border>
                                    <el-table-column
                                        prop="ptname"
                                        label="商品名称"
                                        width="180">
                                    </el-table-column>
                                    <el-table-column
                                        prop="order_count"
                                        label="订单数量">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <hr>
                        <!-- 积分消耗明细 -->
                        <div class="layui-card">
                            <div class="layui-card-header">积分消耗明细</div>
                            <div class="layui-card-body">
                                <div class="date-section">
                                    <el-date-picker
                                        v-model="moneyDate"
                                        type="date"
                                        placeholder="选择日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="pickerOptions">
                                    </el-date-picker>
                                    <el-button type="primary" @click="fetchMoneyData">搜索</el-button>
                                </div>
                                <el-table
                                    :data="moneyTableData"
                                    style="width: 100%"
                                    border>
                                    <el-table-column
                                        prop="type"
                                        label="消费类型"
                                        width="180">
                                    </el-table-column>
                                    <el-table-column
                                        prop="usemoney"
                                        label="消费金额">
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
require_once("lightyearfooter.php");
require_once("footer.php");
?>
<script src="../assets/js/echarts.min.js"></script>
<script type="text/javascript">
    // 设置默认日期为当天
    var defaultDate = new Date().toISOString().split('T')[0];
    
    var orderData = {
        days: ['6天前', '5天前', '4天前', '3天前', '2天前', '1天前', '今天'],
        orders: [<?php echo $today_6_dd; ?>, <?php echo $today_5_dd; ?>, <?php echo $today_4_dd; ?>, <?php echo $today_3_dd; ?>, <?php echo $today_2_dd; ?>, <?php echo $today_1_dd; ?>, <?php echo $today_dd; ?>]
    };
    var consumeData = {
        days: ['6天前', '5天前', '4天前', '3天前', '2天前', '1天前', '今天'],
        total: [<?php echo $today_6_zxh; ?>, <?php echo $today_5_zxh; ?>, <?php echo $today_4_zxh; ?>, <?php echo $today_3_zxh; ?>, <?php echo $today_2_zxh; ?>, <?php echo $today_1_zxh; ?>, <?php echo $today_zxh; ?>],
        order: [<?php echo $today_6_ddxh; ?>, <?php echo $today_5_ddxh; ?>, <?php echo $today_4_ddxh; ?>, <?php echo $today_3_ddxh; ?>, <?php echo $today_2_ddxh; ?>, <?php echo $today_1_ddxh; ?>, <?php echo $today_ddxh; ?>],
        other: [<?php echo $today_6_qtxh; ?>, <?php echo $today_5_qtxh; ?>, <?php echo $today_4_qtxh; ?>, <?php echo $today_3_qtxh; ?>, <?php echo $today_2_qtxh; ?>, <?php echo $today_1_qtxh; ?>, <?php echo $today_qtxh; ?>]
    };
    var vm = new Vue({
        el: "#dataCenter",
        data: {
            orderDate: defaultDate,
            moneyDate: defaultDate,
            orderTableData: [],
            moneyTableData: [],
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            orderChart: null,
            consumeChart: null
        },
        methods: {
            // 初始化订单数量图表
            initOrderChart: function () {
                console.log('初始化订单图表');
                const chartDom = this.$refs.orderChart;
                if (!chartDom) {
                    console.error('orderChart 未找到，检查 ref');
                    return;
                }
                this.orderChart = echarts.init(chartDom);
                var option = {
                    tooltip: { trigger: 'axis' },
                    legend: { data: ['订单数量'] },
                    xAxis: { type: 'category', data: orderData.days },
                    yAxis: { type: 'value' },
                    series: [{
                        name: '订单数量',
                        data: orderData.orders,
                        type: 'line',
                        smooth: true
                    }]
                };
                this.orderChart.setOption(option);
            },
            // 初始化消费统计图表
            initConsumeChart: function () {
                console.log('初始化消费图表');
                const chartDom = this.$refs.consumeChart;
                if (!chartDom) {
                    console.error('consumeChart 未找到，检查 ref');
                    return;
                }
                this.consumeChart = echarts.init(chartDom);
                var option = {
                    tooltip: { trigger: 'axis' },
                    legend: { data: ['总消费', '订单消费', '其他消费'] },
                    xAxis: { type: 'category', data: consumeData.days },
                    yAxis: { type: 'value' },
                    series: [
                        { name: '总消费', data: consumeData.total, type: 'line', smooth: true },
                        { name: '订单消费', data: consumeData.order, type: 'line', smooth: true },
                        { name: '其他消费', data: consumeData.other, type: 'line', smooth: true }
                    ]
                };
                this.consumeChart.setOption(option);
            },
            // 获取商品销售数据
            fetchOrderData: function () {
                if (!this.orderDate) {
                    this.$message.error('请选择日期');
                    return;
                }
                var load = this.$loading({ lock: true, text: '加载中...', spinner: 'el-icon-loading' });
                const params = new URLSearchParams();
                params.append('date', this.orderDate);
                axios.post('/apisub.php?act=orderlog', params)
                    .then(response => {
                        load.close();
                        if (response.data.code === 1) {
                            this.orderTableData = response.data.data;
                            this.$message.success('查询成功');
                        } else {
                            this.$message.error(response.data.msg);
                        }
                    })
                    .catch(error => {
                        load.close();
                        this.$message.error('请求失败');
                    });
            },
            // 获取积分消耗明细
            fetchMoneyData: function () {
                if (!this.moneyDate) {
                    this.$message.error('请选择日期');
                    return;
                }
                var load = this.$loading({ lock: true, text: '加载中...', spinner: 'el-icon-loading' });
                const params = new URLSearchParams();
                params.append('date', this.moneyDate);
                axios.post('/apisub.php?act=moneylog', params)
                    .then(response => {
                        load.close();
                        if (response.data.code === 1) {
                            this.moneyTableData = response.data.data;
                            this.$message.success('查询成功');
                        } else {
                            this.$message.error(response.data.msg);
                        }
                    })
                    .catch(error => {
                        load.close();
                        this.$message.error('请求失败');
                    });
            }
        },
        mounted() {
            this.$nextTick(() => {
                setTimeout(() => {
                    this.initOrderChart(); // 初始化订单图表
                    this.initConsumeChart(); // 初始化消费图表
                }, 300);
            });
        }
    });
</script>
</body>
</html>