<?php
require_once('head.php');
?>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
      <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
        销量排行
      </div>
      <div class="panel-body">
        <div class="row">
          <!-- 今日销量排行榜 -->
          <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 15px; height: 100%;">
              <div class="card-header bg-light">今日销量排行榜</div>
              <div class="card-body">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>商品</th>
                      <th>热度</th>
                      <th>最新下单时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php
                    $a = $DB->query("SELECT cid, COUNT(*) AS count, MAX(addtime) as latest_order_time FROM qingka_wangke_order WHERE TO_DAYS(addtime) = TO_DAYS(NOW()) GROUP BY cid ORDER BY count DESC LIMIT 30");
                    while ($rs = $DB->fetch($a)) {
                      $abc = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$rs['cid']}' ");
                      $formatted_time = date('Y-m-d H:i:s', strtotime($rs['latest_order_time']));
                      $redupaihang = ($rs['count'] / 100);
                      echo "<tr>
                              <td>" . htmlspecialchars($abc['name']) . "</td>
                              <td>指数：" . number_format($redupaihang, 2) . "</td>
                              <td>" . htmlspecialchars($formatted_time) . "</td>
                            </tr>";
                    }
                    ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 昨日销量排行榜 -->
          <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 15px; height: 100%;">
              <div class="card-header bg-light">昨日销量排行榜</div>
              <div class="card-body">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>商品</th>
                      <th>热度</th>
                      <th>最新下单时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php
                    $a = $DB->query("SELECT cid, COUNT(*) AS count, MAX(addtime) as latest_order_time FROM qingka_wangke_order WHERE TO_DAYS(addtime) = TO_DAYS(NOW()) - 1 GROUP BY cid ORDER BY count DESC LIMIT 30");
                    while ($rs = $DB->fetch($a)) {
                      $abc = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$rs['cid']}' ");
                      $formatted_time = date('Y-m-d H:i:s', strtotime($rs['latest_order_time']));
                      $redupaihang = ($rs['count'] / 100);
                      echo "<tr>
                              <td>" . htmlspecialchars($abc['name']) . "</td>
                              <td>指数：" . number_format($redupaihang, 2) . "</td>
                              <td>" . htmlspecialchars($formatted_time) . "</td>
                            </tr>";
                    }
                    ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 本周销量排行榜 -->
          <div class="col-lg-4 col-md-12 col-sm-12 mb-4">
            <div class="card" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 15px; height: 100%;">
              <div class="card-header bg-light">本周销量排行榜</div>
              <div class="card-body">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>商品</th>
                      <th>最新下单时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php
                    $a = $DB->query("SELECT cid, COUNT(*) AS count, MAX(addtime) AS last_order_time FROM qingka_wangke_order WHERE addtime >= DATE_FORMAT(NOW(), '%Y-%m-%d') - INTERVAL (WEEKDAY(NOW()) + 1) DAY GROUP BY cid ORDER BY count DESC LIMIT 30");
                    while ($rs = $DB->fetch($a)) {
                      $abc = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$rs['cid']}' ");
                      $formatted_time = date('Y-m-d H:i:s', strtotime($rs['last_order_time']));
                      echo "<tr>
                              <td>" . htmlspecialchars($abc['name']) . "</td>
                              <td>" . htmlspecialchars($formatted_time) . "</td>
                            </tr>";
                    }
                    ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<?php
require_once("lightyearfooter.php");
require_once("footer.php");
?>