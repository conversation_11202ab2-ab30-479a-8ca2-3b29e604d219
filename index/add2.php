<?php
$title = '小猿提交';
require_once('head.php');
$addsalt = md5(mt_rand(600, 3600) . time());
$_SESSION['addsalt'] = $addsalt;
?>
<style>
/* 增大多选标签 */
.el-select__tags .el-tag {
  height: 32px;
  line-height: 30px;
  font-size: 16px;
  margin: 4px 6px 4px 0;
}
/* 增大关闭按钮 */
.el-tag__close.el-icon-close {
  font-size: 20px !important; /* 调整关闭图标大小 */
  width: 20px !important; /* 调整宽度 */
  height: 20px !important; /* 调整高度 */
}
</style>
<div class="app-content-body">
    <div class="wrapper-md control" id="add">
        <div class="layui-row layui-col-space5">
            <div class="layui-col-md">
                <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 10px;">
                    <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">小猿提交</div>
                    <div class="panel-body" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">
                        <form class="form-horizontal devform">
<?php if ($conf['fllx']=="1") {?>
<div class="form-group">
    <label class="col-sm-2 control-label">项目分类</label>
    <div class="col-sm-9">
        <el-select v-model="id"
                   @change="fenlei(id)"
                   filterable
                   clearable
                   placeholder="请选择项目分类"
                   style="width: 100%;">
            <el-option label="全部分类" value=""></el-option>
            <?php
            $a = $DB->query("select * from qingka_wangke_fenlei where status=1 ORDER BY `sort` ASC");
            while($rs = $DB->fetch($a)){
            ?>
            <el-option label="<?=$rs['name']?>" value="<?=$rs['id']?>"></el-option>
            <?php } ?>
        </el-select>
    </div>
</div>
<?php }else if ($conf['fllx']=="2") {?>
    <div class="form-group">
        <label class="col-sm-2 control-label">项目分类</label>
        <div class="col-sm-9">
            <div class="col-xs-12">
                <div class="example-box">
                    <label class="lyear-radio radio-inline radio-primary">
                        <input type="radio" name="e" checked="" @change="fenlei('');"><span>全部</span>
                    </label>
                    <?php
                    $a=$DB->query("select * from qingka_wangke_fenlei where status=1 ORDER BY `sort` DESC");
                    while($rs=$DB->fetch($a)){
                    ?>
                    <label class="lyear-radio radio-inline radio-primary">
                        <input type="radio" name="e" @change="fenlei(<?=$rs['id']?>);"><span style="color: #1e9fff;"><?=$rs['name']?></span>
                    </label>
                    <?php } ?>
                </div>
            </div>
         </div>
</div>
    <?php }?>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目搜索</label>
                                <div class="col-sm-9">
                                    <el-input v-model="keyword" @input="search" placeholder="输入关键词搜索-苹果双击粘贴" style="width: 100%;"></el-input>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目选择</label>
                                <div class="col-sm-9">
                                    <el-select v-model="cid" @change="tips(cid)" filterable placeholder="选择所需的商品-支持二次搜索" style="width: 100%;">
                                        <el-option v-for="class2 in filteredClass1" :label="class2.name + '→' + class2.price + '积分'" :value="class2.cid">
                                            <div class="el-option-content">
                                                <span class="project-name">{{ class2.name }}</span>
                                                <span class="project-price">{{ class2.price }}积分</span>
                                            </div>
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div v-show="show">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">填写模板</label>
                                    <div class="col-sm-9">
                                        <el-switch
                                            v-model="isSingleMode"
                                            active-text="单账号"
                                            inactive-text="多账号">
                                        </el-switch>
                                        <button type="button" @click="saveclass" style="border-radius: 8px;" class="layui-btn layui-btn-sm layui-btn-danger">收藏项目</button>
                                        <button type="button" @click="dellclass" style="border-radius: 8px;" class="layui-btn layui-btn-sm layui-btn-danger">移除收藏</button>
                                    </div>
                                </div>
                                <div v-if="isSingleMode">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">学校：</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="layui-input" v-model="school" placeholder="学校（选填）" style="border-radius: 8px;">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">账号：</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="layui-input" v-model="newaccount" placeholder="账号（必填）" style="border-radius: 8px;" autocomplete="off">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">密码：</label>
                                        <div class="col-sm-9">
                                            <input type="text" class="layui-input" v-model="newpassword" placeholder="密码（必填）" style="border-radius: 8px;" autocomplete="off">
                                            <span class="help-block m-b-none" style="color:red;" id="warning">
                                                <span v-html="content"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else>
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label"></label>
                                        <div class="col-sm-9">
                                            <textarea rows="10" class="layui-textarea" v-model="userinfo" placeholder="下单格式：账号 密码（中间用空格分隔）
或： 学校 账号 密码 （中间用空格分隔）
多账号下单必须换行，务必一行一条信息" style="border-radius: 8px;"></textarea>
                                            <span class="help-block m-b-none" style="color:red;" id="warning">
                                                <span v-html="content"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">过滤词设置</label>
                                    <div class="col-sm-9">
                                        <el-switch
                                            v-model="filterMode"
                                            active-text="模式1: 去除含过滤词"
                                            inactive-text="模式2: 仅保留含过滤词">
                                        </el-switch>
                                        <div style="margin-top: 10px;">
                                            <el-tag
                                                v-for="(tag, index) in filterKeywords"
                                                :key="index"
                                                closable
                                                @close="removeTag(index)"
                                                style="margin-right: 5px; margin-bottom: 5px;">
                                                {{ tag }}
                                            </el-tag>
                                            <el-input
                                                v-model="newTag"
                                                placeholder="输入过滤词，按回车添加"
                                                @keyup.enter.native="addTag"
                                                style="width: 200px;"
                                                ref="tagInput">
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">自动全选</label>
                                    <div class="col-sm-9">
                                        <el-switch
                                            v-model="autoSelectAll"
                                            active-text="开启自动全选"
                                            inactive-text="关闭自动全选">
                                        </el-switch>
                                    </div>
                                </div>
                                <div class="col-sm-offset-2 col-sm-12">
                                    <button type="button" @click="get" style="border-radius: 13px;" class="layui-btn layui-btn-sm">开始查课</button>
                                    <button type="button" @click="stopQuery" v-show="isQuerying" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-danger">停止查课</button>
                                    <button type="button" @click="add" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-normal">订单提交</button>
                                    <button type="button" @click="showExcelModal" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-warm">读取EXCEL</button>
                                    <button type="button" @click="checkUserinfo" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-danger">检查数据</button>
                                    <button type="reset" style="border-radius: 13px;" class="layui-btn layui-btn-sm layui-btn-primary">清空提交面板</button>
                                </div>
                                <div class="col-sm-offset-2 col-sm-12" v-show="isQuerying">
                                    <span>开始查询:当前第 {{ row.length }} 个/共计 {{ stats.total }} 个</span>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="layui-col-md12" v-show="show1">
                <div class="panel panel-default" style="border-radius: 10px;">
                    <div class="panel-heading font-bold bg-white" style="border-radius: 10px;">
                        查询结果（多账号查询时请耐心等待显示）
                    </div>
                    <div class="panel-body">
                        <form class="form-horizontal devform">
                            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                <div v-for="(rs, key) in row">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" role="tab" :id="'heading' + key">
                                            <h4 class="panel-title">
                                                <a role="button" data-toggle="collapse" data-parent="#accordion" :href="'#collapse' + key" aria-expanded="true">
                                                    <b><span v-html="rs.userName"></span></b><span v-html="rs.userinfo"></span>
                                                    <span v-if="rs.msg == '查询成功'"><b style="color: green;">{{ rs.msg }}</b></span>
                                                    <span v-else-if="rs.msg != '查询成功'"><b style="color: red;">{{ rs.msg }}</b></span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div :id="'collapse' + key" class="panel-collapse collapse in" role="tabpanel" :aria-labelledby="'heading' + key">
                                            <div class="panel-body">
                                                <el-select v-model="rs.selectedCourses" style="width: 100%;" multiple placeholder="请选择课程---输入课程名可搜索" @change="handleCourseSelection(rs.userinfo, rs.userName, rs.data, rs.selectedCourses)" filterable>
                                                    <el-option v-for="res in rs.data" :key="res.id || res.name" :label="res.name + (res.id ? ' [课程ID:' + res.id + ']' : '')" :value="res.id ? res.id : res.name">
                                                        <span style="float: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 260px;">{{ res.name }}</span>
                                                        <span style="float: right; color: #8492a6; font-size: 12px; overflow: hidden; text-overflow: ellipsis; max-width: 50px;">[课程ID{{ res.id }}]</span>
                                                    </el-option>
                                                </el-select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php require_once("lightyearfooter.php");?>
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
var vm = new Vue({
    el: "#add",
    data: {
        row: [],
        check_row: [],
        userinfo: '',
        cid: '',
        id: '',
        class1: [],
        class3: '',
        show: false,
        show1: false,
        content: '',
        keyword: '',
        filteredClass1: [],
        isSingleMode: false,
        showCategories: true,
        school: '',
        newaccount: '',
        newpassword: '',
        filterMode: true,
        filterKeywords: [],
        newTag: '',
        autoSelectAll: true, // New switch for auto-selecting all courses
        isQuerying: false,
        stats: { total: 0, processed: 0 }
    },
    methods: {
        getclass: async function() {
            try {
                var load = layer.load(2);
                let response = await this.$http.post("/apisub.php?act=getclassfl");
                layer.close(load);
                if (response.data.code === 1) {
                    this.class1 = response.body.data;
                    this.filteredClass1 = this.class1;
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error(error);
            }
        },
        fenlei: async function(id) {
            this.selectedButton = id;
            try {
                var load = layer.load(2);
                let response = await this.$http.post("/apisub.php?act=getclassfl", { id: id }, { emulateJSON: true });
                layer.close(load);
                if (response.data.code === 1) {
                    this.class1 = response.body.data;
                    this.filteredClass1 = this.class1;
                    this.keyword = '';
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error(error);
            }
        },
        search: function() {
            const keyword = this.keyword.trim().toLowerCase();
            if (keyword === '') {
                this.filteredClass1 = this.class1;
            } else {
                this.filteredClass1 = this.class1.filter(item => {
                    return item.name.toLowerCase().includes(keyword);
                });
            }
        },
        stopQuery: function() {
            this.isQuerying = false;
            layer.closeAll('loading');
            layer.msg("查询已停止", { icon: 3 });
        },
        addTag() {
            const tag = this.newTag.trim();
            if (tag && !this.filterKeywords.includes(tag)) {
                this.filterKeywords.push(tag);
                this.newTag = '';
            } else if (!tag) {
                layer.msg("请输入有效的过滤词", { icon: 2 });
            } else {
                layer.msg("该过滤词已存在", { icon: 2 });
            }
            this.$refs.tagInput.focus();
        },
        removeTag(index) {
            this.filterKeywords.splice(index, 1);
        },
        get: async function() {
            if (this.isSingleMode) {
                if (this.newaccount == '' || this.newpassword == '') {
                    layer.msg("账号和密码不能为空");
                    return false;
                }
                this.userinfo = this.school ? this.school + ' ' + this.newaccount + ' ' + this.newpassword : this.newaccount + ' ' + this.newpassword;
            }
            if (this.cid == '' || this.userinfo == '') {
                layer.msg("信息格式错误，请检查");
                return false;
            }
            let userinfos = this.userinfo.split('\n').filter(v => v.trim());
            this.row = [];
            this.check_row = [];
            this.stats.total = userinfos.length;
            this.stats.processed = 0;
            this.isQuerying = true;
            for (let info of userinfos) {
                if (!this.isQuerying) break;
                try {
                    let hash = getENC('<?php echo $addsalt;?>');
                    let response = await this.$http.post("/apisub.php?act=get", {
                        cid: this.cid,
                        userinfo: info.trim(),
                        hash
                    }, { emulateJSON: true });
                    let data = response.body;
                    if (data && Array.isArray(data.data)) {
                        let filteredData = data.data;
                        if (this.filterKeywords.length > 0) {
                            filteredData = data.data.filter(item => {
                                const nameLower = item.name.toLowerCase();
                                if (this.filterMode) {
                                    return !this.filterKeywords.some(keyword => nameLower.includes(keyword.toLowerCase()));
                                } else {
                                    return this.filterKeywords.some(keyword => nameLower.includes(keyword.toLowerCase()));
                                }
                            });
                        }
                        if (filteredData.length > 0) {
                            // Conditionally set selectedCourses based on autoSelectAll
                            data.selectedCourses = this.autoSelectAll ? filteredData.map(item => item.id || item.name) : [];
                            data.originalUserinfo = info.trim();
                            this.row.push({
                                ...data,
                                data: filteredData
                            });
                            this.show1 = true;
                        }
                    } else {
                        this.row.push({
                            data: [{
                                name: "账号密码错误",
                                id: "1"
                            }],
                            selectedCourses: this.autoSelectAll ? ["1"] : [],
                            originalUserinfo: info.trim()
                        });
                        this.show1 = true;
                        console.error("Invalid data structure:", data);
                    }
                } catch (error) {
                    console.error(error);
                    this.row.push({
                        data: [{
                            name: "账号密码错误",
                            id: "1"
                        }],
                        selectedCourses: this.autoSelectAll ? ["1"] : [],
                        originalUserinfo: info.trim()
                    });
                    this.show1 = true;
                }
                this.stats.processed++;
                this.$forceUpdate();
            }
            this.isQuerying = false;
            layer.closeAll('loading');
        },
        add: async function() {
            if (this.cid == '') {
                layer.msg("请先查课");
                return;
            }
            this.check_row = this.row.flatMap(rs =>
                rs.selectedCourses.map(courseId => ({
                    userinfo: rs.userinfo,
                    userName: rs.userName,
                    data: rs.data.find(res => (res.id || res.name) === courseId)
                }))
            );
            if (this.check_row.length < 1) {
                layer.msg("请先选择课程");
                return;
            }
            let load = layer.load(2);
            let successCount = 0;
            let errorMessages = [];
            const batchSize = 50;
            const totalBatches = Math.ceil(this.check_row.length / batchSize);
            try {
                for (let i = 0; i < this.check_row.length; i += batchSize) {
                    const batch = this.check_row.slice(i, i + batchSize);
                    const batchNumber = Math.floor(i/batchSize)+1;
                    try {
                        let response = await this.$http.post("/apisub.php?act=add", {
                            cid: this.cid,
                            data: batch
                        }, { emulateJSON: true });
                        if (response.data.code === 1) {
                            successCount++;
                        } else {
                            errorMessages.push(`第 ${batchNumber} 批: ${response.data.msg}`);
                        }
                    } catch (error) {
                        errorMessages.push(`第 ${batchNumber} 批: 提交失败 - ${error.message}`);
                    }
                }
                layer.close(load);
                if (errorMessages.length === 0) {
                    layer.alert(`所有课程已成功提交 (共 ${totalBatches} 批)`, {
                        icon: 1,
                        title: "温馨提示"
                    }, function() {
                        setTimeout(function() {
                            window.location.href = ""
                        });
                    });
                    this.row = [];
                    this.check_row = [];
                } else {
                    let successMsg = successCount > 0 ? `成功提交 ${successCount}/${totalBatches} 批` : "全部提交失败";
                    let errorMsg = `${successMsg}<br>失败详情:<br>` + errorMessages.join("<br>");
                    layer.alert(errorMsg, {
                        icon: successCount > 0 ? 1 : 2,
                        title: "温馨提示"
                    });
                }
            } catch (error) {
                layer.close(load);
                layer.alert("提交过程中发生错误：" + error.message, {
                    icon: 2,
                    title: "错误"
                });
            }
        },
        saveclass: function() {
            this.$http.post("/apisub.php?act=add_favorite", { cid: this.cid }, { emulateJSON: true }).then(function(response) {
                if (response.data.code == 1) {
                    layer.msg("收藏成功", { icon: 1 });
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            });
        },
        dellclass: function() {
            this.$http.post("/apisub.php?act=remove_favorite", { cid: this.cid }, { emulateJSON: true }).then(function(response) {
                if (response.data.code == 1) {
                    layer.msg("移除成功", { icon: 1 });
                } else {
                    layer.msg(response.data.msg, { icon: 2 });
                }
            });
        },
        handleCourseSelection: function(userinfo, userName, rs, selectedCourses) {
            this.check_row = this.check_row.filter(item => item.userinfo !== userinfo);
            selectedCourses.forEach(courseValue => {
                let courseObj = rs.find(res => (res.id ? res.id === courseValue : res.name === courseValue));
                if (courseObj) {
                    this.check_row.push({ userinfo, userName, data: courseObj });
                }
            });
        },
        showExcelModal: function () {
            layer.open({
                type: 1,
                title: '上传Excel',
                content:
                    `<input type="file" id="excelFile" accept=".xlsx,.xls" />` +
                    `<button type="button" id="readExcelBtn" onclick="vm.readExcel()">读取并解析文件</button>`+
                    `<br>文件上传后点读取并解析文件<br>所有账号信息会显示在上方的框中<br>读取时会默认丢弃第一行的数据（表头）<br>默认表格格式为 学校 账号 密码 课程`,
                area: ['300px', '200px']
            });
        },
        readExcel: function () {
            var file = document.getElementById('excelFile').files[0];
            if (!file) {
                layer.msg("请选择Excel文件");
                return;
            }
            var reader = new FileReader();
            reader.onload = function (e) {
                var data = e.target.result;
                var workbook = XLSX.read(data, { type: 'binary' });
                var sheetName = workbook.SheetNames[0];
                var sheet = workbook.Sheets[sheetName];
                var json = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: '' });
                var userData = json.map(row => row.join(' '));
                vm.userinfo = userData.join('\n');
                layer.msg("数据读取成功");
                layer.closeAll();
            };
            reader.readAsBinaryString(file);
        },
        checkUserinfo: function () {
            var lines = this.userinfo.split('\n');
            var errors = [];
            var correctedLines = [];
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                var correctedLine = line.replace(/ +/g, ' ');
                correctedLines.push(correctedLine);
                var parts = correctedLine.split(' ');
                if (parts.length < 2 || parts.length > 3) {
                    errors.push('第' + (i+1) + '行数据数量不正确，此行有' + parts.length + '个数据。');
                }
            }
            if (errors.length > 0) {
                layer.alert(errors.join('\n'), {title: '数据检查结果', icon: 5});
            } else {
                layer.msg('数据检查通过！', {icon: 1});
            }
            this.userinfo = correctedLines.join('\n');
        },
        tips: function(message) {
            for (let item of this.class1) {
                if (item.cid == message) {
                    this.show = true;
                    this.content = item.content;
                    break;
                }
            }
        }
    }
});
</script>