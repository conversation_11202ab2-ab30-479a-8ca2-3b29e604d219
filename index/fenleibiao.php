<?php
$mod = 'blank';
$title = '分类列表';
require_once('head.php');
?>
<div class="app-content-body">
    <div class="wrapper-md control">
        <div class="row">
            <div class="col-sm-12">
                <div class="panel panel-default" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
                <div class="panel-heading font-bold layui-bg-gray" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6; border-radius: 7px;">【商品分类列表】 分类ID用于一键分类对接！（Ctrl+F搜索）<br> 
【分类ID就是分类的ID】</div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-xs-4 col-sm-2">
                          <button id="exportBtn" class="btn btn-info btn-round" style="margin-left: 14px; width: 100%;" >导出分类列表</button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <div class="col-sm-4">
                                <table id="table1" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th scope="col">分类ID</th>
                                            <th scope="col">分类名称</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $a = $DB->query("
                                            SELECT 
                                                qwf.id, 
                                                qwf.name, 
                                                (SELECT COUNT(*) FROM qingka_wangke_class WHERE fenlei = qwf.id) AS product_count
                                            FROM 
                                                qingka_wangke_fenlei qwf
                                            WHERE 
                                                qwf.status = 1 
                                            ORDER BY 
                                                qwf.sort DESC 
                                            LIMIT 0, 14
                                        ");

                                        while ($rs = $DB->fetch($a)) {
                                            echo "<tr>
                                                    <td>{$rs['id']}</td>
                                                    <td>{$rs['name']} 共 {$rs['product_count']}个商品</td>
                                                  </tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-sm-4">
                                <table id="table2" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th scope="col">分类ID</th>
                                            <th scope="col">分类名称</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $a = $DB->query("
                                            SELECT 
                                                qwf.id, 
                                                qwf.name, 
                                                (SELECT COUNT(*) FROM qingka_wangke_class WHERE fenlei = qwf.id) AS product_count
                                            FROM 
                                                qingka_wangke_fenlei qwf
                                            WHERE 
                                                qwf.status = 1 
                                            ORDER BY 
                                                qwf.sort DESC 
                                            LIMIT 14, 14
                                        ");

                                        while ($rs = $DB->fetch($a)) {
                                            echo "<tr>
                                                    <td>{$rs['id']}</td>
                                                    <td>{$rs['name']} 共 {$rs['product_count']}个商品</td>
                                                  </tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-sm-4">
                                <table id="table3" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th scope="col">分类ID</th>
                                            <th scope="col">分类名称</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $a = $DB->query("
                                            SELECT 
                                                qwf.id, 
                                                qwf.name, 
                                                (SELECT COUNT(*) FROM qingka_wangke_class WHERE fenlei = qwf.id) AS product_count
                                            FROM 
                                                qingka_wangke_fenlei qwf
                                            WHERE 
                                                qwf.status = 1 
                                            ORDER BY 
                                                qwf.sort DESC 
                                            LIMIT 28, 100
                                        ");

                                        while ($rs = $DB->fetch($a)) {
                                            echo "<tr>
                                                    <td>{$rs['id']}</td>
                                                    <td>{$rs['name']} 共 {$rs['product_count']}个商品</td>
                                                  </tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php require_once("lightyearfooter.php");?> 
<?php require_once("footer.php");?>
<script src="../assets/js/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    $('#exportBtn').click(function() {
        var tableIds = ['table1', 'table2', 'table3'];
        var wb = XLSX.utils.book_new();
        var ws = XLSX.utils.aoa_to_sheet([]);
        tableIds.forEach(function(tableId, index) {
            var tableElement = document.getElementById(tableId);
            var tableData = XLSX.utils.table_to_sheet(tableElement);
            XLSX.utils.sheet_add_dom(ws, tableElement, { origin: -1 });
        });
        XLSX.utils.book_append_sheet(wb, ws, '分类列表');
        XLSX.writeFile(wb, '渠道分类表.xlsx');
    });
});
</script>