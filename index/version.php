<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统版本信息</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin-top: 30px;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            border-radius: 12px 12px 0 0;
            padding: 15px;
        }
        .card-body {
            padding: 20px;
        }
        .feature-content {
            line-height: 1.6;
        }
        .system-info p {
            margin: 5px 0;
        }
        .system-info b {
            display: inline-block;
            width: 150px;
            font-weight: 600;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .table td {
            vertical-align: middle;
        }
        .btn-custom {
            background-color: #28a745;
            color: white;
            border-radius: 8px;
        }
        .btn-custom:hover {
            background-color: #218838;
        }
        .loading-row {
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row g-4">
            <!-- 版本特性 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">版本特性</div>
                    <div class="card-body feature-content" id="version-features">
                        <p>正在加载特性...</p>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">系统信息</div>
                    <div class="card-body system-info">
                        <b>系统名称</b><p>猿猿国王29.5系统</p>
                        <b>系统作者</b><p>猿猴</p>
                        <b>发行时间</b><p>2025.08.01</p>
                        <b>当前版本</b><p>1.0</p>
                    </div>
                </div>
            </div>

            <!-- 推荐站点 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">推荐站点</div>
                    <div class="card-body">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th scope="col">名称</th>
                                    <th scope="col">描述</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody id="recommended-sites">
                                <tr id="loading-row">
                                    <td colspan="3" class="loading-row">正在加载推荐内容...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 社区动态 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">社区动态</div>
                    <div class="card-body">
                        <div class="alert alert-success" role="alert">
                            系统已更新至最新版本，包含最新功能与优化！
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟加载版本特性
        const versionFeatures = `
            <h5>版本 1.0 特性</h5>
            <ul>
                <li>全新前端主框架，极速轻薄</li>
                <li>全页面无加密，志趣不受限</li>
                <li>全新数据库连接方式，更安全</li>
                <li>uid1权限独立后端，更易维护</li>
                <li>三种经典主题，精选定制色</li>
                <li>导航栏支持多开，智享便捷</li>
                <li>内置ai助手，走向新时代（部分版本）</li>
            </ul>
        `;
        document.getElementById('version-features').innerHTML = versionFeatures;

        // 模拟加载推荐站点
        const recommendedSites = [
            { title: "官方TG频道", content: "最新资讯与更新", domain: "https://t.me/wjwk666" }
        ];

        const tbody = document.getElementById('recommended-sites');
        const loadingRow = document.getElementById('loading-row');
        tbody.removeChild(loadingRow);

        recommendedSites.forEach(site => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${site.title}</td>
                <td>${site.content}</td>
                <td><a class="btn btn-custom btn-sm" href="${site.domain}" target="_blank">查看详情</a></td>
            `;
            tbody.appendChild(tr);
        });
    </script>
</body>
</html>