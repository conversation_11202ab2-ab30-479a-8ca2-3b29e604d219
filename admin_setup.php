<?php
/**
 * 管理员账号设置工具
 * 仅在首次部署时使用，设置完成后请删除此文件
 */

// 安全检查：临时允许访问（设置完成后请删除此文件）
// 如果需要限制IP访问，请取消下面的注释并添加您的IP
/*
$allowed_ips = ['127.0.0.1', '::1', 'localhost', 'YOUR_IP_HERE'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips)) {
    die('访问被拒绝：IP地址不在允许列表中');
}
*/

include('confing/common.php');

// 检查是否已有管理员
$existing_admin = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE uid = 1", []);

if ($_POST['action'] == 'setup_admin') {
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);
    $display_name = trim($_POST['display_name']);
    $verification_code = trim($_POST['verification_code']);
    
    // 验证输入
    $errors = [];
    
    if (empty($username)) {
        $errors[] = '用户名不能为空';
    } elseif (!preg_match('/^[a-zA-Z0-9_]{5,20}$/', $username)) {
        $errors[] = '用户名只能包含字母、数字和下划线，长度5-20位';
    }
    
    if (empty($password)) {
        $errors[] = '密码不能为空';
    } elseif (strlen($password) < 8) {
        $errors[] = '密码长度至少8位';
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $password)) {
        $errors[] = '密码必须包含大小写字母、数字和特殊字符';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = '两次输入的密码不一致';
    }
    
    if (empty($display_name)) {
        $errors[] = '显示名称不能为空';
    }
    
    if (empty($verification_code)) {
        $errors[] = '二次验证码不能为空';
    } elseif (strlen($verification_code) < 6) {
        $errors[] = '二次验证码长度至少6位';
    }
    
    if (empty($errors)) {
        try {
            // 使用更安全的密码哈希
            $hashed_password = password_hash($password, PASSWORD_ARGON2ID);
            
            if ($existing_admin) {
                // 更新现有管理员
                $sql = "UPDATE qingka_wangke_user SET user = ?, pass = ?, name = ? WHERE uid = 1";
                $params = [$username, $hashed_password, $display_name];
            } else {
                // 创建新管理员
                $sql = "INSERT INTO qingka_wangke_user (uid, user, pass, name, money, addprice, addtime, uuid) VALUES (1, ?, ?, ?, 0.00, 1.00, NOW(), 1)";
                $params = [$username, $hashed_password, $display_name];
            }
            
            $DB->prepare_query($sql, $params);
            
            // 更新验证码配置
            $config_sql = "UPDATE qingka_wangke_config SET k = ? WHERE v = 'verification' OR v = 'verification_code'";
            $DB->prepare_query($config_sql, [$verification_code]);
            
            // 如果配置不存在则插入
            $check_config = $DB->prepare_getrow("SELECT * FROM qingka_wangke_config WHERE v = 'verification_code'", []);
            if (!$check_config) {
                $insert_config = "INSERT INTO qingka_wangke_config (v, k) VALUES ('verification_code', ?)";
                $DB->prepare_query($insert_config, [$verification_code]);
            }
            
            $success = true;
            $message = '管理员账号设置成功！请删除此文件并使用新账号登录。';
            
        } catch (Exception $e) {
            $errors[] = '设置失败：' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>管理员账号设置</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { color: red; margin-bottom: 10px; }
        .success { color: green; margin-bottom: 10px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>🔐 管理员账号设置</h1>
    
    <div class="warning">
        <strong>⚠️ 安全提醒：</strong>
        <ul>
            <li>此页面仅在首次部署时使用</li>
            <li>设置完成后请立即删除此文件</li>
            <li>请使用强密码和安全的验证码</li>
        </ul>
    </div>
    
    <?php if (isset($success) && $success): ?>
        <div class="success">✅ <?php echo $message; ?></div>
        <div class="info">
            <strong>登录信息：</strong><br>
            用户名：<?php echo htmlspecialchars($username); ?><br>
            密码：您设置的密码<br>
            二次验证码：您设置的验证码
        </div>
    <?php else: ?>
        
        <?php if (!empty($errors)): ?>
            <?php foreach ($errors as $error): ?>
                <div class="error">❌ <?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if ($existing_admin): ?>
            <div class="info">
                <strong>📝 当前管理员信息：</strong><br>
                UID: <?php echo $existing_admin['uid']; ?><br>
                用户名: <?php echo htmlspecialchars($existing_admin['user']); ?><br>
                显示名: <?php echo htmlspecialchars($existing_admin['name']); ?><br>
                <em>将更新现有管理员信息</em>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <input type="hidden" name="action" value="setup_admin">
            
            <div class="form-group">
                <label>用户名（登录账号）：</label>
                <input type="text" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                <small>只能包含字母、数字和下划线，长度5-20位</small>
            </div>
            
            <div class="form-group">
                <label>密码：</label>
                <input type="password" name="password" required>
                <small>至少8位，必须包含大小写字母、数字和特殊字符</small>
            </div>
            
            <div class="form-group">
                <label>确认密码：</label>
                <input type="password" name="confirm_password" required>
            </div>
            
            <div class="form-group">
                <label>显示名称：</label>
                <input type="text" name="display_name" value="<?php echo htmlspecialchars($_POST['display_name'] ?? '超级管理员'); ?>" required>
            </div>
            
            <div class="form-group">
                <label>二次验证码：</label>
                <input type="text" name="verification_code" value="<?php echo htmlspecialchars($_POST['verification_code'] ?? ''); ?>" required>
                <small>用于超级管理员登录的二次验证，至少6位</small>
            </div>
            
            <button type="submit">设置管理员账号</button>
        </form>
    <?php endif; ?>
    
    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666;">
        <small>
            <strong>安全建议：</strong><br>
            1. 设置完成后立即删除此文件<br>
            2. 定期更换管理员密码<br>
            3. 启用HTTPS访问<br>
            4. 考虑设置IP白名单
        </small>
    </div>
</body>
</html>
