8090教育（http://***********:8090/login）
登陆账号：china
登陆密码：168999
登陆：请求 URL
http://***********:8090/api/auth/login
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
263
content-type
application/json; charset=UTF-8
date
Fri, 20 Jun 2025 08:34:27 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
content-length
40
content-type
application/json
host
***********:8090
origin
http://***********:8090
proxy-connection
keep-alive
referer
http://***********:8090/login
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
负载：{username: "china", password: "168999"}
password
: 
"168999"
username
: 
"china"
返回：{state: true, message: "登录成功", code: 200, data: {,…}}
code
: 
200
data
: 
{,…}
message
: 
"登录成功"
state
: 
true

余额显示api：
请求 URL
http://***********:8090/api/user/balance
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
133
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 16:55:28 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
返回：
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
{exp: 1750611328, iat: 1750524929, u_discount: 1, user_id: 78, balance: 189.89}
balance
: 
189.89
exp
: 
1750611328
iat
: 
1750524929
u_discount
: 
1
user_id
: 
78
message
: 
"success"
state
: 
true
其中balance就是余额

公告api：
请求 URL
http://***********:8090/api/announcements?page=1&pageSize=10
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
442
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 16:55:28 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
负载：
page
1
pageSize
10
返回：
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
[{id: 53, title: null, content: "新增 安化技校考试学习系统", creatorId: "管理员", status: "发布中",…},…]
0
: 
{id: 53, title: null, content: "新增 安化技校考试学习系统", creatorId: "管理员", status: "发布中",…}
1
: 
{id: 52, title: null, content: "新增 河南党员教育云课堂_hndj.ksyun.com", creatorId: "管理员", status: "发布中",…}
message
: 
"success"
state
: 
true

下单api：
请求 URL
http://***********:8090/api/order/websites?keyword=&page=1&pageSize=1000
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
90179
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 16:58:26 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
负载：
keyword
page
1
pageSize
1000
返回：
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
[{site_id: 100490, url: "http://220.170.158.251:8090/FrmLogin.aspx", site_name: "安化技校考试学习系统",…},…]
[0 … 99]
0
: 
{site_id: 100490, url: "http://220.170.158.251:8090/FrmLogin.aspx", site_name: "安化技校考试学习系统",…}
format
: 
"账号 密码"
price
: 
3
site_id
: 
100490
site_name
: 
"安化技校考试学习系统"
speed
: 
null
status
: 
"正常"
url
: 
"http://220.170.158.251:8090/FrmLogin.aspx"。。。。会出现所有可以学习的网站和详细说明，可以搜索自己想学习名字，然后选择后会出现详细说明
请求 URL
http://***********:8090/api/order/website/info?websiteId=100482
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
504
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 16:60:29 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
负载：
websiteId
100482
就是学习编号
返回:
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
{site_name: "青橙优学_呼和浩特市新城区专业技术人员继续教育平台_xcq.nmgrcw.com", exp: 1750611328, iat: 1750524929, discount: 1,…}
check_course
: 
"支持"
description
: 
"正常学习 包考试"
discount
: 
1
exam_support
: 
"支持"
exp
: 
1750611328
format
: 
"账号 密码"
iat
: 
1750524929
price
: 
2
site_id
: 
100482
site_name
: 
"青橙优学_呼和浩特市新城区专业技术人员继续教育平台_xcq.nmgrcw.com"
speed
: 
null
status
: 
"正常"
u_discount
: 
1
url
: 
"https://xcq.nmgrcw.com/"
user_id
: 
78
uuid
: 
"67C96F70-2CAD-7E63-62CA-9CD02D022CD3"
websiteId
: 
"100482"
message
: 
"success"
state
: 
true

还有出现一个输入框，输入想学习的账号密码或者按照要求填写，然后有一个查课按钮
发送查课成功以后，会返回一个查到的课程信息的api：
请求 URL
http://***********:15888/query?username=620503199208283920&password=283920*&courseName=59iedu.com_%E7%94%98%E8%82%83%E7%9C%81%E4%B8%93%E6%8A%80%EF%BC%88%E5%A4%A9%E6%B0%B4%E5%B8%82%EF%BC%89&Time=1750525384663
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
247
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 17:03:04 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
负载：
username
620503199208283920
password
283920*
courseName
59iedu.com_甘肃省专技（天水市）
Time
1750525384663
返回：
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
{name: "620503199208283920----283920*", checked: false,…}
checked
: 
false
children
: 
[{name: "2025"}, {name: "2024"}, {name: "2023"}, {name: "2022"}, {name: "2021"}, {name: "2020"}]
0
: 
{name: "2025"}
1
: 
{name: "2024"}
2
: 
{name: "2023"}
3
: 
{name: "2022"}
4
: 
{name: "2021"}
5
: 
{name: "2020"}
name
: 
"620503199208283920----283920*"
nocheck
: 
false
open
: 
false
message
: 
"success"
state
: 
true





搜索数据：
请求 URL
http://***********:8090/api/order/list?username=622429198612171328&page=1&pageSize=10&sortField=createTime&sortOrder=descend
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
450
content-type
application/json; charset=UTF-8

date
Fri, 20 Jun 2025 08:35:49 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTA0OTQ4NjcsImlhdCI6MTc1MDQwODQ2OCwidV9kaXNjb3VudCI6MSwidXNlcl9pZCI6Nzh9.6fRFBSP6p3CsdcBlsxuB3Ff2cFAce-tigBaYA5KKCWA
host
***********:8090
proxy-connection
keep-alive
referer
http://***********:8090/order-management
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
其中622429198612171328是我搜索的账号
负载：
username
622429198612171328
page
1
pageSize
10
sortField
createTime
sortOrder
descend
返回学习数据：
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
{list: [{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 61066, price: 2, siteId: 100233,…}],…}
list
: 
[{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 61066, price: 2, siteId: 100233,…}]
0
: 
{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 61066, price: 2, siteId: 100233,…}
courseInfo
: 
"1----2025"
courseName
: 
"_订单项目学习完成_"
createTime
: 
"2025-06-18 11:07:59"
name
: 
"张月梅"
orderId
: 
61066
password
: 
"Zym861217"
price
: 
2
siteId
: 
100233
siteName
: 
"59iedu.com_甘肃省专技（定西市）"
status
: 
"已完成"
updateTime
: 
"2025-06-20 16:23:08"
username
: 
"622429198612171328"
uuid
: 
"79D8000F-0A8E-65C1-0C27-84F02142AA3F"
total
: 
1
message
: 
"success"
state
: 
true
然后可以选择查课结果需要学习的课程，然后提交
请求 URL
http://***********:8090/api/order/submit
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
68
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 17:24:43 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
负载:
{websiteId: 100227, accountInfo: "620503199208283920 283920*", selectedCourseKeys: ["2025"]}
accountInfo
: 
"620503199208283920 283920*"
selectedCourseKeys
: 
["2025"]
0
: 
"2025"
websiteId
: 
100227
返回：
{state: true, message: "创建订单成功", code: 200, data: null}
code
: 
200
data
: 
null
message
: 
"创建订单成功"
state
: 
true


订单列表：
请求 URL
http://***********:8090/api/order/list?page=1&pageSize=10&sortField=createTime&sortOrder=descend
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
4210
content-type
application/json; charset=UTF-8
date
Sat, 21 Jun 2025 17:25:47 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
负载：
page
1
pageSize
10
sortField
createTime
sortOrder
descend
返回订单列表：
{state: true, message: "success", code: 200, data: {,…}}
code
: 
200
data
: 
{,…}
list
: 
[{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 63498, price: 2, siteId: 100227,…},…]
0
: 
{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 63498, price: 2, siteId: 100227,…}
courseInfo
: 
"1----2025"
courseName
: 
"账号或密码错误"
createTime
: 
"2025-06-22 01:24:43"
name
: 
null
orderId
: 
63498
password
: 
"283920*"
price
: 
2
siteId
: 
100227
siteName
: 
"59iedu.com_甘肃省专技（天水市）"
status
: 
"已退款"
updateTime
: 
"2025-06-22 01:25:04"
username
: 
"620503199208283920"
uuid
: 
"79D8000F-0A8E-65C1-0C27-84F02142AA3F"。。。。以下省略，有很多页，示例只展现第一页的信息


通过账号搜索订单详细信息：：
请求 URL
http://***********:8090/api/order/list?username=622429198612171328&page=1&pageSize=10&sortField=createTime&sortOrder=descend
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
450
content-type
application/json; charset=UTF-8

date
Fri, 20 Jun 2025 08:35:49 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTA0OTQ4NjcsImlhdCI6MTc1MDQwODQ2OCwidV9kaXNjb3VudCI6MSwidXNlcl9pZCI6Nzh9.6fRFBSP6p3CsdcBlsxuB3Ff2cFAce-tigBaYA5KKCWA
host
***********:8090
proxy-connection
keep-alive
referer
http://***********:8090/order-management
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
其中622429198612171328是我搜索的账号
负载：
username
622429198612171328
page
1
pageSize
10
sortField
createTime
sortOrder
descend
返回学习数据：
{state: true, message: "success", code: 200,…}
code
: 
200
data
: 
{list: [{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 61066, price: 2, siteId: 100233,…}],…}
list
: 
[{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 61066, price: 2, siteId: 100233,…}]
0
: 
{uuid: "79D8000F-0A8E-65C1-0C27-84F02142AA3F", orderId: 61066, price: 2, siteId: 100233,…}
courseInfo
: 
"1----2025"
courseName
: 
"_订单项目学习完成_"
createTime
: 
"2025-06-18 11:07:59"
name
: 
"张月梅"
orderId
: 
61066
password
: 
"Zym861217"
price
: 
2
siteId
: 
100233
siteName
: 
"59iedu.com_甘肃省专技（定西市）"
status
: 
"已完成"
updateTime
: 
"2025-06-20 16:23:08"
username
: 
"622429198612171328"
uuid
: 
"79D8000F-0A8E-65C1-0C27-84F02142AA3F"
total
: 
1
message
: 
"success"
state
: 
true




本网站所有可学习的项目：
请求 URL
http://***********:8090/api/price/sites?page=1&pageSize=100
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-headers
*
access-control-allow-origin
*
cache-control
no-cache
connection
keep-alive
content-length
3374
content-type
application/json; charset=UTF-8
date
Sun, 10 Aug 2025 13:35:18 GMT
keep-alive
timeout=4
pragma
no-cache
proxy-connection
keep-alive
server
E2EE Server 3.0
x-powered-by
E2EE
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ5MTEzNzQsImlhdCI6MTc1NDgyNDk3NSwidV9kaXNjb3VudCI6MSwidXNlcl9pZCI6Nzh9.YkHFZ-ylr0qicNLw-Pur_fauxQdx8H3FBqCosqpp0dI
host
***********:8090
proxy-connection
keep-alive
referer
http://***********:8090/pricepage
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********

负载：
page
1
pageSize
100

返回：
{state: true, message: "success", code: 200, data: {,…}}
code
: 
200
data
: 
{,…}
list
: 
[{site_id: 100563, url: "https://dx.tcc.edu.cn/", site_name: "督学网络学院_dx.tcc.edu.cn", speed: null,…},…]
0
: 
{site_id: 100563, url: "https://dx.tcc.edu.cn/", site_name: "督学网络学院_dx.tcc.edu.cn", speed: null,…}
check_course
: 
"支持"
description
: 
"正常学习 包考试"
exam_support
: 
"支持"
format
: 
"账号 密码"
price
: 
0.75
site_id
: 
100563
site_name
: 
"督学网络学院_dx.tcc.edu.cn"
speed
: 
null
status
: 
"正常"
url
: 
"https://dx.tcc.edu.cn/"
1
: 
{site_id: 100562, url: "https://ksu.edueva.org/", site_name: "喀什大学国家级继续教育_正常学习_ksu.edueva.org",…}
2
: 
{site_id: 100561, url: "https://ksu.edueva.org/", site_name: "喀什大学国家级继续教育_加速学习_ksu.edueva.org",…}
3
: 
{site_id: 100560, url: "https://www.cqrspx.cn/", site_name: "重庆人社培训网_www.cqrspx.cn", speed: null,…}
4
: 
{site_id: 100559, url: "https://www.cqrspx.cn/", site_name: "重庆人社培训网_渝快办登录_www.cqrspx.cn", speed: null,…}
5
: 
{site_id: 100558, url: "https://wp.pep.com.cn/web/index.php?/login/index/173",…}
6
: 
{site_id: 100557, url: "http://www.xjyxjyw.com/", site_name: "新疆医学教育网_兵团推荐项目", speed: null, price: 2.5,…}
7
: 
{site_id: 100556, url: "https://web.scgb.gov.cn/#/index", site_name: "四川干部网络学院_年度50学时_web.scgb.gov.cn",…}
8
: 
{site_id: 100555, url: "https://web.scgb.gov.cn/#/index", site_name: "四川干部网络学院_培训班_web.scgb.gov.cn",…}
9
: 
{site_id: 100554, url: "https://www.91huayi.com/", site_name: "华医网_基层培训_单考试", speed: null, price: 0.5,…}
10
: 
{site_id: 100553, url: "https://www.91huayi.com/", site_name: "华医网_基层培训_正常学习", speed: null, price: 1,…}
11
: 
{site_id: 100552, url: "https://www.91huayi.com/", site_name: "华医网_基层培训_加速学习", speed: null, price: 1,…}
12
: 
{site_id: 100551, url: "https://gsqz.chinamde.cn/", site_name: "现代专技_庆阳职业技术学院_gsqz.chinamde.cn",…}
13
: 
{site_id: 100550, url: "http://jxjy.lzrta.cn/", site_name: "lllnet.cn_兰州铁路技师学院", speed: null,…}
14
: 
{site_id: 100549, url: "https://t.pep.com.cn/hnyj2025", site_name: "2025年河南省人教版义务教育新教材网络培训",…}
15
: 
{site_id: 100548, url: "https://basic.smartedu.cn/training/2025sqpx", site_name: "智慧中小学_2025暑假教师研修",…}
16
: 
{site_id: 100547, url: "https://teacher.vocational.smartedu.cn/", site_name: "智慧中小学_职业教育_2025暑假教师研修",…}
17
: 
{site_id: 100546, url: "https://teacher.higher.smartedu.cn/h/home/",…}
18
: 
{site_id: 100544, url: "http://www.hbszjw.com/", site_name: "鹤壁市专业技术人员继续教育网络学院_www.hbszjw.com",…}
19
: 
{site_id: 100543, url: "http://221.214.69.254:9090/",…}
20
: 
{site_id: 100542, url: "http://221.214.69.254:9090/",…}
21
: 
{site_id: 100541, url: "http://221.214.69.254:9090/",…}
22
: 
{site_id: 100540, url: "https://www.91huayi.com/", site_name: "华医网_全员专项_常速_取消申请", speed: null,…}
23
: 
{site_id: 100539, url: "https://www.91huayi.com/", site_name: "华医网_全员专项_加速_取消申请", speed: null,…}
24
: 
{site_id: 100538, url: "https://www.91huayi.com/", site_name: "华医网_全员专项_常速_申请证书", speed: null,…}
25
: 
{site_id: 100537, url: "https://www.91huayi.com/", site_name: "华医网_全员专项_加速_申请证书", speed: null,…}
26
: 
{site_id: 100536, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_收藏课程_常速_取消申请", speed: null,…}
27
: 
{site_id: 100535, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_收藏课程_常速_申请证书", speed: null,…}
28
: 
{site_id: 100534, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_收藏课程_加速_取消申请", speed: null,…}
29
: 
{site_id: 100533, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_收藏课程_加速_申请证书", speed: null,…}
30
: 
{site_id: 100532, url: "http://guizhou.zxjxjy.com/", site_name: "贵州继续教育网_guizhou.zxjxjy.com",…}
31
: 
{site_id: 100531, url: "http://www.qnzedu.cn/", site_name: "黔南州教育云_www.qnzedu.cn", speed: null,…}
32
: 
{site_id: 100530, url: "https://aitrain.zjer.cn/", site_name: "之江汇_浙江省中小学教师人工智能研修专题_aitrain.zjer.cn",…}
33
: 
{site_id: 100529, url: "https://wp.pep.com.cn/web/index.php?/login/index/165/1/",…}
34
: 
{site_id: 100528, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程查询_常速_取消申请", speed: null,…}
35
: 
{site_id: 100527, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程查询_常速_申请证书", speed: null,…}
36
: 
{site_id: 100526, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程查询_加速_取消申请", speed: null,…}
37
: 
{site_id: 100525, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程查询_加速_申请证书", speed: null,…}
38
: 
{site_id: 100524, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程名称_常速_取消申请", speed: null,…}
39
: 
{site_id: 100523, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程名称_常速_申请证书", speed: null,…}
40
: 
{site_id: 100522, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程名称_加速_取消申请", speed: null,…}
41
: 
{site_id: 100521, url: "https://www.91huayi.com/", site_name: "华医网_继续教育_课程名称_加速_申请证书", speed: null,…}
42
: 
{site_id: 100520, url: "https://www.ahnyjy.cn/", site_name: "安徽农业教育学习在线_www.ahnyjy.cn", speed: null,…}
43
: 
{site_id: 100519, url: "https://jxjypx.aust.edu.cn/", site_name: "安徽理工大学继续教育培训在线_jxjypx.aust.edu.cn",…}
44
: 
{site_id: 100518, url: "https://zmks.zjzx.ah.cn/", site_name: "中煤矿建集团专技在线_zmks.zjzx.ah.cn",…}
45
: 
{site_id: 100517, url: "https://chizhou.zjzx.ah.cn/", site_name: "池州专业技术人员继续教育在线_chizhou.zjzx.ah.cn",…}
46
: 
{site_id: 100516, url: "https://www.zjzx.ah.cn/", site_name: "安徽专业技术人员继续教育在线_www.zjzx.ah.cn",…}
47
: 
{site_id: 100515, url: "https://www.xuexiyun.org.cn/", site_name: "智慧云学院_xuexiyun.org.cn", speed: null,…}
48
: 
{site_id: 100514, url: "https://www.ttcdw.cn/h/spec/wlmqzydx/",…}
49
: 
{site_id: 100508, url: "https://www.samrela.com/", site_name: "国家市场监督管理总局网络学院", speed: null,…}
50
: 
{site_id: 100507, url: "https://www.samrela.com/", site_name: "国家市场监督管理总局网络学院_专题班", speed: null,…}
51
: 
{site_id: 100506, url: "https://gzqdn.brjxjy.com/", site_name: "贵州电子信息职业技术学院_gzqdn.brjxjy.com",…}
52
: 
{site_id: 100505, url: "https://passport.qlteacher.com/",…}
53
: 
{site_id: 100504, url: "http://shq.nmgmsz.com/", site_name: "呼和浩特市赛罕区企事业人员培训平台_shq.nmgmsz.com",…}
54
: 
{site_id: 100503, url: "https://bjhd.chinahrt.com/", site_name: "国培网_海淀区专业技术人员及事业单位人员公共知识培训",…}
55
: 
{site_id: 100502, url: "https://gp.chinahrt.com/index.html#/ahszyjsjxjyLogin",…}
56
: 
{site_id: 100501, url: "https://gp.chinahrt.com/index.html#/lgezjLogin",…}
57
: 
{site_id: 100500, url: "https://qingshuihe.chinahrt.com/", site_name: "国培网_清水河县专业技术人员继续教育培训网",…}
58
: 
{site_id: 100499, url: "https://wlcb.chinahrt.com/", site_name: "国培网_乌兰察布市专业技术人员", speed: null,…}
59
: 
{site_id: 100498, url: "https://wlcbzj.chinahrt.com/", site_name: "国培网_乌兰察布党校专业技术人员", speed: null,…}
60
: 
{site_id: 100497, url: "https://tlslyhcy.chinahrt.com/", site_name: "国培网_通辽市林业和草原专业技术人员", speed: null,…}
61
: 
{site_id: 100496, url: "https://tjzgwp.chinahrt.com/", site_name: "国培网_天津市政工人员继续教育培训网", speed: null,…}
62
: 
{site_id: 100495, url: "https://gp.chinahrt.com/index.html#/lzzjLogin",…}
63
: 
{site_id: 100494, url: "https://gphl.chinahrt.com/", site_name: "国培网_国培互联技术培训中心", speed: null,…}
64
: 
{site_id: 100493, url: "https://www.cnaestc.cn/", site_name: "中国教育科学研究院培训中心_单视频_www.cnaestc.cn",…}
65
: 
{site_id: 100492, url: "https://wwjxjy.yxlearning.com/", site_name: "learning_武威职业技术大学专业技术人员继续教育在线平台",…}
66
: 
{site_id: 100491, url: "https://lcjszj.yxlearning.com/", site_name: "learning_山东省聊城市技师学院专业课",…}
67
: 
{site_id: 100490, url: "http://220.170.158.251:8090/FrmLogin.aspx", site_name: "安化技校考试学习系统",…}
68
: 
{site_id: 100489, url: "https://www.mayortraining.com/cms/xinjiang.htm",…}
69
: 
{site_id: 100488, url: "https://www.mayortraining.com/",…}
70
: 
{site_id: 100487, url: "https://hndj.ksyun.com/console/sign_up", site_name: "河南党员教育云课堂_hndj.ksyun.com",…}
71
: 
{site_id: 100486, url: "https://www.sqgj.gov.cn/", site_name: "陕西干部网络学院_专题班", speed: null, price: 1,…}
72
: 
{site_id: 100485, url: "https://tqx.chinahrt.cn/", site_name: "人事人才服务网_突泉县专业技术人员继续教育培训网", speed: null,…}
73
: 
{site_id: 100484, url: "https://ghatg.hcmde.com/login/", site_name: "甘肃省公路航空旅游投资集团_ghatg.hcmde.com",…}
74
: 
{site_id: 100483, url: "https://sczj.chinamde.cn/login/", site_name: "现代专技_成都理工大学_sczj.chinamde.cn",…}
75
: 
{site_id: 100482, url: "https://xcq.nmgrcw.com/",…}
76
: 
{site_id: 100481, url: "https://hnkj.ghlearning.com/", site_name: "河南专技在线_会计人员继续教育网络学习平台_自动改密",…}
77
: 
{site_id: 100480, url: "https://hnkj.ghlearning.com/", site_name: "河南专技在线_会计人员继续教育网络学习平台", speed: null,…}
78
: 
{site_id: 100479, url: "http://r.forteacher.cn/", site_name: "教学质量提升云平台_r.forteacher.cn", speed: null,…}
79
: 
{site_id: 100478, url: "http://r.forteacher.cn/", site_name: "菁师助手_r.forteacher.cn", speed: null,…}
80
: 
{site_id: 100476, url: "https://sypx.csu.edu.cn/zj/#/", site_name: "中南大学专业技术人员继续教育网_专业技术人员入口",…}
81
: 
{site_id: 100475, url: "https://cljxjy.zpwedu.com/",…}
82
: 
{site_id: 100474, url: "https://gwypx.gsdj.gov.cn/", site_name: "甘肃省公务员网络培训网_gwypx.gsdj.gov.cn",…}
83
: 
{site_id: 100472, url: "https://www.edueva.org/", site_name: "阿勒泰智慧教育云平台_www.altjyed.cn", speed: null,…}
84
: 
{site_id: 100471, url: "http://ptce.gx12333.net/", site_name: "广西专业技术人员_选修课_ptce.gx12333.net",…}
85
: 
{site_id: 100470, url: "https://www.lngbzx.gov.cn/pc/index.html#/",…}
86
: 
{site_id: 100469, url: "http://bzys.jjyxt.cn/", site_name: "巴中开放大学_bzys.jjyxt.cn", speed: null,…}
87
: 
{site_id: 100468, url: "http://ptce.gx12333.net/", site_name: "广西专业技术人员_公需课_ptce.gx12333.net",…}
88
: 
{site_id: 100466, url: "https://zjpt.jxnu.edu.cn/", site_name: "江西专技学习网（江西师范大学）_zjpt-web.jxnu.edu.cn",…}
89
: 
{site_id: 100465, url: "https://hwonline.jumingedu.com/cme",…}
90
: 
{site_id: 100463, url: "https://jxzj.webtrn.cn/", site_name: "网梯科技_江西专技在线_jxzj.webtrn.cn", speed: null,…}
91
: 
{site_id: 100462, url: "https://btln.tsbtgs.cn/", site_name: "现代专技_陇南专业技术继续教育_btln.tsbtgs.cn",…}
92
: 
{site_id: 100461, url: "https://www.91huayi.com/", site_name: "华医网_职业健康_安全生产", speed: null, price: 1,…}
93
: 
{site_id: 100460, url: "https://ztxxpt.zgzjzj.net/", site_name: "昭通市事业单位工作人员培训管理平台_ztxxpt.zgzjzj.net",…}
94
: 
{site_id: 100459, url: "https://office.teacher.com.cn", site_name: "师学通_单视频_office.teacher.com.cn",…}
95
: 
{site_id: 100457, url: "https://wp.pep.com.cn/web/index.php?/login/index/152/1", site_name: "人教云教研",…}
96
: 
{site_id: 100455, url: "http://dz.huaxiajiaoshiyanpei.com/",…}
97
: 
{site_id: 100454, url: "http://pq.huaxiajiaoshiyanpei.com/",…}
98
: 
{site_id: 100453, url: "http://hebi.huaxiajiaoshiyanpei.com/",…}
99
: 
{site_id: 100452, url: "https://www.hnsydwpx.cn/", site_name: "智慧人社_湖南开放大学事业单位工作人员培训网_www.hnsydwpx.cn",…}
total
: 
531
message
: 
"success"
state
: 
true

具体细节省略

其中http://***********:8090/api/price/sites?page=1&pageSize=100
也就是负载page=1代表第一页，pageSize=100代表每页100个，可以根据这个翻页查看所有可以学习的网站
