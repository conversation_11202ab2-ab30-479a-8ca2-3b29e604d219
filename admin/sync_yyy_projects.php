<?php
/**
 * yyy教育项目同步脚本
 * 从yyy教育API获取最新的项目列表并同步到数据库
 */

// 引入必要的文件
require_once '../confing/config.php';
require_once '../Checkorder/configuration.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['user']) || $_SESSION['user']['uid'] != 1) {
    exit('{"code":-1,"msg":"无权限访问"}');
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查是否是测试连接请求
if (isset($_GET['test']) && $_GET['test'] == '1') {
    testConnection();
    exit;
}

function testConnection() {
    global $DB;

    try {
        // 获取yyy教育货源配置
        $huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'yyy' LIMIT 1");
        if (!$huoyuan) {
            exit('{"code":-1,"msg":"未找到yyy教育货源配置"}');
        }

        // 测试登录
        $login_data = array(
            "username" => $huoyuan["user"],
            "password" => $huoyuan["pass"]
        );
        $login_url = $huoyuan["url"] . "/api/login";
        $headers = array(
            "Content-Type: application/json",
            "Accept: application/json, text/plain, */*",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        );

        $login_result = get_url3($login_url, json_encode($login_data), "", $headers);
        $login_response = json_decode($login_result, true);

        if (!$login_response || $login_response['code'] != 200) {
            exit('{"code":-1,"msg":"API连接失败: ' . ($login_response['message'] ?? '无法连接到服务器') . '"}');
        }

        echo '{"code":1,"msg":"连接测试成功","data":{"username":"' . $login_response['data']['username'] . '","expires":"' . $login_response['data']['expires'] . '"}}';

    } catch (Exception $e) {
        echo '{"code":-1,"msg":"连接测试失败: ' . $e->getMessage() . '"}';
    }
}

try {
    // 获取yyy教育货源配置
    $huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'yyy' LIMIT 1");
    if (!$huoyuan) {
        exit('{"code":-1,"msg":"未找到yyy教育货源配置"}');
    }

    // 获取yyy教育分类ID
    $fenlei = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = 'yyy教育' LIMIT 1");
    if (!$fenlei) {
        exit('{"code":-1,"msg":"未找到yyy教育分类"}');
    }
    $fenlei_id = $fenlei['id'];

    // 第一步：登录获取token
    $login_data = array(
        "username" => $huoyuan["user"], 
        "password" => $huoyuan["pass"]
    );
    $login_url = $huoyuan["url"] . "/api/login";
    $headers = array(
        "Content-Type: application/json",
        "Accept: application/json, text/plain, */*",
        "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "X-Requested-With: XMLHttpRequest"
    );
    
    $login_result = get_url3($login_url, json_encode($login_data), "", $headers);
    $login_response = json_decode($login_result, true);
    
    if (!$login_response || $login_response['code'] != 200) {
        exit('{"code":-1,"msg":"登录失败: ' . ($login_response['message'] ?? '未知错误') . '"}');
    }
    
    $access_token = $login_response['data']['accessToken'];
    $refresh_token = $login_response['data']['refreshToken'];
    
    // 构建Cookie
    $token_data = array(
        "accessToken" => $access_token,
        "expires" => 1919520000000,
        "refreshToken" => $refresh_token
    );
    $token_json = json_encode($token_data, JSON_UNESCAPED_SLASHES);
    $encoded_token = urlencode($token_json);
    $cookie = "authorized-token=" . $encoded_token . "; multiple-tabs=true";
    
    // 第二步：获取网站列表
    $site_data = array("version" => "");
    $site_headers = array(
        "Content-Type: application/json",
        "Accept: application/json, text/plain, */*",
        "Authorization: Bearer " . $access_token,
        "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "X-Requested-With: XMLHttpRequest"
    );
    
    $site_url = $huoyuan["url"] . "/api/site";
    $site_result = get_url3($site_url, json_encode($site_data), $cookie, $site_headers);
    $site_response = json_decode($site_result, true);
    
    if (!$site_response || $site_response['code'] != 200) {
        exit('{"code":-1,"msg":"获取网站列表失败: ' . ($site_response['message'] ?? '未知错误') . '"}');
    }
    
    $projects = $site_response['data']['list'];
    $total_count = count($projects);
    
    // 第三步：同步项目到数据库
    $success_count = 0;
    $error_count = 0;
    $update_count = 0;
    
    foreach ($projects as $project) {
        try {
            // 解析价格
            $price_str = $project['price_unit'];
            $price = 0;
            if (preg_match('/(\d+\.?\d*)/', $price_str, $matches)) {
                $price = floatval($matches[1]);
            }

            // 清理项目名称，移除常见的yyy教育前缀
            $original_name = $project['name'];
            $cleaned_name = $original_name;

            // 定义需要移除的前缀
            $prefixes_to_remove = array(
                'YYY继教-',
                'yyy教育-',
                'YYY-',
                'yyy-',
                '学习公社云-',
                '课件全学-',
                'YYY继教学习公社云-',
                'YYY继教-学习公社云-',
                'YYY继教-学习公社云-课件全学-',
                'YYY继教学习公社云课件全学-'
            );

            // 移除前缀
            foreach ($prefixes_to_remove as $prefix) {
                if (strpos($cleaned_name, $prefix) === 0) {
                    $cleaned_name = substr($cleaned_name, strlen($prefix));
                    break;
                }
            }

            // 检查是否已存在
            $existing = $DB->get_row("SELECT cid FROM qingka_wangke_class WHERE noun = '" . $project['id'] . "' AND fenlei = '$fenlei_id' LIMIT 1");

            if ($existing) {
                // 更新现有商品
                $sql = "UPDATE qingka_wangke_class SET
                        name = ?,
                        price = ?,
                        content = ?,
                        uptime = NOW()
                        WHERE cid = ?";
                $params = [
                    $cleaned_name,
                    $price,
                    $project['trans'] . ' (yyy教育聚合平台)',
                    $existing['cid']
                ];
                $DB->prepare_query($sql, $params);
                $update_count++;
            } else {
                // 插入新商品
                $sql = "INSERT INTO qingka_wangke_class (
                    sort, name, getnoun, noun, price, queryplat, docking,
                    yunsuan, content, status, fenlei, addtime
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                $params = [
                    50, // 默认排序
                    $cleaned_name,
                    $project['id'],
                    $project['id'],
                    $price,
                    $huoyuan['hid'],
                    $huoyuan['hid'],
                    '*',
                    $project['trans'] . ' (yyy教育聚合平台)',
                    1,
                    $fenlei_id
                ];
                $DB->prepare_query($sql, $params);
                $success_count++;
            }
        } catch (Exception $e) {
            $error_count++;
            error_log("同步项目失败: " . $project['name'] . " - " . $e->getMessage());
        }
    }
    
    // 返回同步结果
    $result = array(
        "code" => 1,
        "msg" => "同步完成",
        "data" => array(
            "total_projects" => $total_count,
            "new_added" => $success_count,
            "updated" => $update_count,
            "errors" => $error_count,
            "version" => $site_response['data']['version'],
            "lastoid" => $site_response['data']['lastoid']
        )
    );
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(array(
        "code" => -1,
        "msg" => "同步失败: " . $e->getMessage()
    ), JSON_UNESCAPED_UNICODE);
}
?>
