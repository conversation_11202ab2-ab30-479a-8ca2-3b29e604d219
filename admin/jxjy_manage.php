<?php
/**
 * 易教育管理页面
 * 提供项目同步、状态查看等功能
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 检查管理员权限
if (!isset($userrow) || $userrow['uid'] != 1) {
    exit('无权限访问');
}

$action = $_GET['action'] ?? '';

// 处理AJAX请求
if ($action == 'sync_projects') {
    header('Content-Type: application/json');
    
    try {
        // 执行同步脚本
        ob_start();
        include(dirname(__FILE__) . '/../api/jxjy_sync_projects.php');
        $output = ob_get_clean();
        
        // 检查是否同步成功
        if (strpos($output, '所有项目同步成功') !== false) {
            echo json_encode(array('code' => 1, 'msg' => '项目同步成功', 'output' => $output));
        } else {
            echo json_encode(array('code' => -1, 'msg' => '项目同步失败', 'output' => $output));
        }
    } catch (Exception $e) {
        echo json_encode(array('code' => -1, 'msg' => '同步异常: ' . $e->getMessage()));
    }
    exit;
}

if ($action == 'get_stats') {
    header('Content-Type: application/json');
    
    // 获取统计信息
    $huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
    $project_count = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_jxjyclass WHERE status = 1");
    $order_count = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_order WHERE ptname LIKE '%易教育%'");
    $jxjy_order_count = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_jxjyorder");
    
    $stats = array(
        'huoyuan_status' => $huoyuan ? '已配置' : '未配置',
        'project_count' => $project_count['count'],
        'order_count' => $order_count['count'],
        'jxjy_order_count' => $jxjy_order_count['count'],
        'token_status' => ($huoyuan && !empty($huoyuan['token'])) ? '有效' : '无效'
    );
    
    echo json_encode(array('code' => 1, 'data' => $stats));
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>易教育管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/layui/css/layui.css">
    <script src="/assets/layui/layui.js"></script>
    <script src="/assets/vue.min.js"></script>
    <script src="/assets/vue-resource.min.js"></script>
</head>
<body>
    <div id="app" class="layui-container" style="margin-top: 20px;">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>易教育系统管理</h2>
                    </div>
                    <div class="layui-card-body">
                        
                        <!-- 系统状态 -->
                        <fieldset class="layui-elem-field">
                            <legend>系统状态</legend>
                            <div class="layui-field-box">
                                <table class="layui-table">
                                    <tbody>
                                        <tr>
                                            <td>货源配置</td>
                                            <td>
                                                <span :class="stats.huoyuan_status == '已配置' ? 'layui-badge layui-bg-green' : 'layui-badge layui-bg-red'">
                                                    {{ stats.huoyuan_status }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Token状态</td>
                                            <td>
                                                <span :class="stats.token_status == '有效' ? 'layui-badge layui-bg-green' : 'layui-badge layui-bg-red'">
                                                    {{ stats.token_status }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>项目数量</td>
                                            <td>{{ stats.project_count }} 个</td>
                                        </tr>
                                        <tr>
                                            <td>主订单数</td>
                                            <td>{{ stats.order_count }} 个</td>
                                        </tr>
                                        <tr>
                                            <td>易教育订单数</td>
                                            <td>{{ stats.jxjy_order_count }} 个</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </fieldset>
                        
                        <!-- 操作按钮 -->
                        <fieldset class="layui-elem-field">
                            <legend>管理操作</legend>
                            <div class="layui-field-box">
                                <button class="layui-btn layui-btn-primary" @click="refreshStats">
                                    <i class="layui-icon layui-icon-refresh"></i> 刷新状态
                                </button>
                                <button class="layui-btn layui-btn-normal" @click="syncProjects" :disabled="syncing">
                                    <i class="layui-icon layui-icon-download-circle"></i> 
                                    {{ syncing ? '同步中...' : '同步项目数据' }}
                                </button>
                                <button class="layui-btn layui-btn-warm" @click="openTestPage">
                                    <i class="layui-icon layui-icon-test"></i> 功能测试
                                </button>
                                <button class="layui-btn layui-btn-danger" @click="viewLogs">
                                    <i class="layui-icon layui-icon-file"></i> 查看日志
                                </button>
                            </div>
                        </fieldset>
                        
                        <!-- 同步日志 -->
                        <fieldset class="layui-elem-field" v-show="syncLog">
                            <legend>同步日志</legend>
                            <div class="layui-field-box">
                                <pre style="background: #f2f2f2; padding: 10px; max-height: 400px; overflow-y: auto;">{{ syncLog }}</pre>
                            </div>
                        </fieldset>
                        
                        <!-- 使用说明 -->
                        <fieldset class="layui-elem-field">
                            <legend>使用说明</legend>
                            <div class="layui-field-box">
                                <div class="layui-text">
                                    <h3>修复步骤：</h3>
                                    <ol>
                                        <li><strong>检查货源配置</strong>：确保易教育货源已正确配置</li>
                                        <li><strong>同步项目数据</strong>：点击"同步项目数据"按钮，从易教育API获取最新项目列表</li>
                                        <li><strong>测试功能</strong>：同步完成后，点击"功能测试"验证各项功能</li>
                                        <li><strong>开始使用</strong>：测试通过后即可正常使用查课、下单等功能</li>
                                    </ol>
                                    
                                    <h3>注意事项：</h3>
                                    <ul>
                                        <li>首次使用前必须先同步项目数据</li>
                                        <li>如果出现"项目信息不存在"错误，请重新同步项目数据</li>
                                        <li>建议定期同步项目数据以获取最新的项目信息</li>
                                        <li>如有问题请查看同步日志或联系技术支持</li>
                                    </ul>
                                </div>
                            </div>
                        </fieldset>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    layui.use(['layer'], function(){
        var layer = layui.layer;
        
        var vm = new Vue({
            el: '#app',
            data: {
                stats: {
                    huoyuan_status: '检查中...',
                    token_status: '检查中...',
                    project_count: 0,
                    order_count: 0,
                    jxjy_order_count: 0
                },
                syncing: false,
                syncLog: ''
            },
            mounted: function() {
                this.refreshStats();
            },
            methods: {
                refreshStats: function() {
                    var self = this;
                    this.$http.get('?action=get_stats').then(function(response) {
                        if (response.data.code == 1) {
                            self.stats = response.data.data;
                        }
                    });
                },
                
                syncProjects: function() {
                    var self = this;
                    
                    layer.confirm('确定要同步易教育项目数据吗？<br>这个过程可能需要几分钟时间。', {
                        title: '确认同步',
                        icon: 3
                    }, function(index) {
                        layer.close(index);
                        
                        self.syncing = true;
                        self.syncLog = '正在同步项目数据，请稍候...\n';
                        
                        var loading = layer.load(2, {content: '正在同步项目数据...'});
                        
                        self.$http.get('?action=sync_projects').then(function(response) {
                            layer.close(loading);
                            self.syncing = false;
                            
                            if (response.data.code == 1) {
                                self.syncLog = response.data.output;
                                layer.msg('项目同步成功！', {icon: 1});
                                self.refreshStats();
                            } else {
                                self.syncLog = response.data.output || response.data.msg;
                                layer.msg('项目同步失败：' + response.data.msg, {icon: 2});
                            }
                        }).catch(function(error) {
                            layer.close(loading);
                            self.syncing = false;
                            self.syncLog = '同步请求失败：' + error.message;
                            layer.msg('同步请求失败', {icon: 2});
                        });
                    });
                },
                
                openTestPage: function() {
                    window.open('/test/jxjy_test.php', '_blank');
                },
                
                viewLogs: function() {
                    layer.alert('日志查看功能开发中...', {icon: 0});
                }
            }
        });
    });
    </script>
</body>
</html>
