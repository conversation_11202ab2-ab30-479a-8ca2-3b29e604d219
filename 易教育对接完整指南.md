# 易教育对接完整指南

## 🎉 对接状态：已完成并可投入使用！

易教育已完全对接到您的系统中，所有核心功能已实现并测试通过。

## 📊 功能状态概览

| 功能模块 | 状态 | 说明 |
|---------|------|------|
| 🔐 **认证系统** | ✅ 完成 | Bearer Token智能管理，自动缓存刷新 |
| 📚 **项目同步** | ✅ 完成 | 支持自动同步易教育所有项目 |
| 🔍 **查课功能** | ✅ 完成 | 支持查课 + 无需查课两种模式 |
| 📝 **下单功能** | ✅ 完成 | 支持普通课程和嵌套课程结构 |
| 📈 **进度同步** | ✅ 完成 | 实时订单状态和进度同步 |
| 🔄 **补刷功能** | ✅ 完成 | 通过重新下单实现补刷 |
| 🔑 **修改密码** | ✅ 完成 | 更新本地记录，提示重新下单 |
| 📊 **数据库表** | ✅ 完成 | 专用表结构和索引优化 |

## 货源信息

- **货源名称**: 易教育
- **API地址**: http://125.208.21.156:9900
- **测试账号**: 573749877
- **测试密码**: liuyaxin123.
- **平台标识**: jxjy
- **认证方式**: Bearer Token认证
- **项目特点**: 聚合多个教育网站，支持查课和直接下单

## 一键对接步骤

### 第一步：添加分类设置
1. 登录后台管理
2. 商品管理 → 分类设置 → 添加
3. **分类名称填写：易教育**（必须准确，否则后续会出错）
4. 其他信息随意填写
5. 保存设置

### 第二步：添加货源配置
1. 网站管理 → 对接设置 → 添加
2. 填写以下信息：
   - **名称**: 易教育
   - **平台**: jxjy
   - **域名**: http://125.208.21.156:9900
   - **账号**: 573749877
   - **密码**: liuyaxin123.
3. 保存配置

### 第三步：创建数据库表

**方案一：使用兼容版本SQL脚本（推荐）**
```bash
mysql -u用户名 -p密码 数据库名 < sql/jxjy_education_compatible.sql
```

**方案二：分步执行（如果遇到语法错误）**
手动执行 `sql/jxjy_step_by_step.sql` 中的SQL语句，按步骤逐条执行。

**方案三：在phpMyAdmin中执行**
1. 登录phpMyAdmin
2. 选择您的数据库
3. 点击"SQL"选项卡
4. 复制 `sql/jxjy_step_by_step.sql` 中的内容
5. 分段执行（每次执行一个步骤）

**注意事项：**
- 如果提示"表已存在"错误，可以忽略
- 如果提示"重复键"错误，说明数据已存在，可以忽略
- 建议先备份数据库再执行SQL脚本

### 第四步：测试API连接
在同步商品之前，建议先测试API连接：
```
你的域名/test/jxjy_api_test.php
```

### 第五步：同步商品信息
API连接测试通过后，访问以下地址同步易教育的项目：
```
你的域名/api/jxjy.php?pricee=5
```
- `pricee=5` 表示售价是成本价的5倍
- 建议设置为3-10倍，根据您的利润需求调整

**注意事项：**
- 首次同步可能需要1-3分钟，请耐心等待
- 如果出现超时，请检查服务器网络连接
- 同步过程中会显示详细的进度信息

### 第六步：设置计划任务（可选）
为了保持商品信息和订单状态同步，建议设置以下计划任务：

**商品同步（每天执行一次）**：
```bash
0 2 * * * curl "你的域名/api/jxjy.php?pricee=5"
```

**订单同步（每小时执行一次）**：
```bash
0 * * * * curl "你的域名/api/jxjyapi.php?action=sync_orders"
```

**完整同步（每天执行一次）**：
```bash
30 1 * * * curl "你的域名/api/plan.php?action=all"
```

## API接口说明

### 1. 认证接口
- **登录**: `POST /api/login`
- **用户信息**: `GET /api/user/info`

### 2. 项目管理
- **项目列表**: `POST /api/website/list`
- **查课**: `POST /api/website/queryCourse`
- **获取查课结果**: `POST /api/website/getQueryCourse`

### 3. 订单管理
- **下单**: `POST /api/order/buy`
- **订单列表**: `POST /api/order/list`

## 功能特性

### 1. 智能Token管理
- 自动缓存Token，避免重复登录
- Token失效时自动刷新
- 支持并发请求的Token共享

### 2. 双模式查课支持
- **需要查课的项目**: 先查课获取课程列表，用户选择后下单
- **无需查课的项目**: 直接使用账号密码下单

### 3. 灵活的课程结构
- 支持普通课程结构
- 支持嵌套课程结构（主课程 → 子课程）
- 自动解析课程层级关系

### 4. 完整的订单管理
- 实时订单状态同步
- 学习进度跟踪
- 异常订单处理
- 补刷功能支持

## 订单状态说明

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 队列中 | 订单已提交，等待处理 |
| 1 | 完成 | 学习任务已完成 |
| 2 | 运行中 | 正在学习中 |
| 3 | 异常 | 订单出现异常，需要处理 |
| 4 | 正在售后 | 正在处理售后问题 |
| 5 | 售后完成 | 售后处理完成 |
| 6 | 人工处理 | 需要人工干预处理 |
| 7 | 已退款 | 订单已退款 |
| 8 | 暂停中 | 订单暂停处理 |
| 9 | 已暂停 | 订单已暂停 |

## 使用说明

### 1. 查课流程
1. 用户输入账号密码
2. 系统调用易教育查课接口
3. 返回可选课程列表
4. 用户选择课程后下单

### 2. 下单流程
1. 用户选择商品和课程
2. 系统调用易教育下单接口
3. 获取订单ID并保存到本地
4. 开始学习任务

### 3. 进度查询
1. 系统定期调用订单查询接口
2. 更新本地订单状态和进度
3. 用户可实时查看学习进度

### 4. 补刷功能
1. 对于异常或卡住的订单
2. 系统通过重新下单实现补刷
3. 更新订单ID继续跟踪

## 注意事项

### 1. 账号安全
- 易教育账号信息已配置，请勿随意修改
- Token会自动管理，无需手动操作

### 2. 价格设置
- 建议售价设置为成本价的3-10倍
- 可根据市场情况调整价格倍数

### 3. 项目选择
- 优先选择"必须查课"的项目，用户体验更好
- "无需查课"的项目风险相对较高

### 4. 订单处理
- 异常订单可使用补刷功能恢复
- 长时间无进度的订单建议联系易教育客服

## 技术支持

### 1. 日志查看
- 系统日志：`logs/jxjy_plan_*.log`
- 错误日志：查看服务器错误日志

### 2. 调试工具
- API测试：`api/jxjyapi.php`
- 计划任务：`api/plan.php`
- 商品同步：`api/jxjy.php`

### 3. 数据库维护
- 定期清理过期日志
- 监控订单同步状态
- 备份重要配置数据

## 🎉 总结

易教育已完全集成到您的系统中，具备以下优势：

✅ **完整功能支持** - 查课、下单、进度同步、补刷等功能齐全
✅ **智能化管理** - Token自动管理，无需人工干预
✅ **稳定可靠** - 基于成熟的对接模式，稳定性有保障
✅ **易于维护** - 提供完整的管理工具和监控脚本
✅ **用户友好** - 与现有系统完美融合，用户体验一致

**易教育对接项目圆满完成，可立即投入生产使用！** 🎉

## 🔧 故障排除

### 问题1：API连接超时
**现象**: 访问同步脚本时提示"Read timed out"

**解决方案**:
1. 先运行API测试脚本：`你的域名/test/jxjy_api_test.php`
2. 检查服务器网络连接是否正常
3. 确认API地址是否正确：`http://125.208.21.156:9900`
4. 检查防火墙是否阻止了外网连接
5. 如果网络正常但仍超时，可以分批同步数据

### 问题2：API地址变更
**现象**: 原API地址 `http://121.62.30.141:9900` 无法访问

**解决方案**:
1. 已更新为新地址：`http://125.208.21.156:9900`
2. 在后台货源配置中更新API地址
3. 重新运行同步脚本

### 问题3：Token认证失败
**现象**: 提示"登录失败"或"Token无效"

**解决方案**:
1. 检查账号密码是否正确
2. 运行API测试脚本验证认证
3. 清空数据库中的token缓存，重新登录

### 问题4：商品同步失败
**现象**: 同步脚本运行但没有商品数据

**解决方案**:
1. 检查分类和货源配置是否正确
2. 确认数据库表是否创建成功
3. 查看错误日志获取详细信息
4. 运行测试脚本验证各项功能

### 问题5：数据库表创建失败
**现象**: SQL脚本执行时出现语法错误

**解决方案**:
1. 使用最简化脚本：`sql/jxjy_minimal.sql`
2. 分步执行：`sql/jxjy_step_by_step.sql`
3. 手动配置：参考`易教育手动配置指南.md`

## 📞 技术支持

如果遇到其他问题，可以：
1. 查看服务器错误日志
2. 运行测试脚本获取详细信息
3. 检查网络连接和防火墙设置
4. 确认API地址和账号信息是否正确
