<?php
/**
 * 去除yyy教育项目名称前缀脚本
 * 移除"YYY继教-"、"yyy教育-"等前缀，让项目名称更简洁
 */

// 引入公共配置文件
include('confing/common.php');

echo "<h1>yyy教育项目名称前缀清理工具</h1>\n";
echo "<hr>\n";

// 定义需要移除的前缀列表
$prefixes_to_remove = array(
    'YYY继教-',
    'yyy教育-',
    'YYY-',
    'yyy-',
    '学习公社云-',
    '课件全学-',
    'YYY继教学习公社云-',
    'YYY继教-学习公社云-',
    'YYY继教-学习公社云-课件全学-',
    'YYY继教学习公社云课件全学-'
);

// 获取yyy教育分类ID
$fenlei = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = 'yyy教育' LIMIT 1");
if (!$fenlei) {
    echo "❌ 未找到yyy教育分类\n";
    exit;
}
$fenlei_id = $fenlei['id'];

echo "<h2>第一步：分析现有数据</h2>\n";

// 查询所有yyy教育相关的商品
$query = "SELECT cid, name FROM qingka_wangke_class WHERE fenlei = '$fenlei_id' ORDER BY cid";
$result = $DB->query($query);

$total_count = 0;
$need_update_count = 0;
$products_to_update = array();

echo "<h3>扫描yyy教育商品...</h3>\n";

while ($row = $DB->fetch($result)) {
    $total_count++;
    $original_name = $row['name'];
    $cleaned_name = $original_name;
    
    // 检查是否包含需要移除的前缀
    $has_prefix = false;
    foreach ($prefixes_to_remove as $prefix) {
        if (strpos($cleaned_name, $prefix) === 0) {
            $cleaned_name = substr($cleaned_name, strlen($prefix));
            $has_prefix = true;
            break;
        }
    }
    
    if ($has_prefix && $cleaned_name !== $original_name) {
        $need_update_count++;
        $products_to_update[] = array(
            'cid' => $row['cid'],
            'original_name' => $original_name,
            'cleaned_name' => $cleaned_name
        );
        
        echo "📝 CID {$row['cid']}: \"{$original_name}\" → \"{$cleaned_name}\"\n";
    }
}

echo "<hr>\n";
echo "<h2>扫描结果</h2>\n";
echo "📊 总商品数量: {$total_count}\n";
echo "🔧 需要更新的商品: {$need_update_count}\n";
echo "<hr>\n";

if ($need_update_count == 0) {
    echo "✅ 所有商品名称都已经是简洁格式，无需更新！\n";
    exit;
}

// 询问是否执行更新
echo "<h2>第二步：执行更新</h2>\n";
echo "⚠️  即将更新 {$need_update_count} 个商品的名称\n";
echo "📝 更新详情:\n";

foreach ($products_to_update as $product) {
    echo "   CID {$product['cid']}: \"{$product['original_name']}\" → \"{$product['cleaned_name']}\"\n";
}

echo "\n";

// 检查是否通过GET参数确认执行
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<p><strong>⚠️  请确认是否执行更新？</strong></p>\n";
    echo "<p><a href='?confirm=yes' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>✅ 确认执行更新</a></p>\n";
    echo "<p><a href='?' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>❌ 取消操作</a></p>\n";
    exit;
}

echo "<h3>正在执行更新...</h3>\n";

$success_count = 0;
$error_count = 0;

// 开始事务
$DB->query("START TRANSACTION");

try {
    foreach ($products_to_update as $product) {
        $cid = $product['cid'];
        $cleaned_name = $DB->escape($product['cleaned_name']);
        
        $update_sql = "UPDATE qingka_wangke_class SET name = '$cleaned_name' WHERE cid = '$cid'";
        
        if ($DB->query($update_sql)) {
            $success_count++;
            echo "✅ 更新成功 CID {$cid}: \"{$product['cleaned_name']}\"\n";
        } else {
            $error_count++;
            echo "❌ 更新失败 CID {$cid}: \"{$product['original_name']}\"\n";
        }
    }
    
    // 提交事务
    $DB->query("COMMIT");
    
    echo "<hr>\n";
    echo "<h2>更新完成！</h2>\n";
    echo "✅ 成功更新: {$success_count} 个商品\n";
    echo "❌ 更新失败: {$error_count} 个商品\n";
    
    if ($error_count == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 所有商品名称前缀清理完成！</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️  部分商品更新失败，请检查数据库连接</p>\n";
    }
    
} catch (Exception $e) {
    // 回滚事务
    $DB->query("ROLLBACK");
    echo "❌ 更新过程中发生错误，已回滚所有更改\n";
    echo "错误信息: " . $e->getMessage() . "\n";
}

echo "<hr>\n";
echo "<h2>第三步：验证结果</h2>\n";

// 重新查询验证结果
$verify_query = "SELECT cid, name FROM qingka_wangke_class WHERE fenlei = '$fenlei_id' ORDER BY cid LIMIT 10";
$verify_result = $DB->query($verify_query);

echo "<h3>更新后的商品名称示例（前10个）:</h3>\n";
while ($row = $DB->fetch($verify_result)) {
    echo "CID {$row['cid']}: \"{$row['name']}\"\n";
}

echo "<hr>\n";
echo "<h2>注意事项</h2>\n";
echo "<ul>\n";
echo "<li>✅ 数据库中的商品名称已更新</li>\n";
echo "<li>🔄 前端页面会立即显示新的简洁名称</li>\n";
echo "<li>📱 用户在选择项目时将看到更简洁的名称</li>\n";
echo "<li>🔍 搜索功能仍然正常工作</li>\n";
echo "<li>💾 建议定期备份数据库</li>\n";
echo "</ul>\n";

echo "<p><a href='index/price.php' target='_blank'>📋 查看商品列表页面效果</a></p>\n";

?>
