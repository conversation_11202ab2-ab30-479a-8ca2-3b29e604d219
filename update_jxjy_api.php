<?php
/**
 * 更新易教育API地址脚本
 * 将数据库中的易教育API地址从旧地址更新为新地址
 */

// 引入公共配置文件
include('confing/common.php');

echo "<h1>更新易教育API地址</h1>\n";
echo "<pre>\n";

echo "=== 易教育API地址更新 ===\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n\n";

// 旧地址和新地址
$old_url = "http://*************:9900";
$new_url = "http://**************:9900";
$new_ip = "**************";

echo "旧API地址: {$old_url}\n";
echo "新API地址: {$new_url}\n";
echo "新IP地址: {$new_ip}\n\n";

// 步骤1：检查当前配置
echo "步骤1：检查当前易教育货源配置\n";
$current_config = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");

if (!$current_config) {
    echo "❌ 未找到易教育货源配置\n";
    echo "请先在后台添加易教育货源，使用以下信息：\n";
    echo "- 名称: 易教育\n";
    echo "- 平台: jxjy\n";
    echo "- 域名: {$new_url}\n";
    echo "- 账号: 573749877\n";
    echo "- 密码: liuyaxin123.\n";
    echo "- IP: {$new_ip}\n";
    exit;
}

echo "✅ 找到易教育货源配置\n";
echo "   货源ID: {$current_config['hid']}\n";
echo "   当前名称: {$current_config['name']}\n";
echo "   当前URL: {$current_config['url']}\n";
echo "   当前IP: {$current_config['ip']}\n";
echo "   当前账号: {$current_config['user']}\n";

// 步骤2：更新配置
echo "\n步骤2：更新API地址和IP\n";

$update_sql = "UPDATE qingka_wangke_huoyuan SET 
    url = '{$new_url}',
    ip = '{$new_ip}',
    token = '',
    name = '易教育',
    pt = 'jxjy'
    WHERE hid = '{$current_config['hid']}'";

$result = $DB->query($update_sql);

if ($result) {
    echo "✅ 货源配置更新成功\n";
    
    // 验证更新结果
    $updated_config = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid = '{$current_config['hid']}'");
    echo "   更新后URL: {$updated_config['url']}\n";
    echo "   更新后IP: {$updated_config['ip']}\n";
    echo "   更新后名称: {$updated_config['name']}\n";
    echo "   更新后平台: {$updated_config['pt']}\n";
} else {
    echo "❌ 货源配置更新失败\n";
    exit;
}

// 步骤3：测试新API地址
echo "\n步骤3：测试新API地址连接\n";

$login_data = array(
    "username" => $updated_config["user"],
    "password" => $updated_config["pass"]
);

$login_url = "{$new_url}/api/login";

echo "测试登录URL: {$login_url}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$start_time = microtime(true);
$login_result = curl_exec($ch);
$end_time = microtime(true);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$request_time = round($end_time - $start_time, 2);
echo "请求耗时: {$request_time}秒\n";
echo "HTTP状态码: {$http_code}\n";

if ($curl_error) {
    echo "❌ 连接测试失败: {$curl_error}\n";
} else if ($http_code != 200) {
    echo "❌ HTTP状态码异常: {$http_code}\n";
    echo "响应内容: " . substr($login_result, 0, 200) . "\n";
} else {
    $login_result_array = json_decode($login_result, true);
    if ($login_result_array && $login_result_array["code"] == 200) {
        echo "✅ 新API地址连接成功\n";
        
        $token = $login_result_array["data"]["token"];
        echo "获取到Token: " . substr($token, 0, 30) . "...\n";
        
        // 更新Token到数据库
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$current_config['hid']}'");
        echo "Token已保存到数据库\n";
        
        // 测试用户信息接口
        echo "\n测试用户信息接口...\n";
        $info_url = "{$new_url}/api/user/info";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $info_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $info_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if (!$curl_error && $http_code == 200) {
            $info_result_array = json_decode($info_result, true);
            if ($info_result_array && $info_result_array["code"] == 200) {
                echo "✅ 用户信息获取成功\n";
                if (isset($info_result_array["data"]["balance"])) {
                    echo "账户余额: {$info_result_array["data"]["balance"]}元\n";
                }
            }
        }
        
    } else {
        echo "❌ 登录失败\n";
        echo "错误信息: " . ($login_result_array["message"] ?? "未知错误") . "\n";
    }
}

// 步骤4：完成提示
echo "\n=== 更新完成 ===\n";
echo "✅ 易教育API地址已更新为: {$new_url}\n";
echo "✅ IP地址已更新为: {$new_ip}\n";
echo "✅ Token已获取并保存\n";
echo "\n现在可以运行商品同步脚本：\n";
echo "- 调试版本: 你的域名/api/jxjy_debug.php?pricee=5\n";
echo "- 简化版本: 你的域名/api/jxjy_simple.php?pricee=5\n";
echo "- 完整版本: 你的域名/api/jxjy.php?pricee=5\n";

echo "</pre>\n";
?>
