# 8090教育一键对接教程

## 货源信息
- **货源名称**: 8090教育
- **主API地址**: http://***********:8090
- **查课API地址**: http://***********:15888
- **测试账号**: china
- **测试密码**: 168999
- **平台标识**: 8090edu
- **项目数量**: 533个网站项目


数据库：基于现有数据库结构，只需要添加必要的数据，不需要重新创建表   8090edu_optimization.sql

还有一个数据库8090教育Token管理函数
解决token容易过期的问题    8090edu_token.php
                 

## 一键对接步骤：

### 1. 商品管理——分类设置——添加——填写8090edu，其他随意
*必须填写8090edu，否则后续会出错。

### 2. xdjk.php中wkname添加
```php
"8090edu" => "8090edu"
```

### 3. 网站管理——对接设置——添加
- 名称写：8090教育
- 平台选择：8090edu
- 域名：http://***********:8090
- 账号：china
- 密码：168999

### 4. 在api文件夹中新建php文件，粘贴下面的代码
文件名随意，例如文件名为8090edu.php，则地址是：你的域名/api/8090edu.php?pricee=3
pricee是价格倍数，别写错。

```php
<?php
// 引入公共配置文件
include('../confing/common.php');

// 获取并处理 GET 参数
$pricee = trim(strip_tags(daddslashes($_GET['pricee'])));

// 查询货源信息
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE instr(pt,'8090edu') or instr(name,'8090edu')");
$hid = $a["hid"];

// 查询分类信息
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'8090edu') ORDER BY id DESC LIMIT 0, 1");
$category = $b["id"];

// 获取Token
$login_data = array(
    "username" => $a["user"],
    "password" => $a["pass"]
);

$login_url = "{$a["url"]}/api/auth/login";

// 使用curl发送JSON请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
$login_result = curl_exec($ch);
curl_close($ch);

$login_result = json_decode($login_result, true);

// 检查登录结果
if ($login_result["code"] != 200) {
    jsonReturn(1, $login_result["message"]);
}

$token = $login_result["data"]["token"];

// 获取所有网站的完整信息
$all_sites = [];
$page = 1;
$pageSize = 100;
$total_pages = 1;

echo "开始获取网站列表...\n";

do {
    $sites_url = "{$a["url"]}/api/order/websites?keyword=&page={$page}&pageSize={$pageSize}";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $sites_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $sites_result = curl_exec($ch);
    curl_close($ch);

    $sites_result = json_decode($sites_result, true);

    if ($sites_result["code"] != 200) {
        jsonReturn(1, $sites_result["message"]);
    }

    $current_sites = $sites_result["data"];
    $all_sites = array_merge($all_sites, $current_sites);

    // 计算总页数（假设每页100条，根据返回的数据量判断是否还有下一页）
    if (count($current_sites) < $pageSize) {
        break; // 如果当前页数据少于pageSize，说明已经是最后一页
    }

    $page++;
    echo "已获取第 {$page} 页数据，当前总数：" . count($all_sites) . "\n";

    // 防止无限循环，最多获取50页
    if ($page > 50) {
        break;
    }

} while (true);

echo "网站列表获取完成，共 " . count($all_sites) . " 个网站\n";

// 查询最大sort值
$max_sort_query = "SELECT MAX(sort) as max_sort FROM qingka_wangke_class";
$max_sort_result = mysqli_query($conn, $max_sort_query);
$max_sort_row = mysqli_fetch_assoc($max_sort_result);
$current_sort = $max_sort_row['max_sort'] ?? 0;

// 初始化计数器
$inserted_count = 0;
$updated_count = 0;
$ids = [];

// 遍历网站数据
foreach ($all_sites as $site) {
    $site_id = $site['site_id'];
    $ids[] = $site_id;
    $site_name = $DB->escape($site['site_name']);
    $price = $site['price'] * $pricee;
    $content = $DB->escape($site['format'] ?? '账号 密码');

    // 查询是否存在
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '$hid' AND noun = '$site_id' LIMIT 1");

    if (!$rs) {
        // 插入新记录
        $sql = "INSERT INTO qingka_wangke_class
                (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, addtime, status, fenlei)
                VALUES
                ('$current_sort', '$site_name', '$site_id', '$site_id', '$price', '$hid', '$hid', '*', '$content', '$now_time', '1', '$category')";

        $is = $DB->query($sql);
        if ($is) {
            $inserted_count++;
            $current_sort++;
        }
    } else {
        // 更新现有记录
        $sql = "UPDATE qingka_wangke_class
                SET name = '$site_name',
                    price = '$price',
                    content = '$content'
                WHERE docking = '$hid' AND noun = '$site_id'";

        $is = $DB->query($sql);
        if ($is) {
            $updated_count++;
        }
    }
}

// 下架已下架的项目
if (!empty($ids)) {
    $ids_str = implode(',', array_map(function($id) use ($DB) {
        return "'" . $DB->escape($id) . "'";
    }, $ids));
    $sql = "UPDATE qingka_wangke_class SET status = 0 WHERE docking = '$hid' AND noun NOT IN ($ids_str)";
    $DB->query($sql);
}

// 返回操作结果
echo "插入操作完成。成功上架 {$inserted_count} 条记录，更新 {$updated_count} 条记录。\n\n";
?>
```

————————————————————————————————————————————————
查课
————————————————————————————————————————————————
```php
//8090edu查课接口
if ($type == "8090edu") {
    // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
    $token = $a["token"];
    $need_refresh_token = false;

    // 检查是否有缓存的token
    if (empty($token)) {
        $need_refresh_token = true;
    } else {
        // 验证token是否有效 - 通过调用一个简单的API来测试
        $test_url = "{$a["url"]}/api/user/balance";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $test_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$test_result) {
            $need_refresh_token = true;
        } else {
            $test_result_array = json_decode($test_result, true);
            if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                $need_refresh_token = true;
            }
        }
    }

    // 如果需要刷新token，重新登录
    if ($need_refresh_token) {
        $login_data = array(
            "username" => $a["user"],
            "password" => $a["pass"]
        );

        $login_url = "{$a["url"]}/api/auth/login";

        // 使用curl发送JSON请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $login_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        $login_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            return ['code' => -1, 'msg' => "网络连接失败: " . $curl_error, 'data' => []];
        }

        $login_result = json_decode($login_result, true);

        if (!$login_result || !isset($login_result["code"]) || $login_result["code"] != 200) {
            $error_msg = isset($login_result["message"]) ? $login_result["message"] : "登录失败";
            return ['code' => -1, 'msg' => $error_msg, 'data' => []];
        }

        $token = $login_result["data"]["token"];

        // 更新数据库中的token缓存
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
    }

    // 获取网站详情
    $site_url = "{$a["url"]}/api/order/website/info?websiteId={$noun}";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $site_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $site_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($curl_error) {
        return ['code' => -1, 'msg' => "获取网站信息失败: " . $curl_error, 'data' => []];
    }

    $site_result = json_decode($site_result, true);

    if (!$site_result || !isset($site_result["code"]) || $site_result["code"] != 200) {
        $error_msg = isset($site_result["message"]) ? $site_result["message"] : "获取网站信息失败";
        return ['code' => -1, 'msg' => $error_msg, 'data' => []];
    }

    $site_name = isset($site_result["data"]["site_name"]) ? $site_result["data"]["site_name"] : "";

    if (empty($site_name)) {
        return ['code' => -1, 'msg' => '无法获取网站名称', 'data' => []];
    }

    // 查课 - 使用GET请求
    $query_url = "http://***********:15888/query";
    $query_params = array(
        "username" => $user,
        "password" => $pass,
        "courseName" => $site_name,
        "Time" => time() * 1000
    );

    $query_url_with_params = $query_url . "?" . http_build_query($query_params);

    // 使用curl发送GET请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $query_url_with_params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $query_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($curl_error) {
        return ['code' => -1, 'msg' => "查课请求失败: " . $curl_error, 'data' => []];
    }

    $query_result = json_decode($query_result, true);

    if (!$query_result || !isset($query_result["code"])) {
        return ['code' => -1, 'msg' => '查课响应格式错误', 'data' => []];
    }

    if ($query_result["code"] != 200) {
        $error_msg = isset($query_result["message"]) ? $query_result["message"] : "查课失败";
        return ['code' => -1, 'msg' => $error_msg, 'data' => []];
    }

    // 处理查课结果
    $json_data = [];
    if (isset($query_result["data"]) && is_array($query_result["data"])) {
        if (isset($query_result["data"]["children"]) && is_array($query_result["data"]["children"])) {
            // 有子项目的情况
            foreach ($query_result["data"]["children"] as $child) {
                if (isset($child["name"])) {
                    $json_data[] = ['name' => $child["name"]];
                }
            }
        } else {
            // 直接提交模式 - 返回账号密码格式
            $json_data[] = ['name' => $user . "----" . $pass];
        }
    }

    if (empty($json_data)) {
        // 如果没有课程数据，返回直接提交格式
        $json_data[] = ['name' => $user . "----" . $pass];
    }

    return ['code' => 0, 'msg' => '查询成功', 'data' => $json_data];
}
```
————————————————————————————————————————————————
下单
————————————————————————————————————————————————
```php
//8090edu下单接口
if ($type == "8090edu") {
    // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
    $token = $a["token"];
    $need_refresh_token = false;

    // 检查是否有缓存的token
    if (empty($token)) {
        $need_refresh_token = true;
    } else {
        // 验证token是否有效
        $test_url = "{$a["url"]}/api/user/balance";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $test_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$test_result) {
            $need_refresh_token = true;
        } else {
            $test_result_array = json_decode($test_result, true);
            if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                $need_refresh_token = true;
            }
        }
    }

    // 如果需要刷新token，重新登录
    if ($need_refresh_token) {
        $login_data = array(
            "username" => $a["user"],
            "password" => $a["pass"]
        );

        $login_url = "{$a["url"]}/api/auth/login";

        // 使用curl发送JSON请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $login_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        $login_result = curl_exec($ch);
        curl_close($ch);

        $login_result = json_decode($login_result, true);

        if ($login_result["code"] != 200) {
            return array("code" => -1, "msg" => $login_result["message"]);
        }

        $token = $login_result["data"]["token"];

        // 更新数据库中的token缓存
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
    }

    // 提交订单
    // 检查课程名是否为账号密码格式（用户名----密码）
    $selectedCourseKeys = array($kcname);

    // 如果课程名包含"----"，说明是直接提交模式，不需要查课
    if (strpos($kcname, '----') !== false) {
        // 对于直接提交的订单，使用空数组或特殊标识
        $selectedCourseKeys = array();
    }

    $order_data = array(
        "websiteId" => $noun,
        "accountInfo" => $user . " " . $pass,
        "selectedCourseKeys" => $selectedCourseKeys
    );

    $order_url = "{$a["url"]}/api/order/submit";

    // 使用curl发送POST请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $order_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "Authorization: {$token}",
        "Content-Type: application/json"
    ));
    $order_result = curl_exec($ch);
    curl_close($ch);

    $order_result = json_decode($order_result, true);

    if ($order_result["code"] == 200) {
        // 8090教育新版API：下单成功后直接返回订单编号
        // 检查返回数据中是否包含订单编号
        if (isset($order_result["data"]) && !empty($order_result["data"])) {
            $yid = $order_result["data"]; // 直接获取订单编号
            return array("code" => 1, "msg" => "下单成功", "yid" => $yid);
        } else {
            // 如果新API没有返回订单编号，回退到旧的查询方式
            $query_url = "{$a["url"]}/api/order/list?page=1&pageSize=1&sortField=createTime&sortOrder=descend&username=" . urlencode($user);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $query_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
            $query_result = curl_exec($ch);
            curl_close($ch);

            $query_result = json_decode($query_result, true);

            if ($query_result["code"] == 200 && isset($query_result["data"]["list"][0]["orderId"])) {
                $yid = $query_result["data"]["list"][0]["orderId"];
                return array("code" => 1, "msg" => "下单成功", "yid" => $yid);
            } else {
                return array("code" => 1, "msg" => "下单成功", "yid" => time()); // 使用时间戳作为临时ID
            }
        }
    } else {
        $error_msg = isset($order_result["message"]) ? $order_result["message"] : "下单失败";
        return array("code" => -1, "msg" => $error_msg);
    }
}
```
————————————————————————————————————————————————
进度
————————————————————————————————————————————————
```php
//8090edu进度接口
if ($type == "8090edu") {
    // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
    $token = $a["token"];
    $need_refresh_token = false;

    // 检查是否有缓存的token
    if (empty($token)) {
        $need_refresh_token = true;
    } else {
        // 验证token是否有效
        $test_url = "{$a["url"]}/api/user/balance";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $test_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$test_result) {
            $need_refresh_token = true;
        } else {
            $test_result_array = json_decode($test_result, true);
            if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                $need_refresh_token = true;
            }
        }
    }

    // 如果需要刷新token，重新登录
    if ($need_refresh_token) {
        $login_data = array(
            "username" => $a["user"],
            "password" => $a["pass"]
        );

        $login_url = "{$a["url"]}/api/auth/login";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $login_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        $login_result = curl_exec($ch);
        curl_close($ch);

        $login_result = json_decode($login_result, true);

        if ($login_result["code"] != 200) {
            return array(array("code" => -1, "msg" => $login_result["message"]));
        }

        $token = $login_result["data"]["token"];

        // 更新数据库中的token缓存
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
    }

    // 查询订单进度
    $query_url = "{$a["url"]}/api/order/list?page=1&pageSize=50&sortField=createTime&sortOrder=descend";

    // 如果有用户名，添加筛选条件
    if (!empty($user)) {
        $query_url .= "&username=" . urlencode($user);
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $query_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $query_result = curl_exec($ch);
    curl_close($ch);

    $query_result = json_decode($query_result, true);

    $b = [];

    if ($query_result["code"] == 200 && isset($query_result["data"]["list"])) {
        foreach ($query_result["data"]["list"] as $order) {
            $orderId = $order["orderId"];
            $username = $order["username"];
            $password = $order["password"];
            $courseName = isset($order["courseName"]) ? $order["courseName"] : $order["courseInfo"];
            $status = $order["status"];
            $courseInfo = isset($order["courseInfo"]) ? $order["courseInfo"] : "";

            // 状态映射
            $process = 0;
            switch ($status) {
                case "已完成":
                    $process = 100;
                    break;
                case "已退款":
                    $process = 0;
                    break;
                case "进行中":
                    if (preg_match('/(\d+(?:\.\d+)?)%/', $courseInfo, $matches)) {
                        $process = floatval($matches[1]);
                    } else {
                        $process = 50;
                    }
                    break;
                case "待处理":
                case "队列中":
                    $process = 10;
                    break;
                case "异常":
                    $process = 0;
                    break;
                default:
                    $process = 20;
                    break;
            }

            $b[] = array(
                "code" => 1,
                "msg" => "查询成功",
                "yid" => $orderId,
                "kcname" => $courseName,
                "user" => $username,
                "pass" => $password,
                "status_text" => $status,
                "process" => $process . "%",
                "remarks" => $courseInfo
            );
        }
    } else {
        $b[] = array("code" => -1, "msg" => "暂无订单数据");
    }

    return $b;
}
```
————————————————————————————————————————————————
补刷
————————————————————————————————————————————————
```php
//8090edu补刷接口
if ($type == "8090edu") {
    // 检查Token是否存在
    if (empty($token)) {
        return array("code" => -1, "msg" => "8090教育Token未配置");
    }

    // 构建补刷请求数据
    $refresh_data = array(
        "orderId" => intval($yid),
        "status" => "队列中",
        "reason" => ""
    );

    // 8090教育补刷API地址
    $refresh_url = $a["url"] . "/api/order/status/update";

    // 设置请求头
    $headers = array(
        "Authorization: Bearer " . $token,
        "Content-Type: application/json",
        "Accept: application/json"
    );

    // 发送补刷请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $refresh_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($refresh_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // 处理请求错误
    if ($curl_error) {
        return array("code" => -1, "msg" => "网络请求失败: " . $curl_error);
    }

    // 处理HTTP错误
    if ($http_code !== 200) {
        return array("code" => -1, "msg" => "HTTP请求失败，状态码: " . $http_code);
    }

    // 解析响应
    $result = json_decode($response, true);

    if (!$result) {
        return array("code" => -1, "msg" => "响应解析失败");
    }

    // 检查API响应
    if (isset($result["code"]) && $result["code"] == 200 && isset($result["state"]) && $result["state"] === true) {
        return array("code" => 1, "msg" => "8090教育补刷成功，订单已重新进入队列");
    } else {
        $error_msg = isset($result["message"]) ? $result["message"] : "未知错误";
        return array("code" => -1, "msg" => "8090教育补刷失败: " . $error_msg);
    }
}
```
————————————————————————————————————————————————
修改密码
————————————————————————————————————————————————
```php
//8090edu修改密码接口
if ($type == "8090edu") {
    // 8090教育不支持修改密码功能
    return array("code" => -1, "msg" => "8090教育平台不支持修改密码功能");
}
```

## 注意事项

### 1. Token管理
- 8090教育使用JWT Token认证，token会自动缓存在数据库中
- 系统会自动检测token是否过期，过期时自动刷新
- 无需手动管理token

### 2. 双API架构
- 主API：http://***********:8090 - 负责登录、网站列表、下单、订单查询
- 查课API：http://***********:15888 - 专门负责课程查询

### 3. 直接提交模式
- 某些网站不需要查课，可以直接提交
- 系统会自动识别并处理直接提交模式
- 直接提交的课程名格式为：账号----密码

### 4. 功能支持情况
- ✅ 查课功能：完全支持
- ✅ 下单功能：完全支持
- ✅ 进度查询：完全支持
- ✅ 补刷功能：完全支持
- ❌ 修改密码：不支持（8090教育无此API）

### 5. 错误处理
- 所有接口都有完整的错误处理机制
- 网络异常时会自动重试
- Token失效时会自动刷新
- 详细的错误信息提示

## 测试建议

1. **先测试API同步**：访问 你的域名/api/8090edu.php?pricee=3
2. **测试查课功能**：选择一个项目进行查课测试
3. **测试下单功能**：提交一个测试订单
4. **测试进度查询**：查看订单进度是否正常更新
5. **测试补刷功能**：对异常订单进行补刷测试

## 完成！

按照以上步骤操作，8090教育就能完全对接到您的系统中了。如有问题，请检查：
- 分类名称是否为8090edu
- 平台标识是否为8090edu
- API地址和账号密码是否正确
- 网络连接是否正常
