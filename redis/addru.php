<?php
include('../confing/common.php');
$redis = new Redis();
$redis->connect("127.0.0.1", "6379");
$redis->select(10);

echo "连通redis： " . $redis->ping() . "\r\n";
$lenth = $redis->LLEN('addoid');
if ($lenth == 0) {
    $redis->del('addoid');
    $i = 0;
    $stmt = $DB->prepare_query("SELECT `oid` FROM `qingka_wangke_order` WHERE `dockstatus`=0 ORDER BY `oid` DESC");
    if ($stmt !== false) {
        $result = $stmt->get_result();
        while ($b = $result->fetch_assoc()) {
            $updateStmt = $DB->prepare_query(
                "UPDATE qingka_wangke_order SET dockstatus = 5 WHERE oid = ? AND dockstatus = 0",
                [$b['oid']]
            );
            if ($updateStmt !== false) {
                $redis->lPush("addoid", $b['oid']);
                $i++;
                $updateStmt->close();
            }
        }
        $stmt->close();
        echo "入队成功！本次入队订单共计：" . $i . "条\r\n";
    }
} else {
    echo "入队失败！队列池还有：" . $redis->LLEN('addoid') . "条订单正在执行\r\n";
}
?>