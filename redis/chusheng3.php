<?php
include(dirname(__FILE__) . '/../confing/common.php');
include(dirname(__FILE__) . '/../Checkorder/jdjk.php'); // 包含进度查询函数
$redis = new Redis();
$redis->connect("127.0.0.1", "6379");

// 定义多个队列池和对应的 Redis 分区
$queuePools = array(
    'plztoid' => 0,
    'oidsjxz' => 8,
    'oidstdks' => 10
);

$yqsl = 150; // 单进程单次最大获取订单数
$processId = getenv('SUPERVISOR_PROCESS_NAME'); // 获取当前进程ID

while (true) {
    $foundOrder = false; // 标记是否找到订单

    foreach ($queuePools as $queue => $partition) {
        $redis->select($partition);

        $oids = array();
        $totalOrders = 0;
        $queueLength = $redis->llen($queue);

        if ($queueLength >= 30) {
            $yqslyqsl = ($queueLength < $yqsl) ? min(intval($queueLength / 2), $yqsl) : $yqsl;
        } else {
            $yqslyqsl = $queueLength;
        }

        for ($i = 0; $i < $yqslyqsl; $i++) {
            $oid = $redis->lpop($queue);
            if ($oid != '') {
                $oids[] = $oid;
                $foundOrder = true; // 找到订单时设置为true
            } else {
                break;
            }
        }

        $oidcount = count($oids);
        $totalOrders += $oidcount;

        if ($oidcount != 0) {
            echo "当前进程: " . $processId . " 成功获取 " . $totalOrders . " 个订单\n";
            echo "----------------------------------------------\n";

            if (!empty($oids)) {
                foreach ($oids as $oid) {
                    $result = processCx($oid);

                    $updateSuccess = false;

                    foreach ($result as $item) {
                        if ($item['code'] == 1) {
                            // 使用简单的UPDATE语句，确保兼容性
                            $escapedName = $DB->escape($item['name']);
                            $escapedYid = $DB->escape($item['yid']);
                            $escapedStatus = $DB->escape($item['status_text']);
                            $escapedCourseStartTime = $DB->escape($item['kcks']);
                            $escapedCourseEndTime = $DB->escape($item['kcjs']);
                            $escapedExamStartTime = $DB->escape($item['ksks']);
                            $escapedExamEndTime = $DB->escape($item['ksjs']);
                            $escapedProcess = $DB->escape($item['process']);
                            $escapedRemarks = $DB->escape($item['remarks']);
                            $escapedOid = $DB->escape($oid);

                            // 🔥 关键修复：保护yid字段，避免被异常值覆盖
                            // 获取当前订单的yid
                            $current_order = $DB->get_row("SELECT yid FROM qingka_wangke_order WHERE oid='$escapedOid'");
                            $current_yid = $current_order ? $current_order['yid'] : '';

                            // 只有在以下情况才更新yid：
                            // 1. 当前yid为空或为"1"
                            // 2. 且返回的yid不为空且不为"1"
                            $should_update_yid = false;
                            if ((empty($current_yid) || $current_yid == '1') &&
                                !empty($item['yid']) && $item['yid'] != '1') {
                                $should_update_yid = true;
                            }

                            if ($should_update_yid) {
                                // 更新包括yid在内的所有字段
                                $updateSuccess = $DB->query("UPDATE qingka_wangke_order SET
                                    `name`='$escapedName',
                                    `yid`='$escapedYid',
                                    `status`='$escapedStatus',
                                    `courseStartTime`='$escapedCourseStartTime',
                                    `courseEndTime`='$escapedCourseEndTime',
                                    `examStartTime`='$escapedExamStartTime',
                                    `examEndTime`='$escapedExamEndTime',
                                    `process`='$escapedProcess',
                                    `remarks`='$escapedRemarks'
                                    WHERE `oid`='$escapedOid'");
                            } else {
                                // 不更新yid，保持原有订单号
                                $updateSuccess = $DB->query("UPDATE qingka_wangke_order SET
                                    `name`='$escapedName',
                                    `status`='$escapedStatus',
                                    `courseStartTime`='$escapedCourseStartTime',
                                    `courseEndTime`='$escapedCourseEndTime',
                                    `examStartTime`='$escapedExamStartTime',
                                    `examEndTime`='$escapedExamEndTime',
                                    `process`='$escapedProcess',
                                    `remarks`='$escapedRemarks'
                                    WHERE `oid`='$escapedOid'");
                            }
                        }
                    }
                    if ($updateSuccess) {
                        $today_day = date("Y-m-d H:i:s");
                        echo "订单 $oid 已更新完成！\n";
                        echo "当前进程: " . $processId . "剩余 " . (--$totalOrders) . " 个订单。\n";
                        echo "更新时间：" . $today_day . "\n";
                        echo "----------------------------------------------\n";
                        sleep(1);
                    }
                    if (!$updateSuccess) {
                        $DB->query("UPDATE qingka_wangke_order SET
                            `process`='获取失败'
                            WHERE
                            `oid`='$oid'");
                        echo "订单 $oid 更新失败\n";
                        echo "----------------------------------------------\n";
                        $totalOrders--;
                    }
                }
            }
        }
    }

    if (!$foundOrder) {
        sleep(10);
    }

    $randomSleepTime = mt_rand(5, 10);
    sleep($randomSleepTime);
}
?>