[2025-08-23 02:17:59] === 自动退款脚本开始执行 ===
[2025-08-23 02:17:59] 开始处理：账号密码错误订单
[2025-08-23 02:17:59] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:17:59] 开始处理：对接失败订单
[2025-08-23 02:17:59] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:17:59] 开始处理：上游已退款订单
[2025-08-23 02:17:59] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:17:59] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:17:59] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:17:59] 开始处理：长时间未处理订单
[2025-08-23 02:17:59] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:17:59] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:17:59] === 自动退款脚本执行完成 ===
[2025-08-23 02:20:01] === 自动退款脚本开始执行 ===
[2025-08-23 02:20:01] === 自动退款脚本开始执行 ===
[2025-08-23 02:20:01] 开始处理：账号密码错误订单
[2025-08-23 02:20:01] 开始处理：账号密码错误订单
[2025-08-23 02:20:01] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:20:01] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:20:01] 开始处理：对接失败订单
[2025-08-23 02:20:01] 开始处理：对接失败订单
[2025-08-23 02:20:01] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:20:01] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:20:01] 开始处理：上游已退款订单
[2025-08-23 02:20:01] 开始处理：上游已退款订单
[2025-08-23 02:20:01] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:20:01] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:20:01] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:20:01] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:20:01] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:20:01] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:20:01] 开始处理：长时间未处理订单
[2025-08-23 02:20:01] 开始处理：长时间未处理订单
[2025-08-23 02:20:01] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:20:01] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:20:01] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:20:01] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:20:01] === 自动退款脚本执行完成 ===
[2025-08-23 02:20:01] === 自动退款脚本执行完成 ===
[2025-08-23 02:22:49] === 自动退款脚本开始执行 ===
[2025-08-23 02:22:49] 开始处理：账号密码错误订单
[2025-08-23 02:22:49] ✅ 订单2147483775自动退款成功，金额：1.00 (用户：test_user)
[2025-08-23 02:22:49] ✅ 账号密码错误订单处理完成：退款1个订单，金额1
[2025-08-23 02:22:49] 开始处理：对接失败订单
[2025-08-23 02:22:49] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:22:49] 开始处理：上游已退款订单
[2025-08-23 02:22:49] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:22:49] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:22:49] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:22:49] 开始处理：长时间未处理订单
[2025-08-23 02:22:49] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:22:49] 🎉 自动退款完成：共退款1个订单，总金额1
[2025-08-23 02:22:49] === 自动退款脚本执行完成 ===
[2025-08-23 02:25:01] === 自动退款脚本开始执行 ===
[2025-08-23 02:25:01] === 自动退款脚本开始执行 ===
[2025-08-23 02:25:01] 开始处理：账号密码错误订单
[2025-08-23 02:25:01] 开始处理：账号密码错误订单
[2025-08-23 02:25:01] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:25:01] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:25:01] 开始处理：对接失败订单
[2025-08-23 02:25:01] 开始处理：对接失败订单
[2025-08-23 02:25:01] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:25:01] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:25:01] 开始处理：上游已退款订单
[2025-08-23 02:25:01] 开始处理：上游已退款订单
[2025-08-23 02:25:01] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:25:01] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:25:01] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:25:01] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:25:01] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:25:01] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:25:01] 开始处理：长时间未处理订单
[2025-08-23 02:25:01] 开始处理：长时间未处理订单
[2025-08-23 02:25:01] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:25:01] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:25:01] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:25:01] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:25:01] === 自动退款脚本执行完成 ===
[2025-08-23 02:25:01] === 自动退款脚本执行完成 ===
[2025-08-23 02:29:32] === 自动退款脚本开始执行 ===
[2025-08-23 02:29:32] 开始处理：账号密码错误订单
[2025-08-23 02:29:32] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:29:32] 开始处理：对接失败订单
[2025-08-23 02:29:32] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:29:32] 开始处理：上游已退款订单
[2025-08-23 02:29:32] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:29:32] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:29:32] ⚠️ 订单2147483774退款失败：订单太新，不符合退款条件
[2025-08-23 02:29:32] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:29:32] 开始处理：长时间未处理订单
[2025-08-23 02:29:32] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:29:32] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:29:32] === 自动退款脚本执行完成 ===
[2025-08-23 02:30:01] === 自动退款脚本开始执行 ===
[2025-08-23 02:30:01] 开始处理：账号密码错误订单
[2025-08-23 02:30:01] ℹ️ 账号密码错误订单：无需退款的订单
[2025-08-23 02:30:01] 开始处理：对接失败订单
[2025-08-23 02:30:01] ℹ️ 对接失败订单：无需退款的订单
[2025-08-23 02:30:01] 开始处理：上游已退款订单
[2025-08-23 02:30:01] ⚠️ 订单2147483744退款失败：订单已退款，跳过
[2025-08-23 02:30:01] ⚠️ 订单2147483746退款失败：订单已退款，跳过
[2025-08-23 02:30:01] ⚠️ 订单2147483774退款失败：订单太新，不符合退款条件
[2025-08-23 02:30:01] ℹ️ 上游已退款订单：无需退款的订单
[2025-08-23 02:30:01] 开始处理：长时间未处理订单
[2025-08-23 02:30:01] ℹ️ 长时间未处理订单：无需退款的订单
[2025-08-23 02:30:01] ℹ️ 本次执行无需退款的订单
[2025-08-23 02:30:01] === 自动退款脚本执行完成 ===
