<?php
/**
 * 自动退款配置文件
 * 定义退款规则、安全限制和监控参数
 */

if (!defined('IN_CRONLITE')) {
    exit('Access Denied');
}

// 自动退款配置
$refund_config = array(
    // 基本设置
    'enabled' => true,                    // 是否启用自动退款
    'max_refunds_per_run' => 50,         // 单次执行最大退款数量
    'max_amount_per_run' => 1000,        // 单次执行最大退款金额
    'min_order_age_minutes' => 30,       // 订单最小年龄（分钟）才能退款
    'max_order_age_days' => 30,          // 订单最大年龄（天）仍可退款
    
    // 安全限制
    'min_refund_amount' => 0.01,         // 最小退款金额
    'max_refund_amount' => 100,          // 最大单笔退款金额
    'excluded_users' => array(),         // 排除的用户ID列表
    'excluded_platforms' => array(),     // 排除的平台列表
    
    // 监控和报警
    'alert_threshold' => 20,             // 单次退款数量超过此值时报警
    'alert_amount_threshold' => 500,     // 单次退款金额超过此值时报警
    'log_retention_days' => 30,          // 日志保留天数
    
    // 退款条件配置
    'conditions' => array(
        'password_error' => array(
            'enabled' => true,
            'wait_minutes' => 30,        // 等待30分钟后退款
            'keywords' => array('账号密码错误', '请检查账号', '请检查密码', '请检查账号密码'),
            'max_per_run' => 20
        ),
        'dock_failed' => array(
            'enabled' => true,
            'wait_hours' => 1,           // 等待1小时后退款
            'max_per_run' => 10
        ),
        'upstream_refunded' => array(
            'enabled' => true,
            'wait_minutes' => 5,         // 等待5分钟后退款
            'max_per_run' => 15
        ),
        'long_pending' => array(
            'enabled' => true,
            'wait_hours' => 24,          // 等待24小时后退款
            'max_per_run' => 5
        )
    )
);

// 获取配置值的辅助函数
function getRefundConfig($key, $default = null) {
    global $refund_config;
    
    $keys = explode('.', $key);
    $value = $refund_config;
    
    foreach ($keys as $k) {
        if (isset($value[$k])) {
            $value = $value[$k];
        } else {
            return $default;
        }
    }
    
    return $value;
}

// 验证退款条件的函数
function validateRefundConditions($order, $condition_type = '') {
    global $refund_config;

    // 检查是否启用自动退款
    if (!$refund_config['enabled']) {
        return array('valid' => false, 'reason' => '自动退款已禁用');
    }

    // 检查用户是否在排除列表中
    if (in_array($order['uid'], $refund_config['excluded_users'])) {
        return array('valid' => false, 'reason' => '用户在排除列表中');
    }

    // 检查退款金额是否在允许范围内
    if ($order['fees'] < $refund_config['min_refund_amount']) {
        return array('valid' => false, 'reason' => '退款金额过小');
    }

    if ($order['fees'] > $refund_config['max_refund_amount']) {
        return array('valid' => false, 'reason' => '退款金额过大');
    }

    // 检查订单年龄 - 上游已退款订单有特殊规则
    $order_age_minutes = (time() - strtotime($order['addtime'])) / 60;
    $order_age_days = $order_age_minutes / (60 * 24);

    // 上游已退款订单的特殊处理
    if ($condition_type === 'upstream_refunded' || $order['status'] === '已退款') {
        $min_age = getRefundConfig('conditions.upstream_refunded.wait_minutes', 5);
        if ($order_age_minutes < $min_age) {
            return array('valid' => false, 'reason' => "上游退款订单需等待{$min_age}分钟");
        }
    } else {
        // 其他类型订单使用全局最小年龄限制
        if ($order_age_minutes < $refund_config['min_order_age_minutes']) {
            return array('valid' => false, 'reason' => '订单太新，不符合退款条件');
        }
    }

    // 检查最大年龄限制
    if ($order_age_days > $refund_config['max_order_age_days']) {
        return array('valid' => false, 'reason' => '订单太旧，超过退款期限');
    }

    return array('valid' => true, 'reason' => '验证通过');
}

// 检查是否需要报警
function checkAlertThreshold($refund_count, $refund_amount) {
    global $refund_config;
    
    $alerts = array();
    
    if ($refund_count >= $refund_config['alert_threshold']) {
        $alerts[] = "退款数量({$refund_count})超过阈值({$refund_config['alert_threshold']})";
    }
    
    if ($refund_amount >= $refund_config['alert_amount_threshold']) {
        $alerts[] = "退款金额({$refund_amount})超过阈值({$refund_config['alert_amount_threshold']})";
    }
    
    return $alerts;
}

// 记录退款统计
function recordRefundStats($condition_name, $count, $amount) {
    global $DB;
    
    $date = date('Y-m-d');
    $hour = date('H');
    
    // 检查是否已有记录
    $existing = $DB->get_row("
        SELECT * FROM qingka_wangke_refund_stats 
        WHERE date = '{$date}' AND hour = '{$hour}' AND condition_name = '{$condition_name}'
    ");
    
    if ($existing) {
        // 更新现有记录
        $DB->query("
            UPDATE qingka_wangke_refund_stats 
            SET count = count + {$count}, amount = amount + {$amount}
            WHERE id = {$existing['id']}
        ");
    } else {
        // 创建新记录
        $DB->query("
            INSERT INTO qingka_wangke_refund_stats (date, hour, condition_name, count, amount)
            VALUES ('{$date}', '{$hour}', '{$condition_name}', {$count}, {$amount})
        ");
    }
}

// 创建退款统计表（如果不存在）
function createRefundStatsTable() {
    global $DB;
    
    $sql = "
        CREATE TABLE IF NOT EXISTS `qingka_wangke_refund_stats` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `date` date NOT NULL,
            `hour` tinyint(2) NOT NULL,
            `condition_name` varchar(50) NOT NULL,
            `count` int(11) NOT NULL DEFAULT 0,
            `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `date_hour` (`date`, `hour`),
            KEY `condition_name` (`condition_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='自动退款统计表';
    ";
    
    $DB->query($sql);
}

// 清理过期日志
function cleanupOldLogs() {
    global $refund_config;
    
    $log_file = dirname(__FILE__) . '/auto_refund.log';
    $retention_days = $refund_config['log_retention_days'];
    
    if (file_exists($log_file)) {
        $file_age_days = (time() - filemtime($log_file)) / (60 * 60 * 24);
        
        if ($file_age_days > $retention_days) {
            // 备份旧日志
            $backup_file = $log_file . '.' . date('Y-m-d', filemtime($log_file));
            if (!file_exists($backup_file)) {
                copy($log_file, $backup_file);
            }
            
            // 清空当前日志
            file_put_contents($log_file, '');
        }
    }
}

?>
