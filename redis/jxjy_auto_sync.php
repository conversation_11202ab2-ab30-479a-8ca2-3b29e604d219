<?php
/**
 * 易教育专用自动同步脚本
 * 每2分钟执行一次，确保易教育订单像8090教育和yyy教育一样自动同步
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

$log_file = dirname(__FILE__) . '/jxjy_auto_sync.log';
$max_log_size = 10 * 1024 * 1024; // 10MB

// 日志函数
function writeLog($message) {
    global $log_file, $max_log_size;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}\n";
    
    // 检查日志文件大小，如果超过限制则清空
    if (file_exists($log_file) && filesize($log_file) > $max_log_size) {
        file_put_contents($log_file, '');
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    echo $log_message;
}

writeLog("=== 易教育自动同步脚本开始执行 ===");

try {
    // 连接Redis
    $redis = new Redis();
    $redis->connect("127.0.0.1", "6379");
    $redis->select(7); // 选择数据库7
    
    $rediscode = $redis->ping();
    if ($rediscode != true) {
        writeLog("❌ Redis连接失败");
        exit(1);
    }
    
    writeLog("✅ Redis连接成功");
    
    // 检查当前队列长度
    $current_queue_length = $redis->llen("oidsydcl");
    writeLog("当前进度同步队列长度: {$current_queue_length}");
    
    // 如果队列中已经有很多订单，就不再添加新的
    if ($current_queue_length > 150) {
        writeLog("⚠️ 进度同步队列中订单过多({$current_queue_length})，跳过本次易教育同步");
        exit(0);
    }
    
    // 查询需要同步的易教育订单
    $sql = "
        SELECT o.oid, o.user, o.kcname, o.yid, o.status, o.process, o.addtime, o.hid,
               TIMESTAMPDIFF(MINUTE, o.addtime, NOW()) as minutes_old,
               h.pt as platform_type, h.name as platform_name
        FROM qingka_wangke_order o
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid
        WHERE h.pt = 'jxjy'
        AND o.dockstatus = 1
        AND o.addtime > DATE_SUB(NOW(), INTERVAL 48 HOUR)  -- 48小时内的订单
        AND o.status NOT IN ('已完成', '已退款', '已取消')  -- 排除终态订单
        AND (o.status = '补刷中' OR (o.process NOT LIKE '%100%' AND o.process NOT LIKE '%已完成%' AND o.process NOT LIKE '%学习完成%'))  -- 排除已完成进度，除非是补刷
        AND (
            -- 新订单（5分钟内）：立即处理
            (o.addtime > DATE_SUB(NOW(), INTERVAL 5 MINUTE))
            OR
            -- 补刷中的订单：等待2分钟后开始同步
            (o.status = '补刷中' AND o.addtime <= DATE_SUB(NOW(), INTERVAL 2 MINUTE))
            OR
            -- 需要进度更新的订单
            (
                o.addtime <= DATE_SUB(NOW(), INTERVAL 3 MINUTE)
                AND (
                    o.process = '获取失败'
                    OR o.process = 'chusheng-获取失败'
                    OR o.process = '补刷重置中'
                    OR o.process IS NULL
                    OR o.process = ''
                    OR o.process = '0%'
                    OR (o.process NOT LIKE '%100%' AND o.process NOT LIKE '%已完成%' AND o.process NOT LIKE '%学习完成%')
                )
            )
        )
        ORDER BY 
            CASE 
                WHEN o.addtime > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 0  -- 新订单最优先
                WHEN o.status = '补刷中' THEN 1  -- 补刷订单次优先
                WHEN o.process IN ('获取失败', 'chusheng-获取失败', '补刷重置中') THEN 2  -- 失败订单
                ELSE 3
            END,
            o.addtime DESC
        LIMIT 20
    ";
    
    $result = $DB->query($sql);
    
    if (!$result) {
        writeLog("❌ 数据库查询失败");
        exit(1);
    }
    
    $orders_to_enqueue = [];
    $total_orders = 0;
    
    while ($row = $result->fetch_assoc()) {
        $total_orders++;
        
        // 检查订单是否已经在队列中
        $oid = $row['oid'];
        $queue_items = $redis->lrange("oidsydcl", 0, -1);
        
        if (!in_array($oid, $queue_items)) {
            $orders_to_enqueue[] = $oid;
            
            $status_desc = $row['status'];
            $process_desc = $row['process'] ?: '无进度';
            $age_desc = $row['minutes_old'] . '分钟前';
            
            writeLog("准备易教育同步: 订单{$oid} - {$row['user']} - {$status_desc} - {$process_desc} ({$age_desc})");
        } else {
            writeLog("跳过: 易教育订单{$oid}已在队列中");
        }
    }
    
    writeLog("扫描到{$total_orders}个易教育订单，其中" . count($orders_to_enqueue) . "个需要同步");
    
    // 将订单加入进度同步队列
    $enqueued_count = 0;
    foreach ($orders_to_enqueue as $oid) {
        $redis->lPush("oidsydcl", $oid);
        $enqueued_count++;
    }
    
    if ($enqueued_count > 0) {
        writeLog("✅ 成功将{$enqueued_count}个易教育订单加入同步队列");
    } else {
        writeLog("ℹ️ 没有易教育订单需要同步");
    }
    
    // 检查守护进程状态（通过队列处理活动判断）
    // 由于shell_exec被禁用，我们通过队列处理效果来判断守护进程是否工作

    // 检查队列处理效率
    $queue_processing_ok = true;
    if ($final_queue_length > 100) {
        $queue_processing_ok = false;
        writeLog("⚠️ 队列积压严重({$final_queue_length})，可能守护进程有问题");
    } elseif ($final_queue_length > 50) {
        writeLog("⚠️ 队列有些积压({$final_queue_length})，但仍在可接受范围");
    } else {
        writeLog("✅ 队列长度正常({$final_queue_length})，守护进程工作正常");
    }

    // 检查最近的订单处理活动
    $recent_updates = $DB->get_row("
        SELECT COUNT(*) as count
        FROM qingka_wangke_order
        WHERE uptime > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    ");

    $recent_count = $recent_updates ? $recent_updates['count'] : 0;
    if ($recent_count > 0) {
        writeLog("✅ 最近10分钟有{$recent_count}个订单被处理，守护进程工作正常");
    } else {
        writeLog("ℹ️ 最近10分钟没有订单处理活动（可能是正常的低峰期）");
    }
    
    // 显示最终队列状态
    $final_queue_length = $redis->llen("oidsydcl");
    writeLog("最终进度同步队列长度: {$final_queue_length}");
    
    // 统计易教育订单状态
    $jxjy_stats_sql = "
        SELECT 
            o.status,
            COUNT(*) as count
        FROM qingka_wangke_order o
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid
        WHERE h.pt = 'jxjy'
        AND o.addtime > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY o.status
        ORDER BY count DESC
    ";
    
    $stats_result = $DB->query($jxjy_stats_sql);
    if ($stats_result) {
        writeLog("易教育24小时订单状态统计:");
        while ($stat = $stats_result->fetch_assoc()) {
            writeLog("  {$stat['status']}: {$stat['count']} 个");
        }
    }
    
    writeLog("=== 易教育自动同步脚本执行完成 ===");
    
} catch (Exception $e) {
    writeLog("❌ 异常: " . $e->getMessage());
    writeLog("文件: " . $e->getFile());
    writeLog("行号: " . $e->getLine());
    exit(1);
}

?>
