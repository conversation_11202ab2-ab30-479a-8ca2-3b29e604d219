<?php
/**
 * 宝塔面板专用监控脚本
 * 提供简洁的状态报告，适合宝塔面板查看
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');
include(dirname(__FILE__) . '/refund_config.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

echo "=== 自动退款系统状态报告 ===\n";
echo "时间：" . date('Y-m-d H:i:s') . "\n\n";

// 1. 配置状态
echo "【配置状态】\n";
echo "自动退款：" . (getRefundConfig('enabled') ? '✅ 启用' : '❌ 禁用') . "\n";
echo "单次最大退款数：" . getRefundConfig('max_refunds_per_run') . " 笔\n";
echo "单次最大金额：" . getRefundConfig('max_amount_per_run') . " 元\n";
echo "最小订单年龄：" . getRefundConfig('min_order_age_minutes') . " 分钟\n\n";

// 2. 脚本运行状态
echo "【脚本状态】\n";
$bt_log_file = dirname(__FILE__) . '/auto_refund_bt.log';
if (file_exists($bt_log_file)) {
    $last_modified = filemtime($bt_log_file);
    $minutes_ago = (time() - $last_modified) / 60;
    
    if ($minutes_ago <= 10) {
        echo "运行状态：✅ 正常运行\n";
    } else {
        echo "运行状态：⚠️ 可能异常 (上次运行：" . round($minutes_ago) . "分钟前)\n";
    }
    echo "最后运行：" . date('Y-m-d H:i:s', $last_modified) . "\n";
    echo "日志文件：auto_refund_bt.log\n";
} else {
    echo "运行状态：❓ 未知 (无日志文件)\n";
}
echo "\n";

// 3. 今日退款统计
echo "【今日统计】\n";
$today = date('Y-m-d');
$today_stats = $DB->query("
    SELECT 
        condition_name,
        SUM(count) as count,
        SUM(amount) as amount
    FROM qingka_wangke_refund_stats 
    WHERE date = '{$today}'
    GROUP BY condition_name
    ORDER BY amount DESC
");

$total_today_count = 0;
$total_today_amount = 0;

if ($today_stats && $today_stats->num_rows > 0) {
    while ($row = $today_stats->fetch_assoc()) {
        echo "{$row['condition_name']}：{$row['count']} 笔，{$row['amount']} 元\n";
        $total_today_count += $row['count'];
        $total_today_amount += $row['amount'];
    }
    echo "今日总计：{$total_today_count} 笔，{$total_today_amount} 元\n";
} else {
    echo "今日暂无退款记录\n";
}
echo "\n";

// 4. 待退款订单统计
echo "【待退款订单】\n";

// 账号密码错误订单
$password_error = $DB->get_row("
    SELECT COUNT(*) as count, SUM(fees) as amount
    FROM qingka_wangke_order 
    WHERE (
        kcname = '账号密码错误' 
        OR status LIKE '%请检查账号%' 
        OR status LIKE '%请检查密码%' 
        OR status LIKE '%账号密码%'
    )
    AND dockstatus = 2
    AND status != '已退款'
    AND dockstatus != '4'
    AND fees > 0
    AND addtime < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
");

// 对接失败订单
$dock_failed = $DB->get_row("
    SELECT COUNT(*) as count, SUM(fees) as amount
    FROM qingka_wangke_order 
    WHERE dockstatus = 2
    AND status != '已退款'
    AND status NOT LIKE '%请检查%'
    AND kcname != '账号密码错误'
    AND fees > 0
    AND addtime < DATE_SUB(NOW(), INTERVAL 1 HOUR)
");

// 上游已退款订单
$upstream_refunded = $DB->get_row("
    SELECT COUNT(*) as count, SUM(fees) as amount
    FROM qingka_wangke_order 
    WHERE status = '已退款'
    AND dockstatus != '4'
    AND fees > 0
");

// 长时间未处理订单
$long_pending = $DB->get_row("
    SELECT COUNT(*) as count, SUM(fees) as amount
    FROM qingka_wangke_order 
    WHERE dockstatus = 2
    AND status IN ('待处理', '待更新')
    AND fees > 0
    AND addtime < DATE_SUB(NOW(), INTERVAL 24 HOUR)
");

$total_pending_count = 0;
$total_pending_amount = 0;

if ($password_error && $password_error['count'] > 0) {
    echo "账号密码错误：{$password_error['count']} 笔，{$password_error['amount']} 元\n";
    $total_pending_count += $password_error['count'];
    $total_pending_amount += $password_error['amount'];
}

if ($dock_failed && $dock_failed['count'] > 0) {
    echo "对接失败：{$dock_failed['count']} 笔，{$dock_failed['amount']} 元\n";
    $total_pending_count += $dock_failed['count'];
    $total_pending_amount += $dock_failed['amount'];
}

if ($upstream_refunded && $upstream_refunded['count'] > 0) {
    echo "上游已退款：{$upstream_refunded['count']} 笔，{$upstream_refunded['amount']} 元\n";
    $total_pending_count += $upstream_refunded['count'];
    $total_pending_amount += $upstream_refunded['amount'];
}

if ($long_pending && $long_pending['count'] > 0) {
    echo "长时间未处理：{$long_pending['count']} 笔，{$long_pending['amount']} 元\n";
    $total_pending_count += $long_pending['count'];
    $total_pending_amount += $long_pending['amount'];
}

if ($total_pending_count > 0) {
    echo "待退款总计：{$total_pending_count} 笔，{$total_pending_amount} 元\n";
    
    if ($total_pending_count > 10) {
        echo "⚠️ 警告：待退款订单较多，建议检查系统状态\n";
    }
} else {
    echo "✅ 暂无待退款订单\n";
}
echo "\n";

// 5. 最近退款记录
echo "【最近退款记录】\n";
$recent_refunds = $DB->query("
    SELECT oid, uid, amount, reason, created_at
    FROM qingka_wangke_refund_log 
    WHERE type = 'auto'
    ORDER BY created_at DESC 
    LIMIT 5
");

if ($recent_refunds && $recent_refunds->num_rows > 0) {
    while ($row = $recent_refunds->fetch_assoc()) {
        echo "订单{$row['oid']} - 用户{$row['uid']} - {$row['amount']}元 - {$row['reason']} - {$row['created_at']}\n";
    }
} else {
    echo "暂无最近退款记录\n";
}
echo "\n";

// 6. 系统健康检查
echo "【系统健康检查】\n";

// 检查数据库连接
try {
    $DB->query("SELECT 1");
    echo "数据库连接：✅ 正常\n";
} catch (Exception $e) {
    echo "数据库连接：❌ 异常 - " . $e->getMessage() . "\n";
}

// 检查配置文件
if (function_exists('getRefundConfig')) {
    echo "配置文件：✅ 正常\n";
} else {
    echo "配置文件：❌ 异常\n";
}

// 检查统计表
$stats_table_exists = $DB->get_row("SHOW TABLES LIKE 'qingka_wangke_refund_stats'");
if ($stats_table_exists) {
    echo "统计表：✅ 存在\n";
} else {
    echo "统计表：❌ 不存在\n";
}

// 检查日志文件权限
$log_dir = dirname(__FILE__);
if (is_writable($log_dir)) {
    echo "日志权限：✅ 正常\n";
} else {
    echo "日志权限：❌ 无写入权限\n";
}

echo "\n=== 状态报告完成 ===\n";

// 如果有严重问题，返回错误代码
if (!getRefundConfig('enabled', false)) {
    echo "\n⚠️ 注意：自动退款功能已禁用\n";
    exit(1);
}

if ($total_pending_count > 20) {
    echo "\n⚠️ 警告：待退款订单过多({$total_pending_count}笔)，请检查系统\n";
    exit(2);
}

exit(0);

?>
