<?php
/**
 * 手动退款脚本
 * 用于测试退款功能或处理紧急情况
 * 
 * 使用方法：
 * php manual_refund.php --oid=订单ID --reason="退款原因"
 * php manual_refund.php --test  # 测试模式，不实际退款
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');
include(dirname(__FILE__) . '/refund_config.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 解析命令行参数
function parseArgs($argv) {
    $args = array();
    
    foreach ($argv as $arg) {
        if (strpos($arg, '--') === 0) {
            $parts = explode('=', substr($arg, 2), 2);
            $key = $parts[0];
            $value = isset($parts[1]) ? $parts[1] : true;
            $args[$key] = $value;
        }
    }
    
    return $args;
}

// 安全退款函数（从主脚本复制）
function safeRefund($order, $reason, $test_mode = false, $condition_type = '') {
    global $DB;

    try {
        // 验证退款条件，传递条件类型
        $validation = validateRefundConditions($order, $condition_type);
        if (!$validation['valid']) {
            return array('success' => false, 'msg' => $validation['reason']);
        }
        
        if ($test_mode) {
            return array(
                'success' => true, 
                'msg' => "[测试模式] 订单{$order['oid']}符合退款条件，金额：{$order['fees']}",
                'amount' => $order['fees'],
                'user' => $order['user']
            );
        }
        
        // 开始事务
        $DB->query("START TRANSACTION");
        
        // 再次验证订单状态，防止并发问题
        $current_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$order['oid']}' FOR UPDATE");
        
        if (!$current_order) {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单不存在');
        }
        
        // 检查是否已经在我们系统中退款
        if ($current_order['dockstatus'] == '4') {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单已退款，跳过');
        }

        // 对于上游已退款的订单，如果我们系统还没处理，应该继续处理
        // 对于其他类型的订单，如果状态已经是"已退款"，则跳过
        if ($condition_type !== 'upstream_refunded' && $current_order['status'] == '已退款') {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单状态已为已退款，跳过');
        }
        
        // 检查费用是否有效
        if ($current_order['fees'] <= 0) {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单费用无效');
        }
        
        // 获取用户信息
        $user = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid='{$current_order['uid']}'");
        if (!$user) {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '用户不存在');
        }
        
        // 执行退款
        // 1. 更新用户余额
        $update_user = $DB->query("UPDATE qingka_wangke_user SET money=money+'{$current_order['fees']}' WHERE uid='{$user['uid']}'");
        
        // 2. 更新订单状态
        $update_order = $DB->query("UPDATE qingka_wangke_order SET status='已退款', dockstatus='4' WHERE oid='{$current_order['oid']}'");
        
        // 3. 记录日志
        if ($update_user && $update_order) {
            wlog($user['uid'], "手动退款", "订单ID：{$current_order['oid']} 订单信息：{$current_order['user']}{$current_order['kcname']} 原因：{$reason}", "+{$current_order['fees']}");
            
            // 提交事务
            $DB->query("COMMIT");
            
            return array(
                'success' => true, 
                'msg' => "订单{$current_order['oid']}手动退款成功，金额：{$current_order['fees']}",
                'amount' => $current_order['fees'],
                'user' => $current_order['user']
            );
        } else {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '数据库更新失败');
        }
        
    } catch (Exception $e) {
        $DB->query("ROLLBACK");
        return array('success' => false, 'msg' => '退款异常：' . $e->getMessage());
    }
}

// 显示帮助信息
function showHelp() {
    echo "手动退款脚本使用说明：\n\n";
    echo "单个订单退款：\n";
    echo "  php manual_refund.php --oid=订单ID --reason=\"退款原因\"\n\n";
    echo "测试模式（不实际退款）：\n";
    echo "  php manual_refund.php --oid=订单ID --test\n\n";
    echo "批量测试（检查所有符合条件的订单）：\n";
    echo "  php manual_refund.php --test-all\n\n";
    echo "查看配置：\n";
    echo "  php manual_refund.php --config\n\n";
    echo "参数说明：\n";
    echo "  --oid        订单ID\n";
    echo "  --reason     退款原因\n";
    echo "  --test       测试模式，不实际执行退款\n";
    echo "  --test-all   测试所有符合条件的订单\n";
    echo "  --config     显示当前配置\n";
    echo "  --help       显示此帮助信息\n";
}

// 显示配置信息
function showConfig() {
    echo "当前退款配置：\n\n";
    echo "基本设置：\n";
    echo "  启用状态：" . (getRefundConfig('enabled') ? '启用' : '禁用') . "\n";
    echo "  单次最大退款数：" . getRefundConfig('max_refunds_per_run') . "\n";
    echo "  单次最大金额：" . getRefundConfig('max_amount_per_run') . "\n";
    echo "  最小订单年龄：" . getRefundConfig('min_order_age_minutes') . "分钟\n";
    echo "  最大订单年龄：" . getRefundConfig('max_order_age_days') . "天\n\n";
    
    echo "安全限制：\n";
    echo "  最小退款金额：" . getRefundConfig('min_refund_amount') . "\n";
    echo "  最大退款金额：" . getRefundConfig('max_refund_amount') . "\n\n";
    
    echo "退款条件：\n";
    $conditions = getRefundConfig('conditions');
    foreach ($conditions as $name => $config) {
        echo "  {$name}：" . ($config['enabled'] ? '启用' : '禁用') . "\n";
    }
}

// 测试所有符合条件的订单
function testAllOrders() {
    global $DB;
    
    echo "正在检查所有符合退款条件的订单...\n\n";
    
    // 使用与主脚本相同的条件
    $test_conditions = array(
        '账号密码错误订单' => "
            SELECT * FROM qingka_wangke_order 
            WHERE (
                kcname = '账号密码错误' 
                OR status LIKE '%请检查账号%' 
                OR status LIKE '%请检查密码%' 
                OR status LIKE '%账号密码%'
            )
            AND dockstatus = 2
            AND status != '已退款'
            AND dockstatus != '4'
            AND fees > 0
            AND addtime < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            ORDER BY addtime ASC
            LIMIT 50
        ",
        '对接失败订单' => "
            SELECT * FROM qingka_wangke_order 
            WHERE dockstatus = 2
            AND status != '已退款'
            AND status NOT LIKE '%请检查%'
            AND kcname != '账号密码错误'
            AND fees > 0
            AND addtime < DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY addtime ASC
            LIMIT 50
        "
    );
    
    $total_count = 0;
    $total_amount = 0;
    
    foreach ($test_conditions as $condition_name => $sql) {
        echo "检查：{$condition_name}\n";
        
        $result = $DB->query($sql);
        if (!$result) {
            echo "  查询失败\n\n";
            continue;
        }
        
        $count = 0;
        $amount = 0;
        
        while ($order = $result->fetch_assoc()) {
            $refund_result = safeRefund($order, '测试检查', true);
            
            if ($refund_result['success']) {
                $count++;
                $amount += $refund_result['amount'];
                echo "  ✅ 订单{$order['oid']} - {$order['user']} - {$order['fees']}元\n";
            } else {
                echo "  ❌ 订单{$order['oid']} - {$refund_result['msg']}\n";
            }
        }
        
        echo "  小计：{$count}笔，{$amount}元\n\n";
        $total_count += $count;
        $total_amount += $amount;
    }
    
    echo "总计：{$total_count}笔订单符合退款条件，总金额{$total_amount}元\n";
}

// 主程序
if (php_sapi_name() !== 'cli') {
    exit('此脚本只能在命令行模式下运行');
}

$args = parseArgs($argv);

// 显示帮助
if (isset($args['help']) || empty($args)) {
    showHelp();
    exit(0);
}

// 显示配置
if (isset($args['config'])) {
    showConfig();
    exit(0);
}

// 测试所有订单
if (isset($args['test-all'])) {
    testAllOrders();
    exit(0);
}

// 单个订单处理
if (isset($args['oid'])) {
    $oid = $args['oid'];
    $reason = isset($args['reason']) ? $args['reason'] : '手动退款';
    $test_mode = isset($args['test']);
    
    // 获取订单信息
    $order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$oid}'");
    
    if (!$order) {
        echo "错误：订单{$oid}不存在\n";
        exit(1);
    }
    
    echo "订单信息：\n";
    echo "  订单ID：{$order['oid']}\n";
    echo "  用户：{$order['user']}\n";
    echo "  课程：{$order['kcname']}\n";
    echo "  金额：{$order['fees']}\n";
    echo "  状态：{$order['status']}\n";
    echo "  对接状态：{$order['dockstatus']}\n";
    echo "  创建时间：{$order['addtime']}\n\n";
    
    if ($test_mode) {
        echo "测试模式：检查退款条件...\n";
    } else {
        echo "执行退款...\n";
    }
    
    $result = safeRefund($order, $reason, $test_mode);
    
    if ($result['success']) {
        echo "✅ {$result['msg']}\n";
    } else {
        echo "❌ 退款失败：{$result['msg']}\n";
        exit(1);
    }
} else {
    echo "错误：缺少必要参数\n\n";
    showHelp();
    exit(1);
}

?>
