<?php
/**
 * 自动退款脚本 - 宝塔面板版本
 * 专门为宝塔面板计划任务优化的版本
 * 输出格式更适合宝塔日志查看
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');
include(dirname(__FILE__) . '/refund_config.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 宝塔专用日志函数
function btLog($message, $type = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $formatted_message = "[{$timestamp}] [{$type}] {$message}";
    echo $formatted_message . "\n";
    
    // 同时写入文件日志
    $log_file = dirname(__FILE__) . '/auto_refund_bt.log';
    file_put_contents($log_file, $formatted_message . "\n", FILE_APPEND | LOCK_EX);
}

// 安全退款函数
function safeRefund($order, $reason, $condition_type = '') {
    global $DB;

    try {
        // 验证退款条件，传递条件类型
        $validation = validateRefundConditions($order, $condition_type);
        if (!$validation['valid']) {
            return array('success' => false, 'msg' => $validation['reason']);
        }
        
        // 开始事务
        $DB->query("START TRANSACTION");
        
        // 再次验证订单状态，防止并发问题
        $current_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$order['oid']}' FOR UPDATE");
        
        if (!$current_order) {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单不存在');
        }
        
        // 检查是否已经在我们系统中退款
        if ($current_order['dockstatus'] == '4') {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单已退款，跳过');
        }

        // 对于上游已退款的订单，如果我们系统还没处理，应该继续处理
        // 对于其他类型的订单，如果状态已经是"已退款"，则跳过
        if ($condition_type !== 'upstream_refunded' && $current_order['status'] == '已退款') {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单状态已为已退款，跳过');
        }
        
        // 检查费用是否有效
        if ($current_order['fees'] <= 0) {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '订单费用无效');
        }
        
        // 获取用户信息
        $user = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid='{$current_order['uid']}'");
        if (!$user) {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '用户不存在');
        }
        
        // 执行退款
        // 1. 更新用户余额
        $update_user = $DB->query("UPDATE qingka_wangke_user SET money=money+'{$current_order['fees']}' WHERE uid='{$user['uid']}'");
        
        // 2. 更新订单状态
        $update_order = $DB->query("UPDATE qingka_wangke_order SET status='已退款', dockstatus='4' WHERE oid='{$current_order['oid']}'");
        
        // 3. 记录日志
        if ($update_user && $update_order) {
            wlog($user['uid'], "自动退款", "订单ID：{$current_order['oid']} 订单信息：{$current_order['user']}{$current_order['kcname']} 原因：{$reason}", "+{$current_order['fees']}");
            
            // 提交事务
            $DB->query("COMMIT");
            
            return array(
                'success' => true, 
                'msg' => "订单{$current_order['oid']}自动退款成功，金额：{$current_order['fees']}",
                'amount' => $current_order['fees'],
                'user' => $current_order['user'],
                'oid' => $current_order['oid']
            );
        } else {
            $DB->query("ROLLBACK");
            return array('success' => false, 'msg' => '数据库更新失败');
        }
        
    } catch (Exception $e) {
        $DB->query("ROLLBACK");
        return array('success' => false, 'msg' => '退款异常：' . $e->getMessage());
    }
}

btLog("=== 自动退款系统开始执行 ===", "START");

try {
    // 检查配置和初始化
    if (!getRefundConfig('enabled', false)) {
        btLog("自动退款功能已禁用", "WARNING");
        exit(0);
    }
    
    btLog("配置检查通过，开始处理退款", "INFO");
    
    // 创建统计表
    createRefundStatsTable();
    
    // 定义需要自动退款的条件
    $refund_conditions = array();
    
    // 条件1：账号密码错误的订单
    if (getRefundConfig('conditions.password_error.enabled', true)) {
        $wait_minutes = getRefundConfig('conditions.password_error.wait_minutes', 30);
        $max_per_run = getRefundConfig('conditions.password_error.max_per_run', 20);
        $keywords = getRefundConfig('conditions.password_error.keywords', array('账号密码错误'));
        
        $keyword_conditions = array();
        foreach ($keywords as $keyword) {
            $keyword_conditions[] = "kcname LIKE '%{$keyword}%' OR status LIKE '%{$keyword}%'";
        }
        $keyword_sql = implode(' OR ', $keyword_conditions);
        
        $refund_conditions[] = array(
            'name' => '账号密码错误订单',
            'sql' => "
                SELECT * FROM qingka_wangke_order 
                WHERE ({$keyword_sql})
                AND dockstatus = 2
                AND status != '已退款'
                AND dockstatus != '4'
                AND fees > 0
                AND addtime < DATE_SUB(NOW(), INTERVAL {$wait_minutes} MINUTE)
                ORDER BY addtime ASC
                LIMIT {$max_per_run}
            ",
            'reason' => '账号密码错误'
        );
    }
    
    // 条件2：对接失败的订单
    if (getRefundConfig('conditions.dock_failed.enabled', true)) {
        $wait_hours = getRefundConfig('conditions.dock_failed.wait_hours', 1);
        $max_per_run = getRefundConfig('conditions.dock_failed.max_per_run', 10);
        
        $refund_conditions[] = array(
            'name' => '对接失败订单',
            'sql' => "
                SELECT * FROM qingka_wangke_order 
                WHERE dockstatus = 2
                AND status != '已退款'
                AND status NOT LIKE '%请检查%'
                AND kcname != '账号密码错误'
                AND fees > 0
                AND addtime < DATE_SUB(NOW(), INTERVAL {$wait_hours} HOUR)
                ORDER BY addtime ASC
                LIMIT {$max_per_run}
            ",
            'reason' => '对接失败超时'
        );
    }
    
    // 条件3：上游平台返回已退款状态的订单
    if (getRefundConfig('conditions.upstream_refunded.enabled', true)) {
        $wait_minutes = getRefundConfig('conditions.upstream_refunded.wait_minutes', 5);
        $max_per_run = getRefundConfig('conditions.upstream_refunded.max_per_run', 15);
        
        $refund_conditions[] = array(
            'name' => '上游已退款订单',
            'sql' => "
                SELECT * FROM qingka_wangke_order 
                WHERE status = '已退款'
                AND dockstatus != '4'
                AND fees > 0
                AND addtime < DATE_SUB(NOW(), INTERVAL {$wait_minutes} MINUTE)
                ORDER BY addtime ASC
                LIMIT {$max_per_run}
            ",
            'reason' => '上游平台已退款'
        );
    }
    
    // 条件4：长时间未处理的失败订单
    if (getRefundConfig('conditions.long_pending.enabled', true)) {
        $wait_hours = getRefundConfig('conditions.long_pending.wait_hours', 24);
        $max_per_run = getRefundConfig('conditions.long_pending.max_per_run', 5);
        
        $refund_conditions[] = array(
            'name' => '长时间未处理订单',
            'sql' => "
                SELECT * FROM qingka_wangke_order 
                WHERE dockstatus = 2
                AND status IN ('待处理', '待更新')
                AND fees > 0
                AND addtime < DATE_SUB(NOW(), INTERVAL {$wait_hours} HOUR)
                ORDER BY addtime ASC
                LIMIT {$max_per_run}
            ",
            'reason' => '长时间未处理'
        );
    }
    
    $total_refunded = 0;
    $total_amount = 0;
    $max_refunds = getRefundConfig('max_refunds_per_run', 50);
    $max_amount = getRefundConfig('max_amount_per_run', 1000);
    
    btLog("开始处理 " . count($refund_conditions) . " 种退款条件", "INFO");
    
    // 处理每种退款条件
    foreach ($refund_conditions as $condition) {
        // 检查是否已达到限制
        if ($total_refunded >= $max_refunds) {
            btLog("已达到单次最大退款数量限制({$max_refunds})，停止处理", "WARNING");
            break;
        }
        
        if ($total_amount >= $max_amount) {
            btLog("已达到单次最大退款金额限制({$max_amount})，停止处理", "WARNING");
            break;
        }
        
        btLog("开始处理：{$condition['name']}", "INFO");
        
        $result = $DB->query($condition['sql']);
        
        if (!$result) {
            btLog("查询{$condition['name']}失败", "ERROR");
            continue;
        }
        
        $count = 0;
        $condition_amount = 0;
        
        while ($order = $result->fetch_assoc()) {
            // 检查限制
            if ($total_refunded >= $max_refunds || $total_amount >= $max_amount) {
                break;
            }

            // 确定条件类型
            $condition_type = '';
            if (strpos($condition['name'], '上游') !== false) {
                $condition_type = 'upstream_refunded';
            } elseif (strpos($condition['name'], '账号密码') !== false) {
                $condition_type = 'password_error';
            } elseif (strpos($condition['name'], '对接失败') !== false) {
                $condition_type = 'dock_failed';
            } elseif (strpos($condition['name'], '长时间') !== false) {
                $condition_type = 'long_pending';
            }

            $refund_result = safeRefund($order, $condition['reason'], $condition_type);

            if ($refund_result['success']) {
                $count++;
                $total_refunded++;
                $condition_amount += $refund_result['amount'];
                $total_amount += $refund_result['amount'];
                
                btLog("退款成功 - 订单:{$refund_result['oid']} 用户:{$refund_result['user']} 金额:{$refund_result['amount']}元", "SUCCESS");
                
                // 防止过快处理，避免对数据库造成压力
                usleep(100000); // 0.1秒
            } else {
                btLog("订单{$order['oid']}退款失败：{$refund_result['msg']}", "WARNING");
            }
        }
        
        // 记录统计
        if ($count > 0) {
            recordRefundStats($condition['name'], $count, $condition_amount);
            btLog("{$condition['name']}处理完成：退款{$count}个订单，金额{$condition_amount}元", "SUCCESS");
        } else {
            btLog("{$condition['name']}：无需退款的订单", "INFO");
        }
    }
    
    // 检查是否需要报警
    $alerts = checkAlertThreshold($total_refunded, $total_amount);
    if (!empty($alerts)) {
        foreach ($alerts as $alert) {
            btLog("报警：{$alert}", "ALERT");
        }
    }
    
    // 总结报告
    if ($total_refunded > 0) {
        btLog("自动退款完成：共退款{$total_refunded}个订单，总金额{$total_amount}元", "SUCCESS");
        
        // 记录总体统计
        recordRefundStats('总计', $total_refunded, $total_amount);
    } else {
        btLog("本次执行无需退款的订单", "INFO");
    }
    
    btLog("=== 自动退款脚本执行完成 ===", "END");
    
} catch (Exception $e) {
    btLog("脚本执行异常: " . $e->getMessage(), "ERROR");
    btLog("文件: " . $e->getFile(), "ERROR");
    btLog("行号: " . $e->getLine(), "ERROR");
    exit(1);
}

?>
