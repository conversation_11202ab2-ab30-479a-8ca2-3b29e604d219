<?php
/**
 * 进度同步专用脚本 - 每2分钟执行一次
 * 专门处理已提交订单的进度同步
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

$log_file = dirname(__FILE__) . '/progress_sync.log';
$max_log_size = 10 * 1024 * 1024; // 10MB

// 日志函数
function writeLog($message) {
    global $log_file, $max_log_size;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}\n";
    
    // 检查日志文件大小，如果超过限制则清空
    if (file_exists($log_file) && filesize($log_file) > $max_log_size) {
        file_put_contents($log_file, '');
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    echo $log_message;
}

writeLog("=== 进度同步脚本开始执行 ===");

try {
    // 连接Redis
    $redis = new Redis();
    $redis->connect("127.0.0.1", "6379");
    $redis->select(7); // 选择数据库7
    
    $rediscode = $redis->ping();
    if ($rediscode != true) {
        writeLog("❌ Redis连接失败");
        exit(1);
    }
    
    writeLog("✅ Redis连接成功");
    
    // 检查当前队列长度
    $current_queue_length = $redis->llen("oidsydcl");
    writeLog("当前进度同步队列长度: {$current_queue_length}");
    
    // 如果队列中已经有很多订单，就不再添加新的
    if ($current_queue_length > 100) {
        writeLog("⚠️ 进度同步队列中订单过多({$current_queue_length})，跳过本次入队");
        exit(0);
    }
    
    // 查询需要进度同步的订单（排除新订单，专注于已提交的订单）
    // 包含易教育订单的自动同步
    $sql = "
        SELECT o.oid, o.user, o.kcname, o.yid, o.status, o.process, o.addtime, o.hid,
               TIMESTAMPDIFF(MINUTE, o.addtime, NOW()) as minutes_old,
               h.pt as platform_type
        FROM qingka_wangke_order o
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid
        WHERE o.dockstatus = 1
        AND o.status NOT IN ('已完成', '已退款', '已取消')  -- 排除终态订单
        AND (o.status = '补刷中' OR (o.process NOT LIKE '%100%' AND o.process NOT LIKE '%已完成%' AND o.process NOT LIKE '%学习完成%'))  -- 排除已完成进度，除非是补刷
        AND (
            -- 普通订单：5分钟前的订单
            (
                o.addtime <= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                AND o.status IN ('上号中', '已提取', '已提交', '登录中', '考试中', '运行中', '进行中', '学习中', '队列中', '补刷中')
                AND (
                    o.process = '获取失败'
                    OR o.process = 'chusheng-获取失败'
                    OR o.process IS NULL
                    OR o.process = ''
                    OR o.process = '0%'
                    OR (o.process NOT LIKE '%100%' AND o.process NOT LIKE '%已完成%' AND o.process NOT LIKE '%学习完成%')
                )
            )
            OR
            -- 易教育订单：特殊处理，包含更多状态
            (
                h.pt = 'jxjy'
                AND o.addtime <= DATE_SUB(NOW(), INTERVAL 2 MINUTE)  -- 易教育订单2分钟后开始同步
                AND o.status NOT IN ('已完成', '已退款', '已取消')
                AND (o.status = '补刷中' OR (o.process NOT LIKE '%100%' AND o.process NOT LIKE '%已完成%' AND o.process NOT LIKE '%学习完成%'))  -- 排除已完成进度，除非是补刷
                AND (
                    o.process = '获取失败'
                    OR o.process = 'chusheng-获取失败'
                    OR o.process IS NULL
                    OR o.process = ''
                    OR o.process = '0%'
                    OR (o.process NOT LIKE '%100%' AND o.process NOT LIKE '%已完成%' AND o.process NOT LIKE '%学习完成%')
                )
            )
        )
        ORDER BY
            CASE
                WHEN o.process IN ('获取失败', 'chusheng-获取失败', '', '0%') THEN 0  -- 优先处理失败的
                WHEN h.pt = 'jxjy' THEN 1  -- 易教育订单次优先
                ELSE 2
            END,
            o.addtime DESC
        LIMIT 25
    ";
    
    $result = $DB->query($sql);
    
    if (!$result) {
        writeLog("❌ 数据库查询失败");
        exit(1);
    }
    
    $orders_to_enqueue = [];
    $total_orders = 0;
    
    while ($row = $result->fetch_assoc()) {
        $total_orders++;
        
        // 检查订单是否已经在队列中
        $oid = $row['oid'];
        $queue_items = $redis->lrange("oidsydcl", 0, -1);
        
        if (!in_array($oid, $queue_items)) {
            $orders_to_enqueue[] = $oid;
            writeLog("准备进度同步: 订单{$oid} - {$row['user']} - {$row['status']} - {$row['process']} (创建于{$row['minutes_old']}分钟前)");
        } else {
            writeLog("跳过: 订单{$oid}已在进度同步队列中");
        }
    }
    
    writeLog("扫描到{$total_orders}个需要进度同步的订单，其中" . count($orders_to_enqueue) . "个需要入队");
    
    // 将订单加入进度同步队列
    $enqueued_count = 0;
    foreach ($orders_to_enqueue as $oid) {
        $redis->lPush("oidsydcl", $oid);
        $enqueued_count++;
    }
    
    if ($enqueued_count > 0) {
        writeLog("✅ 成功将{$enqueued_count}个订单加入进度同步队列");
    } else {
        writeLog("ℹ️ 没有新订单需要进度同步");
    }
    
    // 检查守护进程工作状态（通过队列处理效果判断）
    // 由于shell_exec被禁用，通过队列长度和处理活动来判断守护进程状态

    if ($final_queue_length > 100) {
        writeLog("⚠️ 队列积压严重({$final_queue_length})，建议检查守护进程状态");
    } elseif ($final_queue_length > 50) {
        writeLog("⚠️ 队列有些积压({$final_queue_length})，但仍在正常范围");
    } else {
        writeLog("✅ 队列长度正常({$final_queue_length})，系统运行良好");
    }

    // 检查最近的订单处理活动
    $recent_updates = $DB->get_row("
        SELECT COUNT(*) as count
        FROM qingka_wangke_order
        WHERE uptime > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    ");

    $recent_count = $recent_updates ? $recent_updates['count'] : 0;
    if ($recent_count > 0) {
        writeLog("✅ 最近10分钟有{$recent_count}个订单被处理");
    } else {
        writeLog("ℹ️ 最近10分钟没有订单处理活动");
    }
    
    // 显示最终队列状态
    $final_queue_length = $redis->llen("oidsydcl");
    writeLog("最终进度同步队列长度: {$final_queue_length}");
    
    writeLog("=== 进度同步脚本执行完成 ===");
    
} catch (Exception $e) {
    writeLog("❌ 异常: " . $e->getMessage());
    writeLog("文件: " . $e->getFile());
    writeLog("行号: " . $e->getLine());
    exit(1);
}

?>
