<?php
/**
 * 守护进程管理脚本
 * 用于启动、停止、重启和检查守护进程状态
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 守护进程配置
$core_daemons = [
    'chusheng1.php' => '核心订单处理守护进程1',
    'chusheng2.php' => '核心订单处理守护进程2'
];

$optional_daemons = [
    'chusheng3.php' => '可选订单处理守护进程3'
];

// 合并所有守护进程
$daemons = array_merge($core_daemons, $optional_daemons);

$redis_dir = dirname(__FILE__);

// 获取操作参数
$action = isset($_GET['action']) ? $_GET['action'] : 'status';
$daemon = isset($_GET['daemon']) ? $_GET['daemon'] : 'all';

echo "<h1>守护进程管理</h1>\n";
echo "<pre>\n";

echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo "操作: {$action}\n";
echo "目标: {$daemon}\n\n";

/**
 * 检查进程是否运行
 */
function isProcessRunning($process_name) {
    $cmd = "ps aux | grep '{$process_name}' | grep -v grep | wc -l";
    $count = intval(trim(shell_exec($cmd)));
    return $count > 0;
}

/**
 * 获取进程PID
 */
function getProcessPid($process_name) {
    $cmd = "ps aux | grep '{$process_name}' | grep -v grep";
    $output = trim(shell_exec($cmd));
    if (!empty($output)) {
        if (preg_match('/\s+(\d+)\s+/', $output, $matches)) {
            return $matches[1];
        }
    }
    return null;
}

/**
 * 启动进程
 */
function startProcess($process_file, $redis_dir) {
    $full_path = "{$redis_dir}/{$process_file}";
    if (!file_exists($full_path)) {
        return "文件不存在: {$full_path}";
    }
    
    $cmd = "nohup php {$full_path} > /dev/null 2>&1 &";
    shell_exec($cmd);
    
    // 等待2秒检查是否启动成功
    sleep(2);
    
    if (isProcessRunning($process_file)) {
        $pid = getProcessPid($process_file);
        return "启动成功 (PID: {$pid})";
    } else {
        return "启动失败";
    }
}

/**
 * 停止进程
 */
function stopProcess($process_name) {
    $pid = getProcessPid($process_name);
    if ($pid) {
        $cmd = "kill {$pid}";
        shell_exec($cmd);
        
        // 等待2秒检查是否停止成功
        sleep(2);
        
        if (!isProcessRunning($process_name)) {
            return "停止成功";
        } else {
            // 强制杀死
            shell_exec("kill -9 {$pid}");
            sleep(1);
            if (!isProcessRunning($process_name)) {
                return "强制停止成功";
            } else {
                return "停止失败";
            }
        }
    } else {
        return "进程未运行";
    }
}

// 执行操作
switch ($action) {
    case 'status':
        echo "=== 守护进程状态检查 ===\n";

        $core_running = 0;
        $optional_running = 0;

        echo "核心守护进程（必需）:\n";
        foreach ($core_daemons as $process_file => $description) {
            $is_running = isProcessRunning($process_file);
            $pid = $is_running ? getProcessPid($process_file) : null;

            $status = $is_running ? "✅ 运行中" : "❌ 未运行";
            $pid_info = $pid ? " (PID: {$pid})" : "";

            echo "  {$description} ({$process_file}): {$status}{$pid_info}\n";

            if ($is_running) {
                $core_running++;
            }
        }

        echo "\n可选守护进程:\n";
        foreach ($optional_daemons as $process_file => $description) {
            $is_running = isProcessRunning($process_file);
            $pid = $is_running ? getProcessPid($process_file) : null;

            $status = $is_running ? "✅ 运行中" : "ℹ️ 未运行";
            $pid_info = $pid ? " (PID: {$pid})" : "";

            echo "  {$description} ({$process_file}): {$status}{$pid_info}\n";

            if ($is_running) {
                $optional_running++;
            }
        }

        echo "\n=== 状态总结 ===\n";
        echo "核心进程: {$core_running}/" . count($core_daemons) . " 个在运行\n";
        echo "可选进程: {$optional_running}/" . count($optional_daemons) . " 个在运行\n";

        if ($core_running >= 2) {
            echo "✅ 系统状态正常：{$core_running}个核心进程足以处理订单队列\n";
        } elseif ($core_running >= 1) {
            echo "⚠️ 系统状态一般：建议启动第二个核心进程以提高处理效率\n";
        } else {
            echo "❌ 系统状态异常：没有核心进程运行，订单无法处理！\n";
        }
        
        // 显示操作链接
        echo "\n=== 可用操作 ===\n";
        echo "启动所有: ?action=start&daemon=all\n";
        echo "停止所有: ?action=stop&daemon=all\n";
        echo "重启所有: ?action=restart&daemon=all\n";
        echo "启动单个: ?action=start&daemon=chusheng1.php\n";
        echo "停止单个: ?action=stop&daemon=chusheng1.php\n";
        break;
        
    case 'start':
        echo "=== 启动守护进程 ===\n";
        
        if ($daemon == 'all') {
            foreach ($daemons as $process_file => $description) {
                echo "启动 {$description} ({$process_file})...\n";
                
                if (isProcessRunning($process_file)) {
                    echo "  ⚠️ 进程已在运行\n";
                } else {
                    $result = startProcess($process_file, $redis_dir);
                    echo "  {$result}\n";
                }
            }
        } else {
            if (isset($daemons[$daemon])) {
                echo "启动 {$daemons[$daemon]} ({$daemon})...\n";
                
                if (isProcessRunning($daemon)) {
                    echo "  ⚠️ 进程已在运行\n";
                } else {
                    $result = startProcess($daemon, $redis_dir);
                    echo "  {$result}\n";
                }
            } else {
                echo "❌ 无效的守护进程名称: {$daemon}\n";
            }
        }
        break;
        
    case 'stop':
        echo "=== 停止守护进程 ===\n";
        
        if ($daemon == 'all') {
            foreach ($daemons as $process_file => $description) {
                echo "停止 {$description} ({$process_file})...\n";
                $result = stopProcess($process_file);
                echo "  {$result}\n";
            }
        } else {
            if (isset($daemons[$daemon])) {
                echo "停止 {$daemons[$daemon]} ({$daemon})...\n";
                $result = stopProcess($daemon);
                echo "  {$result}\n";
            } else {
                echo "❌ 无效的守护进程名称: {$daemon}\n";
            }
        }
        break;
        
    case 'restart':
        echo "=== 重启守护进程 ===\n";
        
        if ($daemon == 'all') {
            foreach ($daemons as $process_file => $description) {
                echo "重启 {$description} ({$process_file})...\n";
                
                // 先停止
                $stop_result = stopProcess($process_file);
                echo "  停止: {$stop_result}\n";
                
                // 再启动
                $start_result = startProcess($process_file, $redis_dir);
                echo "  启动: {$start_result}\n";
            }
        } else {
            if (isset($daemons[$daemon])) {
                echo "重启 {$daemons[$daemon]} ({$daemon})...\n";
                
                // 先停止
                $stop_result = stopProcess($daemon);
                echo "  停止: {$stop_result}\n";
                
                // 再启动
                $start_result = startProcess($daemon, $redis_dir);
                echo "  启动: {$start_result}\n";
            } else {
                echo "❌ 无效的守护进程名称: {$daemon}\n";
            }
        }
        break;
        
    case 'logs':
        echo "=== 查看进程信息 ===\n";
        
        $cmd = "ps aux | grep php | grep chusheng | grep -v grep";
        $output = shell_exec($cmd);
        
        if (!empty($output)) {
            echo "运行中的chusheng进程:\n";
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (!empty($line)) {
                    echo "  {$line}\n";
                }
            }
        } else {
            echo "没有运行中的chusheng进程\n";
        }
        
        // 检查Redis连接
        echo "\n=== Redis连接检查 ===\n";
        try {
            $redis = new Redis();
            $redis->connect("127.0.0.1", "6379");
            $redis->select(7);
            
            if ($redis->ping()) {
                echo "✅ Redis连接正常\n";
                $queue_length = $redis->llen("oidsydcl");
                echo "进度同步队列长度: {$queue_length}\n";
            } else {
                echo "❌ Redis连接失败\n";
            }
        } catch (Exception $e) {
            echo "❌ Redis连接异常: " . $e->getMessage() . "\n";
        }
        break;
        
    default:
        echo "❌ 无效的操作: {$action}\n";
        echo "可用操作: status, start, stop, restart, logs\n";
        break;
}

echo "\n=== 操作完成 ===\n";
echo "返回状态检查: ?action=status\n";

echo "</pre>\n";
?>
