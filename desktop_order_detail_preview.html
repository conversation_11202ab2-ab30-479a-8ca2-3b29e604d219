<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桌面端订单详情优化预览</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .preview-container {
            max-width: 950px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .preview-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .preview-title {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        
        .preview-subtitle {
            margin: 8px 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .demo-section {
            padding: 30px;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        
        .comparison-item {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .comparison-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .before .comparison-title {
            color: #dc2626;
        }
        
        .after .comparison-title {
            color: #16a34a;
        }
        
        .comparison-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .comparison-list li {
            padding: 8px 0;
            font-size: 14px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .comparison-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1 class="preview-title">🎨 桌面端订单详情优化</h1>
            <p class="preview-subtitle">现代化UI设计，提升用户体验</p>
        </div>
        
        <div class="demo-section">
            <div style="text-align: center;">
                <button class="demo-button" onclick="alert('请在实际系统中查看订单详情效果')">
                    🚀 查看优化效果
                </button>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">现代化设计</div>
                    <div class="feature-desc">采用渐变背景、圆角设计和精美阴影，符合现代设计趋势</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">响应式布局</div>
                    <div class="feature-desc">桌面端和移动端分别优化，确保最佳显示效果</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">交互优化</div>
                    <div class="feature-desc">悬浮效果、点击反馈和流畅动画，提升操作体验</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">功能完整</div>
                    <div class="feature-desc">保持所有原有功能，只优化视觉效果和用户体验</div>
                </div>
            </div>
            
            <div class="comparison">
                <div class="comparison-item before">
                    <div class="comparison-title">❌ 优化前</div>
                    <ul class="comparison-list">
                        <li>界面简陋，缺乏设计感</li>
                        <li>信息排列混乱</li>
                        <li>缺乏视觉层次</li>
                        <li>操作按钮不够醒目</li>
                        <li>整体体验较差</li>
                    </ul>
                </div>
                
                <div class="comparison-item after">
                    <div class="comparison-title">✅ 优化后</div>
                    <ul class="comparison-list">
                        <li>现代化卡片式设计</li>
                        <li>信息分组清晰</li>
                        <li>渐变背景和精美图标</li>
                        <li>大按钮易于操作</li>
                        <li>整体美观专业</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
