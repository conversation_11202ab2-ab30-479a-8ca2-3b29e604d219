<?php
include('../confing/common.php');
date_default_timezone_set('Asia/Shanghai');
$startTime = microtime(true);
$batchTimeStr = date('Y-m-d H:i:s', (int)$startTime); // 本批次开始时间（用于回写）
$logFile = __DIR__ . '/同步上游商品日志.txt';
/* ================= 工具函数 & 兼容 ================= */
function db_escape($DB, $str) {
  if ($str === null) return '';
  if (method_exists($DB, 'escape')) return $DB->escape($str);
  return addslashes($str);
}
function writeDbLogRows(&$logRows, $row, $action, $before, $after, $remark = '', $pushStatus = 0, $mode = '', $fenleiName = '', $huoyuanName = '') {
  $nowTime = date('Y-m-d H:i:s');
  $hid = (int)($row['docking'] ?? 0);
  $cid = (int)($row['cid'] ?? 0);
  $platformName = (string)($row['name'] ?? '');
  $fenleiId = (int)($row['fenlei'] ?? 0);
  // 组装 data_before / data_after
  switch ($action) {
    case '克隆上架':
      $data_before = '';
      $data_after = "名称：" . ($after['name'] ?? '') .
                    " / 价格：" . ($after['price'] ?? '') .
                    " / 说明：" . ($after['content'] ?? '') .
                    " / 时间：" . $nowTime;
      break;
    case '更新价格':
    case '更新说明':
    case '更新名称':
    case '替换前缀':
    case '下架':
    case '上架':
    case '跳过':
      $data_before = (string)$before;
      $data_after = (string)$after;
      break;
    case '删除':
    case '删除6月未上架':
    default:
      $data_before = is_array($before) ? implode(' / ', $before) : (string)$before;
      $data_after = (string)$after;
  }
  // 备注
  $remarkArr = [];
  $remarkArr[] = "类型：{$action}";
  $remarkArr[] = "hid={$hid}";
  $remarkArr[] = "分类【{$fenleiName}】";
  $remarkArr[] = "商品CID【{$cid}】";
  if ($platformName) $remarkArr[] = "名称【{$platformName}】";
  if ($action === '更新价格') $remarkArr[] = "价格变动：【旧：{$before}—新：{$after}】";
  if (in_array($action, ['更新说明','更新名称','替换前缀'])) $remarkArr[] = "旧：【{$before}】/新：【{$after}】";
  if (in_array($action, ['删除','删除6月未上架'])) {
    $remarkArr[] = "说明：【" . ($row['content'] ?? '') . "】";
    $remarkArr[] = "价格：【" . ($row['price'] ?? '') . "】";
  }
  if ($action === '跳过') $remarkArr[] = "跳过原因：命中跳过规则";
  if ($mode) $remarkArr[] = "同步模式：【{$mode}】";
  $remarkArr[] = "时间：{$nowTime}";
  $remarkFull = implode(' / ', $remarkArr);
  $logRows[] = [
    'product_id' => $cid,
    'huoyuan_id' => $hid,
    'huoyuan_name' => $huoyuanName,
    'platform_name'=> $platformName,
    'fenlei_id' => $fenleiId,
    'fenlei_name' => $fenleiName,
    'action' => $action,
    'data_before' => $data_before,
    'data_after' => $data_after,
    'operator' => 'php管理员',
    'op_time' => $nowTime,
    'remark' => $remarkFull,
    'push_status' => (int)$pushStatus,
  ];
}
function batchInsert($DB, $table, $columns, $rows, $chunkSize = 200) {
  if (empty($rows)) return;
  $cols = '`' . implode('`,`', $columns) . '`';
  $n = count($rows);
  for ($i = 0; $i < $n; $i += $chunkSize) {
    $vals = [];
    $end = min($n, $i + $chunkSize);
    for ($j = $i; $j < $end; $j++) {
      $r = $rows[$j];
      $escaped = array_map(function($v) use ($DB){
        if ($v === null) return "NULL";
        return "'" . db_escape($DB, (string)$v) . "'";
      }, $r);
      $vals[] = '(' . implode(',', $escaped) . ')';
    }
    $sql = "INSERT INTO {$table} ({$cols}) VALUES " . implode(',', $vals);
    $DB->query($sql);
  }
}
/* ================ 读取全局配置 ================ */
$config = $DB->get_row("SELECT * FROM qingka_wangke_class_config ORDER BY id DESC LIMIT 1");
if (!$config) { echo "未配置同步参数，退出！\n"; exit; }
$huoyuanIdStr = trim($config['hyid'] ?? $config['huoyuan_id'] ?? '');
$huoyuanIds = array_values(array_filter(array_map('intval', explode(',', $huoyuanIdStr))));
$huoyuanPriceRate = $config['huoyuan_price_rate'] ? json_decode($config['huoyuan_price_rate'], true) : [];
$huoyuanCategoryPrice = $config['huoyuan_category_price'] ? json_decode($config['huoyuan_category_price'], true) : [];
$skipRule = $config['skip_rule'] ? json_decode($config['skip_rule'], true) : [];
$replaceRule = $config['replace_rule'] ? json_decode($config['replace_rule'], true) : [];
$tbjgkg = isset($config['tbjgkg']) ? (bool)$config['tbjgkg'] : (isset($config['update_price']) ? (bool)$config['update_price'] : 1);
$tbsmkg = isset($config['tbsmkg']) ? (bool)$config['tbsmkg'] : (isset($config['update_content']) ? (bool)$config['update_content'] : 1);
$tbsjmc = isset($config['tbsjmc']) ? (bool)$config['tbsjmc'] : (isset($config['update_name']) ? (bool)$config['update_name'] : 0);
$tbztkg = isset($config['tbztkg']) ? (bool)$config['tbztkg'] : (isset($config['update_status']) ? (bool)$config['update_status'] : 1);
$qzdqjg = isset($config['qzdqjg']) && in_array($config['qzdqjg'], [1, '1'], true);
$klsjkg = isset($config['klsjkg']) ? (bool)$config['klsjkg'] : (isset($config['clone_enabled']) ? (bool)$config['clone_enabled'] : 1);
$qzkg = isset($config['qzkg']) ? (bool)$config['qzkg'] : (isset($config['enable_replace_rule']) ? (bool)$config['enable_replace_rule'] : 1);
$tgkg = isset($config['tgkg']) ? (bool)$config['tgkg'] : (isset($config['enable_skip_rule']) ? (bool)$config['enable_skip_rule'] : 1);
$pushEnabled = isset($config['tskg']) ? (bool)$config['tskg'] : (isset($config['push_enabled']) ? (bool)$config['push_enabled'] : 1);
$showdocPushUrlRaw = trim($config['sdurl'] ?? $config['showdoc_push_url'] ?? '');
$zmsjkg = isset($config['zmsjkg']) ? (bool)$config['zmsjkg'] : (isset($config['auto_delete_6m']) ? (bool)$config['auto_delete_6m'] : 0);
if (empty($huoyuanIds)) { echo "无有效货源ID，配置错误！\n"; exit; }
$modeDesc = $qzdqjg ? '奸商模式（只升不降）' : '良心模式（随心随意）';
// 本次是否会执行推送 & 默认写入的 push_status（会推送=0；不推送=2(跳过)）
$willPushThisRun = $pushEnabled;
$defaultPushStatus = $willPushThisRun ? 0 : 2;
/* ================ 预加载：货源 & 分类名（减少重复查询） ================ */
$huoyuanNameMap = [];
if ($huoyuanIds) {
  $idList = implode(',', array_map('intval', $huoyuanIds));
  $rs = $DB->query("SELECT hid,name,url,`user`,`pass` FROM qingka_wangke_huoyuan WHERE hid IN ($idList)");
  while ($r = $DB->fetch($rs)) { $huoyuanNameMap[(int)$r['hid']] = $r; }
}
$fenleiNameMap = [];
$rs = $DB->query("SELECT id,name FROM qingka_wangke_fenlei");
while ($r = $DB->fetch($rs)) { $fenleiNameMap[(int)$r['id']] = $r['name']; }
/* ================ 并发拉取上游（curl_multi） ================ */
function fetchUpstreamsMulti($sources, $concurrency = 8) {
  $mh = curl_multi_init();
  $pending = [];
  foreach ($sources as $hid => $s) {
    $apiUrl = rtrim($s['url'], '/') . "/api.php?act=getclass";
    $ch = curl_init();
    curl_setopt_array($ch, [
      CURLOPT_URL => $apiUrl,
      CURLOPT_POST => true,
      CURLOPT_POSTFIELDS => http_build_query(['uid'=>$s['uid'], 'key'=>$s['key']]),
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_CONNECTTIMEOUT => 5,
      CURLOPT_TIMEOUT => 12,
    ]);
    $pending[] = [$hid, $ch];
  }
  $active = [];
  $results = [];
  while ($pending || $active) {
    while (count($active) < $concurrency && $pending) {
      [$hid, $ch] = array_shift($pending);
      curl_multi_add_handle($mh, $ch);
      $active[$hid] = $ch;
    }
    do { $mrc = curl_multi_exec($mh, $running); }
    while ($mrc === CURLM_CALL_MULTI_PERFORM);
    while ($info = curl_multi_info_read($mh)) {
      $ch = $info['handle'];
      $hidDone = null;
      foreach ($active as $hid => $h) { if ($h === $ch) { $hidDone = $hid; break; } }
      if ($hidDone !== null) {
        $results[$hidDone] = curl_multi_getcontent($ch);
        curl_multi_remove_handle($mh, $ch);
        curl_close($ch);
        unset($active[$hidDone]);
      }
    }
    if ($running) curl_multi_select($mh, 0.5);
  }
  curl_multi_close($mh);
  return $results; // [hid => raw_json]
}
// 组装上游请求源
$sources = [];
foreach ($huoyuanIds as $hid) {
  $rowHY = $huoyuanNameMap[$hid] ?? null;
  if (!$rowHY || empty($rowHY['url'])) continue;
  $sources[$hid] = ['url'=>$rowHY['url'],'uid'=>$rowHY['user'],'key'=>$rowHY['pass']];
}
// 并发获取
$upstreamRawMap = fetchUpstreamsMulti($sources, 8);
/* ================ 全局统计与容器 ================ */
$allLogs = [
  '替换前缀'=>[], '克隆上架'=>[], '删除'=>[], '删除6月未上架'=>[],
  '更新价格'=>[], '更新说明'=>[], '更新名称'=>[], '下架'=>[], '上架'=>[], '跳过'=>[]
];
$totalCount = array_fill_keys(array_keys($allLogs), 0);
$totalSkipCount = 0;
$logOutput = [];
/* ================ 处理每个货源：一次性查本地 -> 内存比较 -> 批量写 ================ */
foreach ($huoyuanIds as $hid) {
  $rowHY = $huoyuanNameMap[$hid] ?? null;
  if (!$rowHY) continue;
  // 获取上游余额
  $apiUrlMoney = rtrim($rowHY['url'], '/') . "/api.php?act=getmoney";
  $ch = curl_init();
  curl_setopt_array($ch, [
    CURLOPT_URL => $apiUrlMoney,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query(['uid'=>$rowHY['user'], 'key'=>$rowHY['pass']]),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CONNECTTIMEOUT => 5,
    CURLOPT_TIMEOUT => 12,
  ]);
  $jsonMoney = curl_exec($ch);
  curl_close($ch);
  $dataMoney = json_decode($jsonMoney, true);
  if (isset($dataMoney['code']) && $dataMoney['code'] == 1 && isset($dataMoney['money'])) {
    $money = (float)$dataMoney['money'];
    if ($money < 30) {
      $pushTitle = "上游货源余额告警";
      $pushBody = "货源【{$rowHY['name']}】余额剩余{$money}，小于30，请站长注意加款";
      tuisong('1', "supply_balance", $pushTitle, $pushBody);
    }
  }
  // 解析上游数据
  $json = $upstreamRawMap[$hid] ?? '';
  $dataArr = json_decode($json, true);
  if (!is_array($dataArr) || ($dataArr['code'] ?? 0) != 1 || !is_array($dataArr['data'])) {
    $logOutput[] = "hid={$hid} 上游无数据/异常";
    continue;
  }
  $upstreamList = $dataArr['data'];
  $rate = isset($huoyuanPriceRate[$hid]) ? (float)$huoyuanPriceRate[$hid] : 1.0;
  $rules = $skipRule[$hid] ?? ['cidList'=>[], 'cidRange'=>[], 'fenleiList'=>[]];
  $skipCidList = array_map('intval', (array)($rules['cidList'] ?? []));
  $skipRange = (array)($rules['cidRange'] ?? []);
  $skipFenlei = array_map('intval', (array)($rules['fenleiList'] ?? []));
  $replaceArr = ($qzkg && isset($replaceRule[$hid])) ? $replaceRule[$hid] : [];
  // 上游映射：cid => item，并把名字先做前缀替换；categoryItems：上游分类 => items
  $upMap = []; // (string)cid => item
  $categoryItems = []; // (int)fenlei => items
  foreach ($upstreamList as $it) {
    $cid = (string)($it['cid'] ?? '');
    if ($cid === '') continue;
    if ($qzkg && is_array($replaceArr) && !empty($replaceArr) && isset($it['name'])) {
      foreach ($replaceArr as $rpl) {
        if (!empty($rpl['from'])) {
          $it['name'] = preg_replace('/^' . preg_quote($rpl['from'], '/') . '/u', $rpl['to'], $it['name']);
        }
      }
    }
    $upMap[$cid] = $it;
    $catid = (int)($it['fenlei'] ?? 0);
    $categoryItems[$catid][] = $it;
  }
  $logOutput[] = "hid={$hid} 上游返回数量: ".count($upMap);
  // 一次性把本地该货源的所有商品取出
  $localRows = [];
  $nounSet = []; // noun -> 1
  $getnounSet = []; // getnoun -> 1
  $maxSortByFenlei = []; // fenlei => max(sort)
  $resLocal = $DB->query("SELECT * FROM qingka_wangke_class WHERE docking='{$hid}'");
  while ($r = $DB->fetch($resLocal)) {
    $localRows[] = $r;
    $n = (string)$r['noun'];
    $gn = (string)$r['getnoun'];
    if ($n !== '') $nounSet[$n] = 1;
    if ($gn !== '') $getnounSet[$gn] = 1;
    $f = (int)$r['fenlei'];
    $s = (int)$r['sort'];
    if (!isset($maxSortByFenlei[$f]) || $s > $maxSortByFenlei[$f]) $maxSortByFenlei[$f] = $s;
  }
  // === 用“本地已上架 noun/getnoun ↔ 上游 cid”推导“上游分类 -> 本地分类”映射 ===
  $voteMap = []; // upCatId => [localFenlei => votes]
  $upToLocalMap = []; // upCatId => best localFenlei
  foreach ($localRows as $lr) {
    $status = (int)$lr['status'];
    if ($status !== 1) continue; // 只用已上架做映射锚定
    $lf = (int)$lr['fenlei'];
    $n = trim((string)$lr['noun']);
    $gn = trim((string)$lr['getnoun']);
    foreach ([$n, $gn] as $id) {
      if ($id === '') continue;
      if (!ctype_digit($id)) continue; // 只接受纯数字 id
      if (!isset($upMap[$id])) continue; // 必须能在上游命中
      $upCat = (int)($upMap[$id]['fenlei'] ?? 0);
      if ($upCat <= 0) continue;
      if (!isset($voteMap[$upCat])) $voteMap[$upCat] = [];
      if (!isset($voteMap[$upCat][$lf])) $voteMap[$upCat][$lf] = 0;
      $voteMap[$upCat][$lf]++; // 简单多数
    }
  }
  foreach ($voteMap as $upCat => $lfCounts) {
    arsort($lfCounts); // 票数降序
    $chosen = array_key_first($lfCounts);
    if ($chosen !== null) $upToLocalMap[$upCat] = (int)$chosen;
  }
  // === 映射计算完毕 ===
  // 批量SQL容器
  $sqlPriceUpdates = [];
  $sqlContentUpdates = [];
  $sqlNameUpdates = [];
  $sqlStatusUpdates = [];
  $sqlDeletes = [];
  $nowDatetime = date('Y-m-d H:i:s');
  // 批量日志容器
  $logRows = [];
  // 遍历本地：对比 & 记录变更（先不执行）
  foreach ($localRows as $row) {
    $localCid = (int)$row['cid'];
    $localFenlei = (int)$row['fenlei'];
    $noun = (string)$row['noun'];
    $nounInt = (int)$noun;
    // 跳过规则
    $skip = false;
    if ($tgkg) {
      if ($skipCidList && in_array($nounInt, $skipCidList, true)) $skip = true;
      if (!$skip && count($skipRange) == 2) {
        $r0 = (int)$skipRange[0]; $r1 = (int)$skipRange[1];
        if ($nounInt >= $r0 && $nounInt <= $r1) $skip = true;
      }
      if (!$skip && $skipFenlei && in_array($localFenlei, $skipFenlei, true)) $skip = true;
    }
    if ($skip) {
      $totalSkipCount++; $totalCount['跳过']++;
      $allLogs['跳过'][] = [
        'cid'=>$row['cid'],'name'=>$row['name'],'price'=>$row['price'],'content'=>$row['content'],
        'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],
        'before'=>"CID:{$row['cid']}, 名称:{$row['name']}, 价格:{$row['price']}, 说明:{$row['content']}, 状态:{$row['status']}",
        'after'=>'','mode'=>$modeDesc
      ];
      // 跳过类永不推送，固定写 2
      writeDbLogRows($logRows, $row, '跳过', $row['status'], '', '', 2, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      continue;
    }
    // === 上游不存在 -> 按下架处理（与“上游返回status=0”一致）：仅当本地是上架时才下架并记日志 ===
    if (!isset($upMap[$noun])) {
      if ($tbztkg && (int)$row['status'] != 0) {
        $sqlStatusUpdates[] = "UPDATE qingka_wangke_class SET status=0, addtime='".db_escape($DB,$nowDatetime)."' WHERE cid='{$localCid}'";
        $totalCount['下架']++;
        $allLogs['下架'][] = [
          'cid'=>$row['cid'],'name'=>$row['name'],'before'=>$row['status'],'after'=>0,
          'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
        ];
        writeDbLogRows($logRows, $row, '下架', $row['status'], 0, '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      }
      continue; // 不再进入后续对比逻辑
    }
    // 对比更新
    $item = $upMap[$noun];
    $upName = (string)($item['name'] ?? '');
    $upCont = (string)($item['content'] ?? '');
    $upPrice = (float)($item['price'] ?? 0);
    $upStatus= (int)($item['status'] ?? 1);
    // 分类倍率
    $catPriceRate = $rate;
    if (isset($huoyuanCategoryPrice[$hid][$localFenlei])) {
      $categoryRate = (float)$huoyuanCategoryPrice[$hid][$localFenlei];
      if ($categoryRate !== $rate) $catPriceRate = $categoryRate;
    }
    // === 价格：先算再保留2位比较 ===
    $newPriceNum = round($upPrice * $catPriceRate, 2);
    $oldPriceNum = round((float)$row['price'], 2);
    if ($tbjgkg && $qzdqjg==1) {
      // 奸商模式：只升不降（两位小数比较）
      if ($newPriceNum > $oldPriceNum) {
        $sqlPriceUpdates[] = "UPDATE qingka_wangke_class SET price='".number_format($newPriceNum,2,'.','')."' WHERE cid='{$localCid}'";
        $totalCount['更新价格']++;
        $allLogs['更新价格'][] = [
          'cid'=>$row['cid'],'name'=>$row['name'],'before'=>number_format($oldPriceNum,2,'.',''),'after'=>number_format($newPriceNum,2,'.',''),
          'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
        ];
        writeDbLogRows($logRows, $row, '更新价格', number_format($oldPriceNum,2,'.',''), number_format($newPriceNum,2,'.',''), '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      }
    } else if ($tbjgkg) {
      // 良心模式：不相等就更新（两位小数比较）
      if ($newPriceNum != $oldPriceNum) {
        $sqlPriceUpdates[] = "UPDATE qingka_wangke_class SET price='".number_format($newPriceNum,2,'.','')."' WHERE cid='{$localCid}'";
        $totalCount['更新价格']++;
        $allLogs['更新价格'][] = [
          'cid'=>$row['cid'],'name'=>$row['name'],'before'=>number_format($oldPriceNum,2,'.',''),'after'=>number_format($newPriceNum,2,'.',''),
          'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
        ];
        writeDbLogRows($logRows, $row, '更新价格', number_format($oldPriceNum,2,'.',''), number_format($newPriceNum,2,'.',''), '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      }
    }
    // 说明（完全不同才更新）
    if ($tbsmkg && $row['content'] !== $upCont) {
      $sqlContentUpdates[] = "UPDATE qingka_wangke_class SET content='".db_escape($DB,$upCont)."' WHERE cid='{$localCid}'";
      $totalCount['更新说明']++;
      $allLogs['更新说明'][] = [
        'cid'=>$row['cid'],'name'=>$row['name'],'before'=>$row['content'],'after'=>$upCont,
        'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
      ];
      writeDbLogRows($logRows, $row, '更新说明', $row['content'], $upCont, '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
    }
    // 名称
    if ($tbsjmc && $row['name'] !== $upName) {
      $sqlNameUpdates[] = "UPDATE qingka_wangke_class SET name='".db_escape($DB,$upName)."' WHERE cid='{$localCid}'";
      $totalCount['更新名称']++;
      $allLogs['更新名称'][] = [
        'cid'=>$row['cid'],'before'=>$row['name'],'after'=>$upName,
        'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
      ];
      writeDbLogRows($logRows, $row, '更新名称', $row['name'], $upName, '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
    }
    // 上下架
    if ($tbztkg) {
      if ($upStatus == 0 && (int)$row['status'] != 0) {
        $sqlStatusUpdates[] = "UPDATE qingka_wangke_class SET status=0, addtime='".db_escape($DB,$nowDatetime)."' WHERE cid='{$localCid}'";
        $totalCount['下架']++;
        $allLogs['下架'][] = [
          'cid'=>$row['cid'],'name'=>$row['name'],'before'=>$row['status'],'after'=>0,
          'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
        ];
        writeDbLogRows($logRows, $row, '下架', $row['status'], 0, '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      }
      if ($upStatus == 1 && (int)$row['status'] == 0) {
        $sqlStatusUpdates[] = "UPDATE qingka_wangke_class SET status=1, addtime='".db_escape($DB,$nowDatetime)."' WHERE cid='{$localCid}'";
        $totalCount['上架']++;
        $allLogs['上架'][] = [
          'cid'=>$row['cid'],'name'=>$row['name'],'before'=>$row['status'],'after'=>1,
          'docking'=>$row['docking'],'fenlei'=>$row['fenlei'],'mode'=>$modeDesc
        ];
        writeDbLogRows($logRows, $row, '上架', $row['status'], 1, '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$localFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      }
    }
  }
  /* ============ 克隆上架（映射法）：上游分类 -> 本地分类 ============ */
  if ($klsjkg) {
    foreach ($categoryItems as $upCatId => $catItems) {
      // 先根据映射决定最优本地分类；未找到映射则直接跳过整类（不记任何日志，减少写入）
      if (!isset($upToLocalMap[$upCatId]) || (int)$upToLocalMap[$upCatId] <= 0) {
        continue; // 不写日志、不推送
      }
      $bestFenlei = (int)$upToLocalMap[$upCatId];
      // 得到上游cid集合（纯数字）
      $upCids = [];
      foreach ($catItems as $it) {
        $cidStr = trim((string)($it['cid'] ?? ''));
        if ($cidStr !== '' && ctype_digit($cidStr)) $upCids[$cidStr] = 1;
      }
      if (!$upCids) continue;
      // 组装待插入
      $insertRows = []; // 多值插入
      foreach ($catItems as $it) {
        $noun = trim((string)($it['cid'] ?? ''));
        if ($noun === '' || !ctype_digit($noun)) continue;
        // 已存在则跳过（noun 或 getnoun 命中）
        if (isset($nounSet[$noun]) || isset($getnounSet[$noun])) continue;
        // 跳过规则（以映射后的 bestFenlei 验证）
        $skip = false;
        if ($tgkg) {
          $nounInt = (int)$noun;
          if ($skipCidList && in_array($nounInt, $skipCidList, true)) $skip = true;
          if (!$skip && count($skipRange) == 2) {
            $r0 = (int)$skipRange[0]; $r1 = (int)$skipRange[1];
            if ($nounInt >= $r0 && $nounInt <= $r1) $skip = true;
          }
          if (!$skip && $skipFenlei && in_array($bestFenlei, $skipFenlei, true)) $skip = true;
        }
        if ($skip) {
          // 正常的“规则跳过”仍记录（方便排查）
          $totalSkipCount++; $totalCount['跳过']++;
          $allLogs['跳过'][] = [
            'cid'=>0,'name'=>($it['name'] ?? ''),'price'=>($it['price'] ?? ''),'content'=>($it['content'] ?? ''),
            'docking'=>$hid,'fenlei'=>$bestFenlei,
            'before'=>"CID:0, 名称:".($it['name']??'').", 价格:".($it['price']??'').", 说明:".($it['content']??'').", 状态:0",
            'after'=>'','mode'=>$modeDesc
          ];
          writeDbLogRows($logRows, ['cid'=>0,'docking'=>$hid,'fenlei'=>$bestFenlei,'name'=>($it['name'] ?? ''),'content'=>($it['content'] ?? ''),'price'=>($it['price'] ?? '')], '跳过', '', '', '', 2, $modeDesc, ($fenleiNameMap[$bestFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
          continue;
        }
        // 名称替换（稳妥处理一次）
        $upName = (string)($it['name'] ?? '');
        if ($qzkg && is_array($replaceArr) && !empty($replaceArr) && $upName) {
          foreach ($replaceArr as $rpl) if (!empty($rpl['from'])) {
            $upName = preg_replace('/^' . preg_quote($rpl['from'], '/') . '/u', $rpl['to'], $upName);
          }
        }
        // sort：用缓存的 maxSortByFenlei 自增
        $maxSortByFenlei[$bestFenlei] = ($maxSortByFenlei[$bestFenlei] ?? 0) + 1;
        $newSort = $maxSortByFenlei[$bestFenlei];
        // 价格（使用分类倍率，最后两位小数）
        $catPriceRate = $rate;
        if (isset($huoyuanCategoryPrice[$hid][$bestFenlei])) {
          $categoryRate = (float)$huoyuanCategoryPrice[$hid][$bestFenlei];
          if ($categoryRate !== $rate) $catPriceRate = $categoryRate;
        }
        $calcPrice = number_format(round((float)($it['price'] ?? 0) * (float)$catPriceRate, 2), 2, '.', '');
        $content = (string)($it['content'] ?? '');
        $insertRows[] = [
          'sort' => $newSort,
          'name' => $upName,
          'queryplat' => $hid,
          'getnoun' => $noun,
          'docking' => $hid,
          'noun' => $noun,
          'price' => $calcPrice,
          'yunsuan' => '*',
          'content' => $content,
          'addtime' => $nowDatetime,
          'status' => 1,
          'fenlei' => $bestFenlei,
          'uptime' => $nowDatetime,
        ];
        $totalCount['克隆上架']++;
        $allLogs['克隆上架'][] = [
          'cid'=>0,'fenlei'=>$bestFenlei,'name'=>$upName,'price'=>$calcPrice,'content'=>$content,'docking'=>$hid,'mode'=>$modeDesc
        ];
        writeDbLogRows($logRows, ['cid'=>0,'docking'=>$hid,'fenlei'=>$bestFenlei,'name'=>$upName,'content'=>$content,'price'=>$calcPrice], '克隆上架', '', ['name'=>$upName,'price'=>$calcPrice,'content'=>$content], '', $defaultPushStatus, $modeDesc, ($fenleiNameMap[$bestFenlei] ?? ''), ($huoyuanNameMap[$hid]['name'] ?? ''));
      }
      // 批量插入克隆上的数据
      if ($insertRows) {
        $DB->query("START TRANSACTION");
        $columns = ['sort','name','queryplat','getnoun','docking','noun','price','yunsuan','content','addtime','status','fenlei','uptime'];
        $values = [];
        foreach ($insertRows as $r) {
          $values[] = [
            $r['sort'], $r['name'], $r['queryplat'], $r['getnoun'], $r['docking'], $r['noun'],
            $r['price'], $r['yunsuan'], $r['content'], $r['addtime'], $r['status'], $r['fenlei'], $r['uptime']
          ];
        }
        // 多值插入（200条/批）
        $chunk = 200; $n = count($values);
        for ($i=0; $i<$n; $i+=$chunk) {
          $end = min($n, $i+$chunk);
          $vals = [];
          for ($j=$i; $j<$end; $j++) {
            $v = $values[$j];
            $vals[] = "("
              . (int)$v[0] . ","
              . "'" . db_escape($DB,$v[1]) . "',"
              . (int)$v[2] . ","
              . "'" . db_escape($DB,$v[3]) . "',"
              . (int)$v[4] . ","
              . "'" . db_escape($DB,$v[5]) . "',"
              . "'" . db_escape($DB,$v[6]) . "',"
              . "'" . db_escape($DB,$v[7]) . "',"
              . "'" . db_escape($DB,$v[8]) . "',"
              . "'" . db_escape($DB,$v[9]) . "',"
              . (int)$v[10] . ","
              . (int)$v[11] . ","
              . "'" . db_escape($DB,$v[12]) . "')";
          }
          $sql = "INSERT INTO qingka_wangke_class (sort,name,queryplat,getnoun,docking,noun,price,yunsuan,content,addtime,status,fenlei,uptime) VALUES "
               . implode(',', $vals);
          $DB->query($sql);
        }
        $DB->query("COMMIT");
      }
    }
  }
  /* ============ 批量执行本地更新/删除（开启事务，减少往返） ============ */
  $DB->query("START TRANSACTION");
  foreach (array_chunk($sqlPriceUpdates, 200) as $chunk) { foreach ($chunk as $sql) $DB->query($sql); }
  foreach (array_chunk($sqlContentUpdates, 200) as $chunk) { foreach ($chunk as $sql) $DB->query($sql); }
  foreach (array_chunk($sqlNameUpdates, 200) as $chunk) { foreach ($chunk as $sql) $DB->query($sql); }
  foreach (array_chunk($sqlStatusUpdates, 200) as $chunk) { foreach ($chunk as $sql) $DB->query($sql); }
  foreach (array_chunk($sqlDeletes, 200) as $chunk) { foreach ($chunk as $sql) $DB->query($sql); }
  $DB->query("COMMIT");
  /* ============ 批量写 class_log ============ */
  if (!empty($logRows)) {
    $columns = ['product_id','huoyuan_id','huoyuan_name','platform_name','fenlei_id','fenlei_name','action','data_before','data_after','operator','op_time','remark','push_status'];
    $rows = [];
    foreach ($logRows as $lr) {
      $rows[] = [
        $lr['product_id'], $lr['huoyuan_id'], $lr['huoyuan_name'], $lr['platform_name'],
        $lr['fenlei_id'], $lr['fenlei_name'], $lr['action'], $lr['data_before'], $lr['data_after'],
        $lr['operator'], $lr['op_time'], $lr['remark'], $lr['push_status']
      ];
    }
    batchInsert($DB, 'qingka_wangke_class_log', $columns, $rows, 200);
  }
}
/* ============ 6个月未上架自动删除（可选） ============ */
if ($zmsjkg) {
  $sixMonthsAgo = date('Y-m-d H:i:s', strtotime('-180 days'));
  $res = $DB->query("SELECT * FROM qingka_wangke_class WHERE status=0 AND uptime < '{$sixMonthsAgo}'");
  $toDel = []; $logRows = [];
  while ($r = $DB->fetch($res)) {
    $toDel[] = (int)$r['cid'];
    $allLogs['删除6月未上架'][] = [
      'cid'=>$r['cid'],'name'=>$r['name'],'price'=>$r['price'],'content'=>$r['content'],
      'docking'=>$r['docking'],'fenlei'=>$r['fenlei'],
      'before'=>"CID:{$r['cid']}, 名称:{$r['name']}, 价格:{$r['price']}, 说明:{$r['content']}, 状态:{$r['status']}"
    ];
    $totalCount['删除6月未上架']++;
    writeDbLogRows($logRows, $r, '删除6月未上架', $r['status'], '', '', $defaultPushStatus,$modeDesc,'', ''
    );
  }
  if ($toDel) {
    $DB->query("START TRANSACTION");
    foreach (array_chunk($toDel, 500) as $chunk) {
      $ids = implode(',', array_map('intval', $chunk));
      $DB->query("DELETE FROM qingka_wangke_class WHERE cid IN ($ids)");
    }
    $DB->query("COMMIT");
  }
  if (!empty($logRows)) {
    $columns = ['product_id','huoyuan_id','huoyuan_name','platform_name','fenlei_id','fenlei_name','action','data_before','data_after','operator','op_time','remark','push_status'];
    $rows = [];
    foreach ($logRows as $lr) {
      $rows[] = [
        $lr['product_id'], $lr['huoyuan_id'], $lr['huoyuan_name'], $lr['platform_name'],
        $lr['fenlei_id'], $lr['fenlei_name'], $lr['action'], $lr['data_before'], $lr['data_after'],
        $lr['operator'], $lr['op_time'], $lr['remark'], $lr['push_status']
      ];
    }
    batchInsert($DB, 'qingka_wangke_class_log', $columns, $rows, 200);
  }
}
/* ============ 推送（按类别汇总一次） ============ */
$cats = ['替换前缀', '克隆上架', '更新价格', '更新说明', '更新名称', '下架', '上架', '删除', '删除6月未上架'];
$pushTitle = "货源自动监控通知";
$summary = [];
foreach ($cats as $cat) {
    if (empty($allLogs[$cat])) continue;
    $needPush = $pushEnabled;
    $pushStatus = 2;
    if ($needPush) {
        $count = count($allLogs[$cat]);
        $summary[] = "{$cat}: {$count}条";
        $catEsc = db_escape($DB, $cat);
        $timeEsc = db_escape($DB, $batchTimeStr);
        $sqlUpdateLog = "UPDATE qingka_wangke_class_log
                         SET push_status=1
                         WHERE action='{$catEsc}'
                           AND op_time >= '{$timeEsc}'
                           AND push_status=0";
        $DB->query($sqlUpdateLog);
    }
}
$pushBody = "【货源变更通知】\n时间：" . date('Y-m-d H:i:s') . "\n\n" .
            implode("\n", $summary) . "\n\n本次同步自动执行";
if (!empty($summary)) {
    tuisong('1', "supply_update", $pushTitle, $pushBody);
}
/* ============ 控制台/文件日志输出 ============ */
foreach (array_keys($allLogs) as $cat) {
  if ($totalCount[$cat] > 0) {
    $logOutput[] = "【{$cat}】共 {$totalCount[$cat]} 条：";
    foreach ($allLogs[$cat] as $row) {
      $modeLabel = (isset($row['mode']) && $row['mode']) ? "【{$row['mode']}】" : '';
      $fenleiName = $fenleiNameMap[(int)($row['fenlei'] ?? 0)] ?? '';
      $hid = (int)($row['docking'] ?? 0);
      $HY = "[" . ($huoyuanNameMap[$hid]['name'] ?? ("HID:".$hid)) . "]";
      if ($cat == '替换前缀') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——分类【{$fenleiName}】——旧名称【{$row['before']}】 替换为新名称【{$row['after']}】";
      } elseif ($cat == '更新价格') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——名称【{$row['name']}】——分类【{$fenleiName}】——本地价格【{$row['before']}】 更新后价格【{$row['after']}】";
      } elseif ($cat == '更新名称') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——本地名称【{$row['before']}】——分类【{$fenleiName}】 更新后名称【{$row['after']}】";
      } elseif ($cat == '更新说明') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——名称【{$row['name']}】——分类【{$fenleiName}】——本地说明【{$row['before']}】 更新后说明【{$row['after']}】";
      } elseif ($cat == '下架') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——名称【{$row['name']}】——分类【{$fenleiName}】 下架";
      } elseif ($cat == '上架') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——名称【{$row['name']}】——分类【{$fenleiName}】 上架";
      } elseif ($cat == '删除' || $cat == '删除6月未上架') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——名称【{$row['name']}】——分类【{$fenleiName}】——价格【{$row['price']}】 说明【{$row['content']}】";
      } elseif ($cat == '克隆上架') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 分类【{$fenleiName}】 商品【{$row['cid']}】——名称【{$row['name']}】——价格【{$row['price']}】";
      } elseif ($cat == '跳过') {
        $logOutput[] = "{$HY}{$modeLabel} hid={$hid} 商品【{$row['cid']}】——名称【{$row['name']}】——分类【{$fenleiName}】——价格【{$row['price']}】 说明【{$row['content']}】（已跳过）";
      }
    }
    $logOutput[] = "";
  }
}
$logOutput[] = "=========== 同步完成: 配置开关状态 ==========";
$logOutput[] = "是否更新价格：".($tbjgkg ? '是' : '否');
$logOutput[] = "是否更新说明：".($tbsmkg ? '是' : '否');
$logOutput[] = "是否更新名称：".($tbsjmc ? '是' : '否');
$logOutput[] = "是否更新状态：".($tbztkg ? '是' : '否');
$logOutput[] = "是否克隆上架：".($klsjkg ? '是' : '否');
$logOutput[] = "前缀规则：".($qzkg ? '启用' : '未启用')." 跳过规则：".($tgkg ? '启用' : '未启用');
$logOutput[] = "";
$logOutput[] = "当前同步运行模式：{$modeDesc}";
$logOutput[] = "";
$logOutput[] = "统计更新：";
foreach (array_keys($allLogs) as $cat) {
  $logOutput[] = "{$cat}：{$totalCount[$cat]} 条";
}
$logOutput[] = "本次跳过：{$totalSkipCount} 条";
$endTime = microtime(true);
$usedTime = round($endTime - $startTime, 2);
$logOutput[] = "本次同步总用时：{$usedTime}s";
$logOutput[] = "\n脚本执行完毕.\n";
echo implode("\n", $logOutput)."\n";
$newBlock = "=============================\n"
          . implode("\n", $logOutput)
          . "\n### LastSyncTimestamp=" . date('Y-m-d H:i:s') . "\n"
          . "-----------------------------\n";
$oldLog = file_exists($logFile) ? file_get_contents($logFile) : '';
$fullLog = $newBlock . $oldLog;
file_put_contents($logFile, $fullLog);
?>