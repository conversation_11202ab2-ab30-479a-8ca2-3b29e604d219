<?php
/**
 * 易教育API接口文件
 * 兼容原始易教育对接格式
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 获取请求参数
$act = $_GET['act'] ?? $_GET['action'] ?? '';

// 获取易教育货源配置
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$huoyuan) {
    exit(json_encode(array("code" => -1, "msg" => "未找到易教育货源配置")));
}

$jk = $huoyuan;  // 兼容原始变量名
$header = array("Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $jk['token']);
$now_time = date("Y-m-d H:i:s");

// 获取用户信息（如果有会话）
$userrow = array();
if (isset($_SESSION['userrow'])) {
    $userrow = $_SESSION['userrow'];
} else {
    // 模拟用户信息（用于测试）
    $userrow = array('uid' => 1, 'addprice' => 1);
}

$clientip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

/**
 * 易教育API管理类
 */
class JxjyAPI {
    private $db;
    private $config;
    private $token;
    
    public function __construct() {
        global $DB;
        $this->db = $DB;
        $this->loadConfig();
    }
    
    /**
     * 加载易教育配置
     */
    private function loadConfig() {
        $config = $this->db->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' AND status = 1 LIMIT 1");
        if (!$config) {
            throw new Exception("易教育配置未找到或已禁用");
        }
        $this->config = $config;
    }
    
    /**
     * 获取访问Token
     */
    private function getToken() {
        // 检查缓存的token是否有效
        if (!empty($this->config['token'])) {
            if ($this->validateToken($this->config['token'])) {
                $this->token = $this->config['token'];
                return $this->token;
            }
        }
        
        // 重新登录获取token
        $login_data = array(
            "username" => $this->config['user'],
            "password" => $this->config['pass']
        );
        
        $login_url = $this->config['url'] . "/api/login";
        $response = $this->sendRequest($login_url, $login_data, 'POST');
        
        if (!$response || $response['code'] != 200) {
            throw new Exception("登录失败：" . ($response['message'] ?? '未知错误'));
        }
        
        $this->token = $response['data']['token'];
        
        // 更新数据库中的token
        $this->db->query("UPDATE qingka_wangke_huoyuan SET token = '{$this->token}' WHERE hid = '{$this->config['hid']}'");
        
        return $this->token;
    }
    
    /**
     * 验证Token是否有效
     */
    private function validateToken($token) {
        $test_url = $this->config['url'] . "/api/user/info";
        $response = $this->sendRequest($test_url, null, 'GET', $token);
        return $response && $response['code'] == 200;
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendRequest($url, $data = null, $method = 'GET', $token = null) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $headers = array("Content-Type: application/json");
        if ($token) {
            $headers[] = "Authorization: Bearer " . $token;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        if ($method == 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            throw new Exception("网络请求失败：" . $curl_error);
        }
        
        return json_decode($response, true);
    }
    
    /**
     * 同步项目列表
     */
    public function syncProjects() {
        $token = $this->getToken();
        
        // 获取网站列表
        $list_data = array("pageSize" => 100, "pageNum" => 1);
        $list_url = $this->config['url'] . "/api/website/list";
        $response = $this->sendRequest($list_url, $list_data, 'POST', $token);
        
        if (!$response || $response['code'] != 200) {
            throw new Exception("获取项目列表失败：" . ($response['message'] ?? '未知错误'));
        }
        
        // 易教育API数据直接在data数组中
        $projects = $response['data'];
        $sync_count = 0;
        
        foreach ($projects as $project) {
            $project_data = array(
                'id' => $project['id'],
                'number' => $project['number'],
                'name' => $project['name'],
                'format' => $project['format'] ?? '账号 密码',
                'url' => $project['url'] ?? '',
                'price' => floatval($project['price'] ?? 0),
                'unitId' => $project['unitId'] ?? '1',
                'must' => $project['must'] ?? '1',
                'unit' => $project['unit'] ?? '年度',
                'rate' => $project['rate'] ?? '1',
                'isExam' => $project['isExam'] ?? '0',
                'isSearchCourse' => $project['isSearchCourse'] ?? '1',
                'status' => 1,
                'description' => $project['description'] ?? '',
                'remark' => $project['remark'] ?? ''
            );
            
            // 检查项目是否已存在
            $existing = $this->db->get_row("SELECT id FROM qingka_wangke_jxjyclass WHERE id = '{$project['id']}'");
            
            if ($existing) {
                // 更新现有项目
                $update_sql = "UPDATE qingka_wangke_jxjyclass SET 
                    number = '{$project_data['number']}',
                    name = '{$project_data['name']}',
                    format = '{$project_data['format']}',
                    url = '{$project_data['url']}',
                    price = {$project_data['price']},
                    unitId = '{$project_data['unitId']}',
                    must = '{$project_data['must']}',
                    unit = '{$project_data['unit']}',
                    rate = '{$project_data['rate']}',
                    isExam = '{$project_data['isExam']}',
                    isSearchCourse = '{$project_data['isSearchCourse']}',
                    description = '{$project_data['description']}',
                    remark = '{$project_data['remark']}'
                    WHERE id = '{$project['id']}'";
                $this->db->query($update_sql);
            } else {
                // 插入新项目
                $insert_sql = "INSERT INTO qingka_wangke_jxjyclass (
                    id, number, name, format, url, price, unitId, must, unit, 
                    rate, isExam, isSearchCourse, status, description, remark
                ) VALUES (
                    '{$project_data['id']}', '{$project_data['number']}', '{$project_data['name']}', 
                    '{$project_data['format']}', '{$project_data['url']}', {$project_data['price']}, 
                    '{$project_data['unitId']}', '{$project_data['must']}', '{$project_data['unit']}', 
                    '{$project_data['rate']}', '{$project_data['isExam']}', '{$project_data['isSearchCourse']}', 
                    {$project_data['status']}, '{$project_data['description']}', '{$project_data['remark']}'
                )";
                $this->db->query($insert_sql);
            }
            
            $sync_count++;
        }
        
        return array("code" => 1, "msg" => "项目同步完成", "count" => $sync_count);
    }
    
    /**
     * 同步订单状态
     */
    public function syncOrderStatus() {
        $token = $this->getToken();
        
        // 获取需要同步的订单（状态不是完成的）
        $orders = $this->db->get_rows("SELECT * FROM qingka_wangke_jxjyorder WHERE status NOT IN ('完成', '已退款') ORDER BY addtime DESC LIMIT 50");
        
        $sync_count = 0;
        
        foreach ($orders as $order) {
            try {
                // 查询订单状态
                $query_data = array(
                    "pageSize" => 10,
                    "pageNum" => 1,
                    "status" => "all",
                    "websiteNumber" => $order['noun'],
                    "orderNumber" => $order['yid'],
                    "username" => $order['user'],
                    "name" => ""
                );
                
                $query_url = $this->config['url'] . "/api/order/list";
                $response = $this->sendRequest($query_url, $query_data, 'POST', $token);
                
                if ($response && $response['code'] == 200 && isset($response['data']['list'])) {
                    foreach ($response['data']['list'] as $api_order) {
                        if ($api_order['number'] == $order['yid']) {
                            // 状态映射
                            $status_mapping = array(
                                0 => '队列中',
                                1 => '完成',
                                2 => '运行中',
                                3 => '异常',
                                4 => '正在售后',
                                5 => '售后完成',
                                6 => '人工处理',
                                7 => '已退款',
                                8 => '暂停中',
                                9 => '已暂停'
                            );
                            
                            $new_status = $status_mapping[$api_order['status']] ?? '未知状态';
                            $new_process = $api_order['progress'] ?? '0%';
                            $new_remarks = $api_order['remark'] ?? '';
                            
                            // 更新订单状态
                            $update_sql = "UPDATE qingka_wangke_jxjyorder SET 
                                status = '{$new_status}',
                                process = '{$new_process}',
                                remarks = '{$new_remarks}',
                                updatetime = NOW()
                                WHERE oid = '{$order['oid']}'";
                            $this->db->query($update_sql);
                            
                            $sync_count++;
                            break;
                        }
                    }
                }
            } catch (Exception $e) {
                // 记录错误但继续处理其他订单
                error_log("同步订单 {$order['oid']} 失败：" . $e->getMessage());
            }
        }
        
        return array("code" => 1, "msg" => "订单状态同步完成", "count" => $sync_count);
    }
    
    /**
     * 获取用户余额信息
     */
    public function getUserInfo() {
        $token = $this->getToken();

        $info_url = $this->config['url'] . "/api/user/info";
        $response = $this->sendRequest($info_url, null, 'GET', $token);

        if (!$response || $response['code'] != 200) {
            throw new Exception("获取用户信息失败：" . ($response['message'] ?? '未知错误'));
        }

        return $response['data'];
    }

    /**
     * 查课接口
     */
    public function queryCourse($websiteNumber, $username, $password) {
        $token = $this->getToken();

        // 发起查课请求
        $query_data = array(
            "websiteNumber" => $websiteNumber,
            "data" => array(array("username" => $username, "password" => $password))
        );

        $query_url = $this->config['url'] . "/api/website/queryCourse";
        $response = $this->sendRequest($query_url, $query_data, 'POST', $token);

        if (!$response || $response['code'] != 200) {
            return array(array("code" => -1, "msg" => "发送查课请求失败，请联系管理员"));
        }

        $uuid = $response['data']['uuid'];

        // 等待5秒后获取查课结果
        sleep(5);

        $result_data = array("uuid" => $uuid);
        $result_url = $this->config['url'] . "/api/website/getQueryCourse";
        $result_response = $this->sendRequest($result_url, $result_data, 'POST', $token);

        if (!$result_response || $result_response['code'] != 200) {
            return array(array("code" => -1, "msg" => "获取查课结果失败"));
        }

        // 检查登录是否成功
        if (isset($result_response['data'][0]['name']) && strpos($result_response['data'][0]['name'], "登录失败") !== false) {
            return array(array("code" => -1, "msg" => $result_response['data'][0]['name']));
        }

        // 解析课程信息
        if (isset($result_response['data'][0]['children'])) {
            $children = $result_response['data'][0]['children'];
            $filteredData = array();

            foreach ($children as $child) {
                if (isset($child['id']) && isset($child['name'])) {
                    $filteredData[] = array("id" => $child['id'], "name" => $child['name']);
                } elseif (isset($child['name']) && isset($child['children'])) {
                    foreach ($child['children'] as $subChild) {
                        if (isset($subChild['id']) && isset($subChild['name'])) {
                            $filteredData[] = array("id" => $subChild['id'], "name" => $child['name'] . "----" . $subChild['name']);
                        }
                    }
                }
            }

            $userinfo = $result_response['data'][0]['name'] ?? '';
            return array(array("code" => 1, "data" => $filteredData, "msg" => "查询成功", "userinfo" => $userinfo));
        }

        return array(array("code" => -1, "msg" => "未找到课程信息"));
    }
}

// 处理API请求
try {
    $action = $_GET['action'] ?? $_GET['act'] ?? 'info';
    $api = new JxjyAPI();

    switch ($action) {
        case 'sync_projects':
            $result = $api->syncProjects();
            break;

        case 'sync_orders':
            $result = $api->syncOrderStatus();
            break;

        case 'user_info':
            $result = array("code" => 1, "msg" => "获取成功", "data" => $api->getUserInfo());
            break;

        case 'jxjyclass':
            // 获取易教育项目列表（前端使用）
            $projects_result = $DB->query("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 ORDER BY id ASC");
            $data = array();

            if ($projects_result) {
                while ($row = $DB->fetch_array($projects_result)) {
                    // 根据用户倍率计算价格
                    if (isset($_SESSION['userrow']['addprice'])) {
                        $price = $row['price'] * $_SESSION['userrow']['addprice'];
                        $row['price'] = number_format($price, 2);
                    }
                    $data[] = $row;
                }
            }

            $result = array('code' => 1, 'data' => $data);
            break;

        case 'getcourse':
            // 易教育查课接口
            $id = trim(strip_tags($_POST['id'] ?? ''));
            $username = trim(strip_tags($_POST['user'] ?? ''));
            $password = trim(strip_tags($_POST['pass'] ?? ''));

            if (empty($id) || empty($username) || empty($password)) {
                $result = array(array("code" => -1, "msg" => "参数不能为空"));
                break;
            }

            // 使用项目ID查询项目信息（前端传递的是项目ID）
            $project = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$id}' LIMIT 1");
            if (!$project) {
                $result = array(array("code" => -1, "msg" => "项目信息不存在，项目ID: {$id}"));
                break;
            }

            // 记录查课日志
            if (isset($_SESSION['userrow']['uid'])) {
                wlog($_SESSION['userrow']['uid'], "继续教育查课", "平台{$project['name']} {$username} {$password}", 0);
            }

            // 检查是否需要查课
            if ($project['isSearchCourse'] == '0') {
                $result = array(array("code" => -1, "msg" => "该项目无需查课"));
                break;
            }

            // 调用查课接口
            $result = $api->queryCourse($project['number'], $username, $password);
            break;

        case 'info':
        default:
            $result = array(
                "code" => 1,
                "msg" => "易教育API同步脚本",
                "actions" => array(
                    "sync_projects" => "同步项目列表",
                    "sync_orders" => "同步订单状态",
                    "user_info" => "获取用户信息",
                    "getcourse" => "查课接口"
                )
            );
            break;
    }
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(array("code" => -1, "msg" => $e->getMessage()), JSON_UNESCAPED_UNICODE);
}
?>
