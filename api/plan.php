<?php
/**
 * 易教育计划任务脚本
 * 功能：自动登录、商品上架、订单同步等计划任务
 * 创建时间：2025-08-21
 */

// 引入数据库配置
include_once '../confing/mysqlset.php';
include_once '../confing/common.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

/**
 * 易教育计划任务管理类
 */
class JxjyPlan {
    private $db;
    private $config;
    private $log_file;
    
    public function __construct() {
        global $DB;
        $this->db = $DB;
        $this->log_file = '../logs/jxjy_plan_' . date('Y-m-d') . '.log';
        $this->loadConfig();
    }
    
    /**
     * 加载易教育配置
     */
    private function loadConfig() {
        $config = $this->db->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' AND status = 1 LIMIT 1");
        if (!$config) {
            throw new Exception("易教育配置未找到或已禁用");
        }
        $this->config = $config;
    }
    
    /**
     * 记录日志
     */
    private function log($message) {
        $log_message = date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
        
        // 确保日志目录存在
        $log_dir = dirname($this->log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        file_put_contents($this->log_file, $log_message, FILE_APPEND | LOCK_EX);
        echo $log_message;
    }
    
    /**
     * 自动登录并更新Token
     */
    public function autoLogin() {
        try {
            $this->log("开始自动登录易教育平台...");
            
            $login_data = array(
                "username" => $this->config['user'],
                "password" => $this->config['pass']
            );
            
            $login_url = $this->config['url'] . "/api/login";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($curl_error) {
                throw new Exception("网络请求失败：" . $curl_error);
            }
            
            $result = json_decode($response, true);
            
            if (!$result || $result['code'] != 200) {
                throw new Exception("登录失败：" . ($result['message'] ?? '未知错误'));
            }
            
            $token = $result['data']['token'];
            
            // 更新数据库中的token
            $this->db->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$this->config['hid']}'");
            
            $this->log("自动登录成功，Token已更新");
            return true;
            
        } catch (Exception $e) {
            $this->log("自动登录失败：" . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 自动同步商品信息
     */
    public function autoSyncProducts() {
        try {
            $this->log("开始自动同步商品信息...");
            
            // 调用API同步脚本
            $api_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/jxjyapi.php?action=sync_projects";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($curl_error) {
                throw new Exception("调用同步API失败：" . $curl_error);
            }
            
            $result = json_decode($response, true);
            
            if (!$result || $result['code'] != 1) {
                throw new Exception("商品同步失败：" . ($result['msg'] ?? '未知错误'));
            }
            
            $this->log("商品同步成功，同步数量：" . ($result['count'] ?? 0));
            return true;
            
        } catch (Exception $e) {
            $this->log("商品同步失败：" . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 自动同步订单状态
     */
    public function autoSyncOrders() {
        try {
            $this->log("开始自动同步订单状态...");
            
            // 调用API同步脚本
            $api_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/jxjyapi.php?action=sync_orders";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($curl_error) {
                throw new Exception("调用同步API失败：" . $curl_error);
            }
            
            $result = json_decode($response, true);
            
            if (!$result || $result['code'] != 1) {
                throw new Exception("订单同步失败：" . ($result['msg'] ?? '未知错误'));
            }
            
            $this->log("订单同步成功，同步数量：" . ($result['count'] ?? 0));
            return true;
            
        } catch (Exception $e) {
            $this->log("订单同步失败：" . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 自动上架商品到系统
     */
    public function autoAddProducts() {
        try {
            $this->log("开始自动上架商品...");
            
            // 获取易教育分类ID
            $fenlei = $this->db->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '易教育' LIMIT 1");
            if (!$fenlei) {
                throw new Exception("易教育分类不存在");
            }
            $fenlei_id = $fenlei['id'];
            
            // 获取易教育货源ID
            $huoyuan_id = $this->config['hid'];
            
            // 获取未上架的易教育项目
            $projects = $this->db->get_rows("
                SELECT jc.* FROM qingka_wangke_jxjyclass jc 
                LEFT JOIN qingka_wangke_class c ON c.noun = jc.number 
                WHERE c.id IS NULL AND jc.status = 1 
                ORDER BY jc.price ASC 
                LIMIT 20
            ");
            
            $add_count = 0;
            
            foreach ($projects as $project) {
                // 计算售价（成本价 + 利润）
                $cost_price = floatval($project['price']);
                $sell_price = $cost_price * 5; // 5倍利润
                if ($sell_price < 1) $sell_price = 1; // 最低1元
                
                // 构建商品描述
                $content = $project['description'];
                if (!empty($project['format'])) {
                    $content .= " 格式：" . $project['format'];
                }
                $content .= " (易教育平台)";
                
                // 插入商品
                $insert_sql = "INSERT INTO qingka_wangke_class (
                    sort, name, getnoun, noun, price, queryplat, docking, 
                    yunsuan, content, status, fenlei, addtime
                ) VALUES (
                    100, 
                    '{$project['name']}', 
                    '{$project['id']}', 
                    '{$project['number']}', 
                    {$sell_price}, 
                    {$huoyuan_id}, 
                    {$huoyuan_id}, 
                    '*', 
                    '{$content}', 
                    1, 
                    {$fenlei_id}, 
                    NOW()
                )";
                
                if ($this->db->query($insert_sql)) {
                    $add_count++;
                    $this->log("商品上架成功：{$project['name']} - 售价：{$sell_price}元");
                }
            }
            
            $this->log("商品上架完成，新增数量：{$add_count}");
            return true;
            
        } catch (Exception $e) {
            $this->log("商品上架失败：" . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 清理过期日志
     */
    public function cleanLogs() {
        try {
            $log_dir = dirname($this->log_file);
            if (!is_dir($log_dir)) {
                return true;
            }
            
            $files = glob($log_dir . '/jxjy_plan_*.log');
            $clean_count = 0;
            
            foreach ($files as $file) {
                $file_time = filemtime($file);
                $days_old = (time() - $file_time) / (24 * 60 * 60);
                
                // 删除7天前的日志
                if ($days_old > 7) {
                    if (unlink($file)) {
                        $clean_count++;
                    }
                }
            }
            
            if ($clean_count > 0) {
                $this->log("清理过期日志完成，删除文件数：{$clean_count}");
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->log("清理日志失败：" . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 执行所有计划任务
     */
    public function runAll() {
        $this->log("=== 易教育计划任务开始执行 ===");
        
        $results = array();
        
        // 1. 自动登录
        $results['login'] = $this->autoLogin();
        
        // 2. 同步商品信息
        $results['sync_products'] = $this->autoSyncProducts();
        
        // 3. 同步订单状态
        $results['sync_orders'] = $this->autoSyncOrders();
        
        // 4. 自动上架商品
        $results['add_products'] = $this->autoAddProducts();
        
        // 5. 清理日志
        $results['clean_logs'] = $this->cleanLogs();
        
        $this->log("=== 易教育计划任务执行完成 ===");
        
        return $results;
    }
}

// 处理请求
try {
    $action = $_GET['action'] ?? 'all';
    $plan = new JxjyPlan();
    
    switch ($action) {
        case 'login':
            $result = array("success" => $plan->autoLogin());
            break;
            
        case 'sync_products':
            $result = array("success" => $plan->autoSyncProducts());
            break;
            
        case 'sync_orders':
            $result = array("success" => $plan->autoSyncOrders());
            break;
            
        case 'add_products':
            $result = array("success" => $plan->autoAddProducts());
            break;
            
        case 'clean_logs':
            $result = array("success" => $plan->cleanLogs());
            break;
            
        case 'all':
        default:
            $result = $plan->runAll();
            break;
    }
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(array("error" => $e->getMessage()), JSON_UNESCAPED_UNICODE);
}
?>
