<?php
/**
 * 简化的易教育同步API测试
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 包含必要文件
include('../confing/common.php');

function jsonReturn($code, $msg, $data = null) {
    $result = array("code" => $code, "msg" => $msg);
    if ($data !== null) {
        $result["data"] = $data;
    }
    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}

try {
    // 获取易教育货源信息
    $jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
    if (!$jk) {
        jsonReturn(-1, "易教育货源配置不存在");
    }

    // 查询需要同步的易教育订单
    $sql = "SELECT o.*, h.pt as platform_type 
            FROM qingka_wangke_order o 
            LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
            WHERE h.pt = 'jxjy' 
            AND o.dockstatus = 1 
            AND o.status NOT IN ('已完成', '已退款', '已取消')
            AND o.addtime > DATE_SUB(NOW(), INTERVAL 7 DAY)
            ORDER BY o.addtime DESC 
            LIMIT 5";
    
    $result = $DB->query($sql);
    $orders_processed = 0;
    
    if (!$result) {
        jsonReturn(-1, "数据库查询失败");
    }
    
    $orders = [];
    while ($order = $DB->fetch($result)) {
        $orders[] = $order;
        $orders_processed++;
    }
    
    if (empty($orders)) {
        jsonReturn(1, "没有需要同步的易教育订单");
    }
    
    // 返回找到的订单信息
    $order_info = [];
    foreach ($orders as $order) {
        $order_info[] = [
            'oid' => $order['oid'],
            'user' => $order['user'],
            'status' => $order['status'],
            'process' => $order['process'],
            'yid' => $order['yid']
        ];
    }
    
    jsonReturn(1, "找到 {$orders_processed} 个需要同步的订单", $order_info);
    
} catch (Exception $e) {
    jsonReturn(-1, "同步失败: " . $e->getMessage());
}
?>
