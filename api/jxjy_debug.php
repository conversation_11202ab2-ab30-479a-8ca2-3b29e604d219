<?php
/**
 * 易教育商品同步脚本 - 调试版本
 * 提供详细的调试信息，帮助诊断问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置最大执行时间
set_time_limit(300); // 5分钟

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 获取并处理 GET 参数
$pricee = isset($_GET['pricee']) ? floatval($_GET['pricee']) : 5;

echo "<h1>易教育商品同步 - 调试版本</h1>\n";
echo "<pre>\n";

echo "=== 调试信息 ===\n";
echo "执行时间: " . date('Y-m-d H:i:s') . "\n";
echo "价格倍数: {$pricee}\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "最大执行时间: " . ini_get('max_execution_time') . "秒\n\n";

// 步骤1：检查数据库配置
echo "步骤1：检查数据库配置\n";
try {
    $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE instr(pt,'jxjy') or instr(name,'易教育')");
    if (!$a) {
        die("❌ 错误：未找到易教育货源配置，请先在后台添加易教育货源\n");
    }
    echo "✅ 货源配置正常\n";
    echo "   货源ID: {$a['hid']}\n";
    echo "   API地址: {$a['url']}\n";
    echo "   账号: {$a['user']}\n";
    
    $hid = $a["hid"];
} catch (Exception $e) {
    die("❌ 数据库查询失败: " . $e->getMessage() . "\n");
}

// 步骤2：检查分类配置
echo "\n步骤2：检查分类配置\n";
try {
    $b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'易教育') ORDER BY id DESC LIMIT 0, 1");
    if (!$b) {
        die("❌ 错误：未找到易教育分类，请先在后台添加易教育分类\n");
    }
    echo "✅ 分类配置正常\n";
    echo "   分类ID: {$b['id']}\n";
    echo "   分类名称: {$b['name']}\n";
    
    $category = $b["id"];
} catch (Exception $e) {
    die("❌ 数据库查询失败: " . $e->getMessage() . "\n");
}

// 步骤3：Token验证和登录
echo "\n步骤3：Token验证和登录\n";
$token = $a["token"];
$need_refresh_token = false;

echo "当前Token: " . (empty($token) ? "无" : substr($token, 0, 20) . "...") . "\n";

// 检查是否有缓存的token
if (empty($token)) {
    echo "Token为空，需要重新登录\n";
    $need_refresh_token = true;
} else {
    echo "验证Token有效性...\n";
    // 验证token是否有效
    $test_url = "{$a["url"]}/api/user/info";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $start_time = microtime(true);
    $test_result = curl_exec($ch);
    $end_time = microtime(true);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Token验证耗时: " . round($end_time - $start_time, 2) . "秒\n";
    echo "HTTP状态码: {$http_code}\n";

    if ($curl_error || !$test_result) {
        echo "Token验证失败: {$curl_error}\n";
        $need_refresh_token = true;
    } else {
        $test_result_array = json_decode($test_result, true);
        if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
            echo "Token无效，需要重新登录\n";
            $need_refresh_token = true;
        } else {
            echo "✅ Token有效\n";
        }
    }
}

// 如果需要刷新token，重新登录
if ($need_refresh_token) {
    echo "\n执行登录...\n";
    $login_data = array(
        "username" => $a["user"],
        "password" => $a["pass"]
    );

    $login_url = "{$a["url"]}/api/login";
    echo "登录URL: {$login_url}\n";
    echo "登录数据: " . json_encode($login_data) . "\n";

    // 使用curl发送JSON请求，增加超时和重试机制
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $start_time = microtime(true);
    $login_result = curl_exec($ch);
    $end_time = microtime(true);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    curl_close($ch);

    echo "登录请求耗时: " . round($end_time - $start_time, 2) . "秒\n";
    echo "HTTP状态码: {$http_code}\n";
    echo "CURL总时间: {$total_time}秒\n";

    if ($curl_error) {
        die("❌ 登录失败：网络错误 - " . $curl_error . " (HTTP状态码: {$http_code})\n");
    }

    if ($http_code != 200) {
        die("❌ 登录失败：HTTP状态码 {$http_code}，请检查API地址是否正确\n");
    }

    $login_result_array = json_decode($login_result, true);

    if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
        $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
        echo "❌ 登录失败：" . $error_msg . "\n";
        echo "响应内容: " . substr($login_result, 0, 500) . "\n";
        die();
    }

    $token = $login_result_array["data"]["token"];
    echo "✅ 登录成功\n";
    echo "新Token: " . substr($token, 0, 30) . "...\n";
    
    // 更新数据库中的token
    try {
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$hid}'");
        echo "Token已更新到数据库\n";
    } catch (Exception $e) {
        echo "⚠️  Token更新失败: " . $e->getMessage() . "\n";
    }
}

// 步骤4：获取项目列表
echo "\n步骤4：获取项目列表\n";

$list_data = array("pageSize" => 10, "pageNum" => 1); // 先只获取10个项目测试
$list_url = "{$a["url"]}/api/website/list";

echo "请求URL: {$list_url}\n";
echo "请求数据: " . json_encode($list_data) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $list_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 120);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($list_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    "Content-Type: application/json",
    "Authorization: Bearer {$token}"
));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$start_time = microtime(true);
$list_result = curl_exec($ch);
$end_time = microtime(true);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
curl_close($ch);

echo "项目列表请求耗时: " . round($end_time - $start_time, 2) . "秒\n";
echo "HTTP状态码: {$http_code}\n";
echo "CURL总时间: {$total_time}秒\n";

if ($curl_error) {
    die("❌ 获取项目列表失败：网络错误 - " . $curl_error . " (HTTP状态码: {$http_code})\n");
}

if ($http_code != 200) {
    die("❌ 获取项目列表失败：HTTP状态码 {$http_code}，请检查Token是否有效\n");
}

$list_result_array = json_decode($list_result, true);

if (!$list_result_array || !isset($list_result_array["code"]) || $list_result_array["code"] != 200) {
    $error_msg = isset($list_result_array["message"]) ? $list_result_array["message"] : "获取项目列表失败";
    echo "❌ 获取项目列表失败：" . $error_msg . "\n";
    echo "响应内容: " . substr($list_result, 0, 500) . "\n";
    die();
}

echo "✅ 项目列表获取成功\n";

// 检查数据结构 - 易教育API数据直接在data数组中
if (isset($list_result_array["data"]) && is_array($list_result_array["data"])) {
    $projects = $list_result_array["data"];
    echo "✅ 数据结构正常\n";
    echo "项目数量: " . count($projects) . "\n";

    if (count($projects) > 0) {
        echo "示例项目:\n";
        foreach (array_slice($projects, 0, 3) as $project) {
            echo "- {$project['name']} (编号: {$project['number']}, 价格: {$project['price']}元)\n";
        }

        echo "\n✅ 调试完成，API连接正常！\n";
        echo "现在可以运行完整同步：\n";
        echo "- 简化版本: 你的域名/api/jxjy_simple.php?pricee={$pricee}\n";
        echo "- 完整版本: 你的域名/api/jxjy.php?pricee={$pricee}\n";
    } else {
        echo "⚠️  项目列表为空\n";
    }
} else {
    echo "❌ 数据结构异常\n";
    echo "响应结构: " . json_encode($list_result_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
}

echo "</pre>\n";
?>
