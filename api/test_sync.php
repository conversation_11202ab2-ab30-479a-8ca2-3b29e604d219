<?php
/**
 * 测试易教育同步API
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 包含必要文件
include('../confing/common.php');

// 手动定义必要函数
function get_url($url, $post = false, $cookie = false, $header = false) {
    $ch = curl_init();
    if ($header) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    } else {
        curl_setopt($ch, CURLOPT_HEADER, 0);
    }
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.62 Safari/537.36");
    if ($post) {
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post));
    }
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}

function post($url, $data, $headers = array()) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    return $result;
}

function jsonReturn($code, $msg, $data = null) {
    $result = array("code" => $code, "msg" => $msg);
    if ($data !== null) {
        $result["data"] = $data;
    }
    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}

// 包含进度查询函数
include('../Checkorder/jdjk.php');

echo "开始测试易教育同步...\n";

try {
    // 获取易教育货源信息
    $jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
    if (!$jk) {
        jsonReturn(-1, "易教育货源配置不存在");
    }

    echo "✅ 易教育货源配置存在\n";

    // 查询需要同步的易教育订单（从主订单表）
    $sql = "SELECT o.*, h.pt as platform_type 
            FROM qingka_wangke_order o 
            LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
            WHERE h.pt = 'jxjy' 
            AND o.dockstatus = 1 
            AND o.status NOT IN ('已完成', '已退款', '已取消')
            AND o.addtime > DATE_SUB(NOW(), INTERVAL 7 DAY)
            ORDER BY o.addtime DESC 
            LIMIT 3";
    
    $result = $DB->query($sql);
    $sync_count = 0;
    $error_count = 0;
    $orders_processed = 0;
    
    if (!$result) {
        jsonReturn(-1, "数据库查询失败");
    }
    
    echo "开始处理订单...\n";
    
    while ($order = $DB->fetch($result)) {
        $orders_processed++;
        
        echo "处理订单: {$order['oid']} - {$order['user']} - yid: {$order['yid']}\n";
        
        try {
            // 调用进度查询函数
            $progress_result = processCx($order['oid']);
            
            if (!empty($progress_result) && is_array($progress_result)) {
                foreach ($progress_result as $item) {
                    if (isset($item['code']) && $item['code'] == 1) {
                        echo "  查询成功: {$item['status_text']} - {$item['process']}\n";
                        
                        // 更新订单状态
                        $status = isset($item['status_text']) ? $item['status_text'] : $order['status'];
                        $process = isset($item['process']) ? $item['process'] : $order['process'];
                        $remarks = isset($item['remarks']) ? $item['remarks'] : $order['remarks'];
                        
                        // 🔥 关键修复：不要覆盖 yid 字段！
                        $should_update_yid = false;
                        $new_yid = $order['yid']; // 默认保持原值
                        
                        if (empty($order['yid']) || $order['yid'] == '1') {
                            if (isset($item['yid']) && !empty($item['yid']) && $item['yid'] != '1') {
                                $should_update_yid = true;
                                $new_yid = $item['yid'];
                            }
                        }
                        
                        echo "  原yid: {$order['yid']}, 新yid: {$new_yid}, 是否更新: " . ($should_update_yid ? "是" : "否") . "\n";
                        
                        if ($should_update_yid) {
                            $update_sql = "UPDATE qingka_wangke_order SET 
                                          status = ?, 
                                          process = ?, 
                                          remarks = ?,
                                          yid = ?,
                                          uptime = NOW()
                                          WHERE oid = ?";
                            $update_result = $DB->query("UPDATE qingka_wangke_order SET 
                                          status = '{$status}', 
                                          process = '{$process}', 
                                          remarks = '{$remarks}',
                                          yid = '{$new_yid}',
                                          uptime = NOW()
                                          WHERE oid = '{$order['oid']}'");
                        } else {
                            // 不更新 yid，保持原有订单号
                            $update_result = $DB->query("UPDATE qingka_wangke_order SET 
                                          status = '{$status}', 
                                          process = '{$process}', 
                                          remarks = '{$remarks}',
                                          uptime = NOW()
                                          WHERE oid = '{$order['oid']}'");
                        }
                        
                        if ($update_result) {
                            echo "  ✅ 数据库更新成功\n";
                            $sync_count++;
                        } else {
                            echo "  ❌ 数据库更新失败\n";
                            $error_count++;
                        }
                    } else {
                        echo "  ❌ 查询失败: " . (isset($item['msg']) ? $item['msg'] : '未知错误') . "\n";
                        $error_count++;
                    }
                }
            } else {
                echo "  ⚠️ processCx返回空结果\n";
                $error_count++;
            }
        } catch (Exception $e) {
            echo "  ❌ 异常: " . $e->getMessage() . "\n";
            $error_count++;
        }
        
        // 避免请求过于频繁
        usleep(200000); // 0.2秒延迟
    }
    
    echo "\n同步完成，处理订单: {$orders_processed}，成功: {$sync_count}，失败: {$error_count}\n";
    jsonReturn(1, "同步完成，处理订单: {$orders_processed}，成功: {$sync_count}，失败: {$error_count}");
    
} catch (Exception $e) {
    echo "❌ 同步失败: " . $e->getMessage() . "\n";
    jsonReturn(-1, "同步失败: " . $e->getMessage());
}
?>
