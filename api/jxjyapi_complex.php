<?php
/**
 * 易教育API接口文件
 * 完全兼容原始易教育对接格式
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 获取请求参数
$act = $_GET['act'] ?? $_GET['action'] ?? '';

// 获取易教育货源配置
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$huoyuan) {
    exit(json_encode(array("code" => -1, "msg" => "未找到易教育货源配置")));
}

$jk = $huoyuan;  // 兼容原始变量名
$header = array("Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $jk['token']);
$now_time = date("Y-m-d H:i:s");

// 获取用户信息（如果有会话）
$userrow = array();
if (isset($_SESSION['userrow'])) {
    $userrow = $_SESSION['userrow'];
} else {
    // 模拟用户信息（用于测试）
    $userrow = array('uid' => 1, 'addprice' => 1);
}

$clientip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

// 处理不同的API请求
switch ($act) {
    case 'jxjyclass':
        // 获取易教育项目列表
        $projects_result = $DB->query("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 ORDER BY id ASC");
        $data = array();
        
        if ($projects_result) {
            while ($row = $DB->fetch_array($projects_result)) {
                // 根据用户倍率计算价格
                $price = $row['price'] * $userrow['addprice'];
                $row['price'] = number_format($price, 2);
                $data[] = $row;
            }
        }
        
        $response = array('code' => 1, 'data' => $data);
        exit(json_encode($response));
        break;

    case 'getcourse':
        // 查课接口
        $id = trim(strip_tags($_POST['id'] ?? ''));
        $username = trim(strip_tags($_POST['user'] ?? ''));
        $password = trim(strip_tags($_POST['pass'] ?? ''));
        
        if (empty($id) || empty($username) || empty($password)) {
            $responseData = array(array("code" => -1, "msg" => "参数不能为空"));
            exit(json_encode($responseData));
        }
        
        // 使用项目ID查询项目信息
        $rs = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$id}' LIMIT 1");
        if (!$rs) {
            $responseData = array(array("code" => -1, "msg" => "项目信息不存在，项目ID: {$id}"));
            exit(json_encode($responseData));
        }
        
        $number = $rs['number'];
        
        // 记录查课日志
        if (isset($userrow['uid'])) {
            wlog($userrow['uid'], "继续教育查课", "平台{$rs['name']} {$username} {$password}", 0);
        }
        
        // 检查是否需要查课
        if ($rs['isSearchCourse'] == '0') {
            $responseData = array(array("code" => -1, "msg" => "该项目无需查课"));
            exit(json_encode($responseData));
        }
        
        // 发起查课请求
        $data = array(
            "websiteNumber" => $number,
            "data" => array(array("username" => $username, "password" => $password))
        );
        
        $jxjy_url = $jk["url"] . "/api/website/queryCourse";
        $result = post($jxjy_url, json_encode($data), $header);
        $result = json_decode($result, true);
        
        if ($result && $result['code'] == 200) {
            $uuid = $result['data']['uuid'];
            
            // 延时5秒
            sleep(5);
            
            $data2 = array("uuid" => $uuid);
            $jxjy_url2 = $jk["url"] . "/api/website/getQueryCourse";
            $result2 = post($jxjy_url2, json_encode($data2), $header);
            $result2 = json_decode($result2, true);
            
            if ($result2 && $result2['code'] == 200 && isset($result2['data'][0]['name']) && strpos($result2['data'][0]['name'], "登录失败") === false) {
                if (isset($result2['data'][0]['children'])) {
                    $children = $result2['data'][0]['children'];
                    $filteredData = array();
                    
                    foreach ($children as $child) {
                        if (isset($child['id']) && isset($child['name'])) {
                            $filteredData[] = array("id" => $child['id'], "name" => $child['name']);
                        } elseif (isset($child['name']) && isset($child['children'])) {
                            foreach ($child['children'] as $subChild) {
                                if (isset($subChild['id']) && isset($subChild['name'])) {
                                    $filteredData[] = array("id" => $subChild['id'], "name" => $child['name'] . "----" . $subChild['name']);
                                }
                            }
                        }
                    }
                    
                    $responseData = array(array("code" => 1, "data" => $filteredData, "msg" => "查询成功", "userinfo" => $result2['data'][0]['name']));
                    exit(json_encode($responseData));
                }
            } else {
                $error_msg = isset($result2['data'][0]['name']) ? $result2['data'][0]['name'] : "查课失败";
                $responseData = array(array("code" => -1, "msg" => $error_msg));
                exit(json_encode($responseData));
            }
        } else {
            $responseData = array(array("code" => -1, "msg" => "发送查课请求失败，请联系管理员"));
            exit(json_encode($responseData));
        }
        break;

    case 'jxjyadd':
        // 下单接口
        $id = trim(strip_tags($_POST['id'] ?? ''));
        $username = trim(strip_tags($_POST['user'] ?? ''));
        $password = trim(strip_tags($_POST['pass'] ?? ''));
        $select = $_POST['select'] ?? array();
        
        if (empty($id)) {
            jsonReturn(-1, "ID不能为空");
        }
        if (empty($username)) {
            jsonReturn(-1, "账号不能为空");
        }
        if (empty($password)) {
            jsonReturn(-1, "密码不能为空");
        }
        
        // 使用项目ID查询项目信息
        $rs = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id = '{$id}' LIMIT 1");
        if (!$rs) {
            jsonReturn(-1, "项目信息不存在，项目ID: {$id}");
        }
        
        $number = $rs['number'];
        $danjia = round($rs['price'] * $userrow['addprice'], 2);
        
        // 检查余额
        if ($userrow['money'] < $danjia) {
            jsonReturn(-1, "您的余额不足");
        }
        
        if ($danjia == 0 || $userrow['addprice'] < 0.1) {
            exit('{"code":-1,"msg":"价格异常，请联系管理员"}');
        }
        
        if ($rs['must'] == '0') {
            // 无需查课的项目
            $data = array(
                "websiteNumber" => $number,
                "data" => array(array("username" => $username, "password" => $password))
            );
            
            $jxjy_url = $jk["url"] . "/api/order/buy";
            $result = post($jxjy_url, json_encode($data), $header);
            $result = json_decode($result, true);
            
            if ($result && $result['code'] == 200) {
                // 插入订单记录
                $insert_sql = "INSERT INTO qingka_wangke_order (
                    uid, cid, ptname, name, user, pass, kcid, kcname, fees, noun, status, ip, addtime
                ) VALUES (
                    '{$userrow['uid']}', '{$id}', '易教育', '{$rs['name']}', '{$username}', '{$password}', 
                    '', '', '{$danjia}', '{$rs['number']}', '已提交', '{$clientip}', '{$now_time}'
                )";
                
                if ($DB->query($insert_sql)) {
                    // 扣除余额
                    $DB->query("UPDATE qingka_wangke_user SET money = money - '{$danjia}' WHERE uid = '{$userrow['uid']}' LIMIT 1");
                    wlog($userrow['uid'], "继续教育下单", "平台{$rs['name']} {$username} {$password} 扣除{$danjia}元", -$danjia);
                    jsonReturn(1, "成功下单");
                } else {
                    jsonReturn(-1, "数据库错误");
                }
            } else {
                $error_msg = isset($result['message']) ? $result['message'] : "下单失败";
                jsonReturn(-1, $error_msg);
            }
        } else {
            // 需要查课的项目
            if (empty($select)) {
                jsonReturn(-1, "请选择课程");
            }
            
            $success = 0;
            foreach ($select as $item) {
                $kcid = $item['id'];
                $kcname = $item['name'];
                
                // 构建下单数据
                if (strpos($kcname, '----') !== false) {
                    list($kcname1, $kcname2) = explode('----', $kcname, 2);
                    $data = array(
                        "websiteNumber" => $number,
                        "data" => array(array(
                            "username" => $username,
                            "password" => $password,
                            "name" => $username . "----" . $password,
                            "children" => array(array(
                                "name" => $kcname1,
                                "children" => array(array(
                                    "name" => $kcname2,
                                    "disabled" => false,
                                    "id" => $kcid,
                                    "selected" => true
                                )),
                                "disabled" => true,
                                "selected" => true
                            )),
                            "selected" => true
                        ))
                    );
                } else {
                    $data = array(
                        "websiteNumber" => $number,
                        "data" => array(array(
                            "username" => $username,
                            "password" => $password,
                            "name" => $username . "----" . $password,
                            "children" => array(array(
                                "name" => $kcname,
                                "disabled" => false,
                                "id" => $kcid,
                                "selected" => true
                            )),
                            "selected" => true
                        ))
                    );
                }
                
                $jxjy_url = $jk["url"] . "/api/order/buy";
                $result = post($jxjy_url, json_encode($data), $header);
                $result = json_decode($result, true);
                
                if ($result && $result['code'] == 200) {
                    // 插入订单记录
                    $insert_sql = "INSERT INTO qingka_wangke_order (
                        uid, cid, ptname, name, user, pass, kcid, kcname, fees, noun, status, ip, addtime
                    ) VALUES (
                        '{$userrow['uid']}', '{$id}', '易教育', '{$rs['name']}', '{$username}', '{$password}', 
                        '{$kcid}', '{$kcname}', '{$danjia}', '{$rs['number']}', '已提交', '{$clientip}', '{$now_time}'
                    )";
                    
                    if ($DB->query($insert_sql)) {
                        // 扣除余额
                        $DB->query("UPDATE qingka_wangke_user SET money = money - '{$danjia}' WHERE uid = '{$userrow['uid']}' LIMIT 1");
                        wlog($userrow['uid'], "继续教育下单", "平台{$rs['name']} {$username} {$password} {$kcname} 扣除{$danjia}元", -$danjia);
                        $success++;
                    }
                } else {
                    jsonReturn(-1, "下单失败");
                }
            }
            
            if ($success > 0) {
                exit('{"code":1,"msg":"提交成功，共提交' . $success . '条"}');
            } else {
                exit('{"code":-1,"msg":"提交失败"}');
            }
        }
        break;

    default:
        // 默认返回API信息
        $result = array(
            "code" => 1,
            "msg" => "易教育API接口",
            "actions" => array(
                "jxjyclass" => "获取项目列表",
                "getcourse" => "查课接口",
                "jxjyadd" => "下单接口"
            )
        );
        exit(json_encode($result));
        break;
}

// 辅助函数
function post($url, $data, $headers = array()) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    return $result;
}

function jsonReturn($code, $msg, $data = null) {
    $result = array("code" => $code, "msg" => $msg);
    if ($data !== null) {
        $result["data"] = $data;
    }
    exit(json_encode($result));
}
?>
