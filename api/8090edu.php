<?php

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 获取并处理 GET 参数
$pricee = isset($_GET['pricee']) ? floatval($_GET['pricee']) : 3;

// 查询 qingka_wangke_huoyuan 表获取相关信息
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE instr(pt,'8090edu') or instr(name,'8090edu')");
$hid = $a["hid"];

// 查询 qingka_wangke_fenlei 表获取分类信息
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'8090edu') ORDER BY id DESC LIMIT 0, 1");
$category = $b["id"];

// 获取Token
$login_data = array(
    "username" => $a["user"],
    "password" => $a["pass"]
);

$login_url = "{$a["url"]}/api/auth/login";

// 使用curl发送JSON请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
$login_result = curl_exec($ch);
curl_close($ch);

$login_result = json_decode($login_result, true);

// 检查登录结果
if ($login_result["code"] != 200) {
    jsonReturn(1, $login_result["message"]);
}

$token = $login_result["data"]["token"];

// 获取所有网站的完整信息（使用新的高效API）
$all_sites = [];
$page = 1;
$pageSize = 100;
$total_pages = 1;

echo "开始获取网站列表...\n";

do {
    $sites_url = "{$a["url"]}/api/price/sites?page={$page}&pageSize={$pageSize}";

    // 使用curl发送带Authorization头的请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $sites_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $sites_result = curl_exec($ch);
    curl_close($ch);

    $sites_result = json_decode($sites_result, true);

    if ($sites_result["code"] != 200) {
        jsonReturn(1, $sites_result["message"]);
    }

    // 合并当前页的数据
    if (isset($sites_result["data"]["list"])) {
        $all_sites = array_merge($all_sites, $sites_result["data"]["list"]);

        // 计算总页数
        if ($page == 1 && isset($sites_result["data"]["total"])) {
            $total_records = $sites_result["data"]["total"];
            $total_pages = ceil($total_records / $pageSize);
            echo "总记录数: {$total_records}，总页数: {$total_pages}\n";
        }

        echo "已获取第 {$page}/{$total_pages} 页，当前总数: " . count($all_sites) . "\n";
    }

    $page++;
} while ($page <= $total_pages);

echo "网站列表获取完成，共 " . count($all_sites) . " 个网站\n";

// 查询 qingka_wangke_class 表的最大 sort 值
$max_sort_query = "SELECT MAX(sort) as max_sort FROM qingka_wangke_class";
$max_sort_result = mysqli_query($conn, $max_sort_query);
$max_sort_row = mysqli_fetch_assoc($max_sort_result);
$current_sort = $max_sort_row['max_sort'] ?? 0;

// 初始化插入和更新记录的计数器
$inserted_count = 0;
$updated_count = 0;

// 初始化输出数组
$output = [];
$ids = [];

// 遍历所有网站数据
$total_sites = count($all_sites);
echo "开始处理 {$total_sites} 个网站...\n";

// 设置脚本执行时间限制
set_time_limit(0);

foreach ($all_sites as $index => $site) {
    $site_id = $site['site_id'];
    $ids[] = $site_id;

    // 每处理100个网站显示一次进度
    if ($index % 100 == 0 || $index == $total_sites - 1) {
        echo "处理进度: " . ($index + 1) . "/{$total_sites} (" . round(($index + 1) / $total_sites * 100, 1) . "%)\n";
    }

    // 直接使用完整的网站信息（不需要再次获取详细信息）
    $site_detail = $site;

    // 清理项目名称，移除常见的8090教育前缀
    $original_name = $site_detail['site_name'];
    $cleaned_name = $original_name;

    // 定义需要移除的前缀
    $prefixes_to_remove = array(
        '8090教育-'
    );

    // 移除前缀
    foreach ($prefixes_to_remove as $prefix) {
        if (strpos($cleaned_name, $prefix) === 0) {
            $cleaned_name = substr($cleaned_name, strlen($prefix));
            break;
        }
    }

    $name = $DB->escape($cleaned_name);
    $price = $site_detail['price'] * $pricee;

    // 构建完整的课程说明信息（使用详细信息）
    $content_parts = [];

    // 基本信息
    $content_parts[] = "网站编号: " . $site_detail['site_id'];
    $content_parts[] = "项目格式: " . ($site_detail['format'] ?? '未知');
    $content_parts[] = "项目状态: " . ($site_detail['status'] ?? '未知');

    // 功能支持信息
    $check_course = isset($site_detail['check_course']) ? $site_detail['check_course'] : '未知';
    $content_parts[] = "项目查课: " . $check_course;

    $exam_support = isset($site_detail['exam_support']) ? $site_detail['exam_support'] : '未知';
    $content_parts[] = "项目考试: " . $exam_support;

    // 项目说明 - 总是显示，即使为空
    $description = isset($site_detail['description']) ? $site_detail['description'] : '';
    $content_parts[] = "项目说明: " . ($description ?: '无');

    // 网站地址
    $url = isset($site_detail['url']) ? $site_detail['url'] : '';
    if (!empty($url)) {
        $content_parts[] = "项目网站: " . $url;
    }

    // 速率信息
    $speed = isset($site_detail['speed']) ? $site_detail['speed'] : '';
    if (!empty($speed) && $speed !== null) {
        $content_parts[] = "项目速率: " . $speed;
    }

    // 拼接所有信息
    $content = $DB->escape(implode(' | ', $content_parts));

    // 查询 qingka_wangke_class 表是否存在对应记录 - 使用更精确的查询条件
    $escaped_site_id = $DB->escape($site_id);
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '$hid' AND noun = '$escaped_site_id' LIMIT 1");

    if (!$rs) {
        // 若记录不存在，执行插入操作
        $sql = "INSERT INTO qingka_wangke_class
                (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, addtime, status, fenlei, uptime)
                VALUES
                ('$current_sort', '$name', '$escaped_site_id', '$escaped_site_id', '$price', '$hid', '$hid', '*', '$content', '$now_time', '1', '$category', '$now_time')";

        $is = $DB->query($sql);
        if ($is) {
            $inserted_count++;
            $current_sort++;
        }
    } else {
        // 若记录存在，执行更新操作
        $sql = "UPDATE qingka_wangke_class
                SET name = '$name',
                    price = '$price',
                    content = '$content',
                    uptime = '$now_time',
                    status = '1'
                WHERE docking = '$hid' AND noun = '$escaped_site_id'";

        $is = $DB->query($sql);
        if ($is) {
            $updated_count++;
        }
    }
}

//下架已下架的项目
if (!empty($ids)) {
    $ids_str = implode(',', array_map(function($id) use ($DB) {
        return "'" . $DB->escape($id) . "'";
    }, $ids));
    $sql = "UPDATE qingka_wangke_class SET status = 0 WHERE docking = '$hid' AND noun NOT IN ($ids_str)";
    $DB->query($sql);
}

// 清理可能的重复记录（保留最新的）
echo "检查并清理重复记录...\n";
$cleanup_sql = "
    DELETE c1 FROM qingka_wangke_class c1
    INNER JOIN qingka_wangke_class c2
    WHERE c1.docking = '$hid'
    AND c2.docking = '$hid'
    AND c1.noun = c2.noun
    AND c1.cid < c2.cid
";
$cleanup_result = $DB->query($cleanup_sql);
if ($cleanup_result) {
    $affected_rows = $DB->affected_rows();
    if ($affected_rows > 0) {
        echo "清理了 {$affected_rows} 条重复记录\n";
    }
}

// 返回操作结果
$total_processed = $inserted_count + $updated_count;
echo "8090教育同步完成！\n";
echo "处理网站总数: {$total_sites}\n";
echo "成功上架: {$inserted_count} 条\n";
echo "成功更新: {$updated_count} 条\n";
echo "总计处理: {$total_processed} 条记录\n";
echo "同步时间: " . date('Y-m-d H:i:s') . "\n";

?>
