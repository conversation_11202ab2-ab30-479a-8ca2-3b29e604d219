<?php
/**
 * 订单进度实时更新API
 * 只返回订单的进度相关字段，用于前端轮询更新
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

try {
    // 获取请求参数
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // 如果是GET请求，从URL参数获取
    if ($_SERVER['REQUEST_METHOD'] == 'GET') {
        $data = $_GET;
    }

    // 如果没有数据，尝试从POST获取
    if (empty($data) && !empty($_POST)) {
        $data = $_POST;
    }
    
    // 验证必要参数
    if (!isset($data['oids']) || empty($data['oids'])) {
        echo json_encode([
            'code' => -1,
            'msg' => '缺少订单ID参数',
            'data' => []
        ]);
        exit;
    }
    
    $oids = $data['oids'];
    
    // 如果是字符串，转换为数组
    if (is_string($oids)) {
        $oids = explode(',', $oids);
    }
    
    // 验证订单ID格式
    $oids = array_filter($oids, function($oid) {
        return is_numeric($oid) && $oid > 0;
    });
    
    if (empty($oids)) {
        echo json_encode([
            'code' => -1,
            'msg' => '无效的订单ID',
            'data' => []
        ]);
        exit;
    }
    
    // 限制一次查询的订单数量，避免性能问题
    if (count($oids) > 100) {
        $oids = array_slice($oids, 0, 100);
    }
    
    // 构建SQL查询
    $oid_list = implode(',', array_map('intval', $oids));
    
    $sql = "
        SELECT 
            oid,
            status,
            process,
            name,
            remarks,
            yid,
            dockstatus,
            addtime,
            uptime
        FROM qingka_wangke_order 
        WHERE oid IN ({$oid_list})
        ORDER BY addtime DESC
    ";
    
    $result = $DB->query($sql);
    
    if (!$result) {
        echo json_encode([
            'code' => -1,
            'msg' => '数据库查询失败',
            'data' => []
        ]);
        exit;
    }
    
    $progress_data = [];
    
    while ($row = $result->fetch_assoc()) {
        // 处理进度显示
        $process_display = $row['process'];
        if (empty($process_display) || $process_display == '获取失败' || $process_display == 'chusheng-获取失败') {
            // 根据状态设置默认进度
            switch ($row['status']) {
                case '已完成':
                case '完成':
                    $process_display = '100%';
                    break;
                case '已退款':
                case '退款':
                case '失败':
                    $process_display = '0%';
                    break;
                case '上号中':
                case '进行中':
                case '学习中':
                    $process_display = '50%';
                    break;
                case '队列中':
                case '待处理':
                    $process_display = '10%';
                    break;
                default:
                    $process_display = '0%';
                    break;
            }
        }
        
        // 确保进度格式正确
        if (!empty($process_display) && !strpos($process_display, '%')) {
            $process_display .= '%';
        }
        
        // 处理状态显示
        $status_display = $row['status'];
        if (empty($status_display)) {
            $status_display = '未知';
        }
        
        // 处理详细信息
        $name_display = $row['name'];
        if (empty($name_display)) {
            $name_display = $row['remarks'];
        }
        
        $progress_data[] = [
            'oid' => intval($row['oid']),
            'status' => $status_display,
            'process' => $process_display,
            'name' => $name_display,
            'remarks' => $row['remarks'],
            'yid' => $row['yid'],
            'dockstatus' => intval($row['dockstatus']),
            'addtime' => $row['addtime'],
            'uptime' => $row['uptime'],
            'update_time' => date('Y-m-d H:i:s') // 添加更新时间戳
        ];
    }
    
    // 返回成功结果
    echo json_encode([
        'code' => 1,
        'msg' => '获取成功',
        'data' => $progress_data,
        'count' => count($progress_data),
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    // 错误处理
    echo json_encode([
        'code' => -1,
        'msg' => '服务器错误: ' . $e->getMessage(),
        'data' => []
    ]);
}

?>
