<?php
include('../confing/common.php');
@header('Content-Type: application/json; charset=UTF-8');
$username = trim($_REQUEST['username']);
$kcname = $_REQUEST['kcname'];
$cid = intval($_REQUEST['cid']);

if (empty($username)) {
    exit('{"code":0,"msg":"username参数不能为空（查询账号）"}');
}
if (empty($kcname)) {
    exit('{"code":0,"msg":"kcname参数不能为空（查询课程）"}');
}
if (empty($cid)) {
    exit('{"code":0,"msg":"cid参数不能为空（对接平台id）"}');
}

$sql = "SELECT * FROM qingka_wangke_order WHERE user=? AND kcname=? AND cid=? ORDER BY oid DESC LIMIT 1";
$params = [$username, $kcname, $cid];
$row = $DB->prepare_getrow($sql, $params);

if ($row) {
    $data = [
        'code' => 1,
        'data' => [
            [
                'id' => $row['oid'],
                'ptname' => $row['ptname'],
                'school' => $row['school'],
                'name' => $row['name'],
                'user' => $row['user'],
                'kcname' => $row['kcname'],
                'addtime' => $row['addtime'],
                'courseStartTime' => $row['courseStartTime'],
                'courseEndTime' => $row['courseEndTime'],
                'examStartTime' => $row['examStartTime'],
                'examEndTime' => $row['examEndTime'],
                'status' => $row['status'],
                'process' => $row['process'],
                'remarks' => $row['remarks']
            ]
        ]
    ];
    exit(json_encode($data));
} else {
    $data = ['code' => -1, 'msg' => "未查到该账号的下单信息"];
    exit(json_encode($data));
}
