<?php
/**
 * 易教育项目同步脚本
 * 从易教育API获取项目列表并同步到本地数据库
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置执行时间限制
set_time_limit(300);

echo "<h1>易教育项目同步</h1>\n";
echo "<pre>\n";

// 获取易教育货源配置
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$jk) {
    echo "❌ 错误：未找到易教育货源配置\n";
    echo "请先在后台添加易教育货源配置\n";
    exit;
}

echo "✅ 找到易教育货源配置\n";
echo "   货源ID: {$jk['hid']}\n";
echo "   货源名称: {$jk['name']}\n";
echo "   API地址: {$jk['url']}\n";
echo "   账号: {$jk['user']}\n\n";

// 通用POST请求函数
function post($url, $data, $headers = array()) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $result = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("CURL错误: " . $error);
    }
    
    return $result;
}

// 检查Token有效性并刷新
function checkAndRefreshToken($jk) {
    global $DB;
    
    $token = $jk["token"];
    $need_refresh = false;

    // 检查Token是否存在
    if (empty($token)) {
        $need_refresh = true;
        echo "⚠️  Token为空，需要重新登录\n";
    } else {
        // 验证Token是否有效
        echo "🔍 验证Token有效性...\n";
        $test_url = "{$jk["url"]}/api/user/info";
        
        try {
            $test_result = post($test_url, "", array("Authorization: Bearer {$token}"));
            $test_result_array = json_decode($test_result, true);
            
            if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                $need_refresh = true;
                echo "⚠️  Token已失效，需要重新登录\n";
            } else {
                echo "✅ Token有效\n";
            }
        } catch (Exception $e) {
            $need_refresh = true;
            echo "⚠️  Token验证失败: " . $e->getMessage() . "\n";
        }
    }

    // 如果需要刷新Token，重新登录
    if ($need_refresh) {
        echo "🔑 正在重新登录...\n";
        
        $login_data = array(
            "username" => $jk["user"],
            "password" => $jk["pass"]
        );

        $login_url = "{$jk["url"]}/api/login";

        try {
            $login_result = post($login_url, json_encode($login_data), array("Content-Type: application/json"));
            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                throw new Exception($error_msg);
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的Token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$jk["hid"]}'");
            
            echo "✅ 登录成功，Token已更新\n";
            return $token;
            
        } catch (Exception $e) {
            throw new Exception("登录失败: " . $e->getMessage());
        }
    }

    return $token;
}

try {
    // 检查并刷新Token
    $token = checkAndRefreshToken($jk);
    
    // 设置请求头
    $headers = array(
        "Content-Type: application/json",
        "Authorization: Bearer {$token}"
    );

    echo "\n📡 正在获取易教育项目列表...\n";
    
    // 获取项目列表
    $projects_url = "{$jk["url"]}/api/website/list";
    $projects_data = array(
        "pageSize" => 1000,
        "pageNum" => 1
    );
    
    $projects_result = post($projects_url, json_encode($projects_data), $headers);
    $projects_result_array = json_decode($projects_result, true);
    
    if (!$projects_result_array || !isset($projects_result_array["code"]) || $projects_result_array["code"] != 200) {
        $error_msg = isset($projects_result_array["message"]) ? $projects_result_array["message"] : "获取项目列表失败";
        throw new Exception($error_msg);
    }
    
    $projects = $projects_result_array["data"]["list"];
    $total_count = count($projects);
    
    echo "✅ 成功获取到 {$total_count} 个项目\n\n";
    
    // 开始同步项目
    echo "🔄 开始同步项目到数据库...\n";
    
    $success_count = 0;
    $update_count = 0;
    $error_count = 0;
    
    foreach ($projects as $project) {
        $number = $project['number'];
        $name = $project['name'];
        $price = isset($project['price']) ? $project['price'] : 0;
        $unitId = isset($project['unitId']) ? $project['unitId'] : '';
        $unit = isset($project['unit']) ? $project['unit'] : '';
        $rate = isset($project['rate']) ? $project['rate'] : '';
        $isExam = isset($project['isExam']) ? ($project['isExam'] ? '1' : '0') : '0';
        $isSearchCourse = isset($project['isSearchCourse']) ? ($project['isSearchCourse'] ? '1' : '0') : '0';
        $description = isset($project['description']) ? $project['description'] : '';
        $url = isset($project['url']) ? $project['url'] : '';
        $format = isset($project['format']) ? $project['format'] : '';
        $must = $isSearchCourse; // must字段与isSearchCourse相同
        
        try {
            // 检查项目是否已存在
            $existing = $DB->get_row("SELECT id FROM qingka_wangke_jxjyclass WHERE number = '{$number}' LIMIT 1");
            
            if ($existing) {
                // 更新现有项目
                $sql = "UPDATE qingka_wangke_jxjyclass SET 
                        name = '{$name}',
                        price = '{$price}',
                        unitId = '{$unitId}',
                        unit = '{$unit}',
                        rate = '{$rate}',
                        isExam = '{$isExam}',
                        isSearchCourse = '{$isSearchCourse}',
                        description = '{$description}',
                        url = '{$url}',
                        format = '{$format}',
                        must = '{$must}',
                        updatetime = NOW()
                        WHERE number = '{$number}'";
                
                if ($DB->query($sql)) {
                    $update_count++;
                    echo "🔄 更新项目: {$name} (编号: {$number})\n";
                } else {
                    $error_count++;
                    echo "❌ 更新失败: {$name} (编号: {$number})\n";
                }
            } else {
                // 插入新项目
                $sql = "INSERT INTO qingka_wangke_jxjyclass 
                        (number, name, price, unitId, unit, rate, isExam, isSearchCourse, description, url, format, must, status, addtime, updatetime) 
                        VALUES 
                        ('{$number}', '{$name}', '{$price}', '{$unitId}', '{$unit}', '{$rate}', '{$isExam}', '{$isSearchCourse}', '{$description}', '{$url}', '{$format}', '{$must}', 1, NOW(), NOW())";
                
                if ($DB->query($sql)) {
                    $success_count++;
                    echo "✅ 新增项目: {$name} (编号: {$number})\n";
                } else {
                    $error_count++;
                    echo "❌ 新增失败: {$name} (编号: {$number})\n";
                }
            }
        } catch (Exception $e) {
            $error_count++;
            echo "❌ 处理项目失败: {$name} - " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📊 同步完成统计:\n";
    echo "   总项目数: {$total_count}\n";
    echo "   新增项目: {$success_count}\n";
    echo "   更新项目: {$update_count}\n";
    echo "   失败项目: {$error_count}\n";
    
    if ($error_count == 0) {
        echo "\n🎉 所有项目同步成功！\n";
        echo "现在可以正常使用易教育查课功能了。\n";
    } else {
        echo "\n⚠️  有 {$error_count} 个项目同步失败，请检查错误信息。\n";
    }
    
} catch (Exception $e) {
    echo "❌ 同步失败: " . $e->getMessage() . "\n";
    echo "\n请检查以下项目:\n";
    echo "1. 易教育货源配置是否正确\n";
    echo "2. 网络连接是否正常\n";
    echo "3. API地址是否可访问\n";
    echo "4. 账号密码是否正确\n";
}

echo "\n</pre>\n";
?>
