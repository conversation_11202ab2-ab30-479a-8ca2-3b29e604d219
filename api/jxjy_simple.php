<?php
/**
 * 易教育商品同步脚本 - 简化版本
 * 跳过Token验证，直接重新登录，避免超时问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置最大执行时间
set_time_limit(300); // 5分钟

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 获取并处理 GET 参数
$pricee = isset($_GET['pricee']) ? floatval($_GET['pricee']) : 5;

// 查询 qingka_wangke_huoyuan 表获取相关信息
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE instr(pt,'jxjy') or instr(name,'易教育')");
if (!$a) {
    die("错误：未找到易教育货源配置，请先在后台添加易教育货源");
}
$hid = $a["hid"];

// 查询 qingka_wangke_fenlei 表获取分类信息
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'易教育') ORDER BY id DESC LIMIT 0, 1");
if (!$b) {
    die("错误：未找到易教育分类，请先在后台添加易教育分类");
}
$category = $b["id"];

echo "开始易教育商品同步...\n";
echo "API地址: {$a["url"]}\n";
echo "价格倍数: {$pricee}\n\n";

// 直接重新登录，不验证旧Token
echo "步骤1：登录获取Token...\n";
$login_data = array(
    "username" => $a["user"],
    "password" => $a["pass"]
);

$login_url = "{$a["url"]}/api/login";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$login_result = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($curl_error) {
    die("登录失败：网络错误 - " . $curl_error . " (HTTP状态码: {$http_code})");
}

if ($http_code != 200) {
    die("登录失败：HTTP状态码 {$http_code}，请检查API地址是否正确");
}

$login_result_array = json_decode($login_result, true);

if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
    $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
    die("登录失败：" . $error_msg);
}

$token = $login_result_array["data"]["token"];
echo "✅ 登录成功\n";

// 更新数据库中的token
$DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$hid}'");

echo "\n步骤2：获取项目列表...\n";

// 获取网站列表
$list_data = array("pageSize" => 100, "pageNum" => 1);
$list_url = "{$a["url"]}/api/website/list";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $list_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 120);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($list_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    "Content-Type: application/json",
    "Authorization: Bearer {$token}"
));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$list_result = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($curl_error) {
    die("获取项目列表失败：网络错误 - " . $curl_error . " (HTTP状态码: {$http_code})");
}

if ($http_code != 200) {
    die("获取项目列表失败：HTTP状态码 {$http_code}，请检查Token是否有效");
}

$list_result_array = json_decode($list_result, true);

if (!$list_result_array || !isset($list_result_array["code"]) || $list_result_array["code"] != 200) {
    $error_msg = isset($list_result_array["message"]) ? $list_result_array["message"] : "获取项目列表失败";
    die("获取项目列表失败：" . $error_msg);
}

// 检查数据结构 - 易教育API数据直接在data数组中
if (!isset($list_result_array["data"]) || !is_array($list_result_array["data"])) {
    die("项目列表数据结构异常");
}

$projects = $list_result_array["data"];
$total_projects = count($projects);

echo "✅ 找到 {$total_projects} 个易教育项目\n";

if ($total_projects == 0) {
    die("项目列表为空，请检查API返回数据");
}

echo "\n步骤3：同步商品数据...\n";

// 查询 qingka_wangke_class 表的最大 sort 值
$max_sort_result = $DB->get_row("SELECT MAX(sort) as max_sort FROM qingka_wangke_class");
$current_sort = ($max_sort_result['max_sort'] ?? 0) + 1;

// 初始化计数器
$inserted_count = 0;
$updated_count = 0;
$sync_count = 0;

// 收集所有项目ID，用于下架已删除的项目
$project_ids = array();

// 遍历项目列表
foreach ($projects as $project) {
    $project_id = $project['id'];
    $project_ids[] = $project_id;
    
    $name = $DB->escape($project['name']);
    $number = $project['number'];
    $price = floatval($project['price']) * $pricee;
    
    // 构建项目描述
    $content_parts = array();
    $content_parts[] = "项目编号: " . $number;
    $content_parts[] = "格式要求: " . ($project['format'] ?? '账号 密码');
    $content_parts[] = "项目网址: " . ($project['url'] ?? '');
    $content_parts[] = "价格单位: " . ($project['unit'] ?? '年度');
    
    // 功能支持信息
    $must = $project['must'] ?? '1';
    $content_parts[] = "查课要求: " . ($must == '1' ? '必须查课' : '无需查课');
    
    $isExam = $project['isExam'] ?? '0';
    $content_parts[] = "考试支持: " . ($isExam == '1' ? '支持考试' : '不支持考试');
    
    $rate = $project['rate'] ?? '1';
    $rate_text = array('0' => '正常速度', '1' => '加速', '2' => '秒学');
    $content_parts[] = "学习速度: " . ($rate_text[$rate] ?? '正常速度');
    
    // 项目说明
    $description = $project['description'] ?? '';
    if (!empty($description)) {
        $content_parts[] = "项目说明: " . $description;
    }
    
    // 移除 "(易教育平台)" 标识
    $content = $DB->escape(implode(' | ', $content_parts));

    // 查询 qingka_wangke_class 表是否存在对应记录
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking = '$hid' AND noun = '$number' LIMIT 1");

    if (!$rs) {
        // 若记录不存在，执行插入操作
        $sql = "INSERT INTO qingka_wangke_class 
                (sort, name, getnoun, noun, price, queryplat, docking, yunsuan, content, addtime, status, fenlei) 
                VALUES 
                ('$current_sort', '$name', '$project_id', '$number', '$price', '$hid', '$hid', '*', '$content', '$now_time', '1', '$category')";

        $is = $DB->query($sql);
        if ($is) {
            $inserted_count++;
            $current_sort++;
        }
    } else {
        // 若记录存在，执行更新操作
        $sql = "UPDATE qingka_wangke_class 
                SET name = '$name', 
                    price = '$price', 
                    content = '$content',
                    status = '1'
                WHERE docking = '$hid' AND noun = '$number'";

        $is = $DB->query($sql);
        if ($is) {
            $updated_count++;
        }
    }

    // 同步到易教育专用表
    $project_data = array(
        'id' => $project['id'],
        'number' => $project['number'],
        'name' => $DB->escape($project['name']),
        'format' => $DB->escape($project['format'] ?? '账号 密码'),
        'url' => $DB->escape($project['url'] ?? ''),
        'price' => floatval($project['price'] ?? 0),
        'unitId' => $project['unitId'] ?? '1',
        'must' => $project['must'] ?? '1',
        'unit' => $DB->escape($project['unit'] ?? '年度'),
        'rate' => $project['rate'] ?? '1',
        'isExam' => $project['isExam'] ?? '0',
        'isSearchCourse' => $project['isSearchCourse'] ?? '1',
        'status' => 1,
        'description' => $DB->escape($project['description'] ?? ''),
        'remark' => $DB->escape($project['remark'] ?? '')
    );
    
    // 检查项目是否已存在
    $existing = $DB->get_row("SELECT id FROM qingka_wangke_jxjyclass WHERE id = '{$project['id']}'");
    
    if ($existing) {
        // 更新现有项目
        $update_sql = "UPDATE qingka_wangke_jxjyclass SET 
            number = '{$project_data['number']}',
            name = '{$project_data['name']}',
            format = '{$project_data['format']}',
            url = '{$project_data['url']}',
            price = {$project_data['price']},
            unitId = '{$project_data['unitId']}',
            must = '{$project_data['must']}',
            unit = '{$project_data['unit']}',
            rate = '{$project_data['rate']}',
            isExam = '{$project_data['isExam']}',
            isSearchCourse = '{$project_data['isSearchCourse']}',
            description = '{$project_data['description']}',
            remark = '{$project_data['remark']}'
            WHERE id = '{$project['id']}'";
        $DB->query($update_sql);
    } else {
        // 插入新项目
        $insert_sql = "INSERT INTO qingka_wangke_jxjyclass (
            id, number, name, format, url, price, unitId, must, unit, 
            rate, isExam, isSearchCourse, status, description, remark
        ) VALUES (
            '{$project_data['id']}', '{$project_data['number']}', '{$project_data['name']}', 
            '{$project_data['format']}', '{$project_data['url']}', {$project_data['price']}, 
            '{$project_data['unitId']}', '{$project_data['must']}', '{$project_data['unit']}', 
            '{$project_data['rate']}', '{$project_data['isExam']}', '{$project_data['isSearchCourse']}', 
            {$project_data['status']}, '{$project_data['description']}', '{$project_data['remark']}'
        )";
        $DB->query($insert_sql);
    }
    
    $sync_count++;
}

// 下架已删除的项目
if (!empty($project_ids)) {
    $ids_str = implode(',', array_map(function($id) use ($DB) {
        return "'" . $DB->escape($id) . "'";
    }, $project_ids));
    $sql = "UPDATE qingka_wangke_class SET status = 0 WHERE docking = '$hid' AND getnoun NOT IN ($ids_str)";
    $DB->query($sql);
}

// 返回操作结果
$total_processed = $inserted_count + $updated_count;
echo "\n=== 易教育同步完成 ===\n";
echo "处理项目总数: {$total_projects}\n";
echo "成功上架: {$inserted_count} 条\n";
echo "成功更新: {$updated_count} 条\n";
echo "总计处理: {$total_processed} 条记录\n";
echo "专用表同步: {$sync_count} 条\n";
echo "价格倍数: {$pricee}倍\n";
echo "同步时间: " . date('Y-m-d H:i:s') . "\n";
echo "=========================\n";

?>
