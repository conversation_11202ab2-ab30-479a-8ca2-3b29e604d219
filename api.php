<?php
include('confing/common.php'); 
$ckxz=$DB->get_row("select settings,api_ck,api_xd,api_tongbc,api_tongb,api_proportion from qingka_wangke_config");
$redis=new Redis();
$redis->connect("127.0.0.1","6379");
$redis->select(11);
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;
@header('Content-Type: application/json; charset=UTF-8');
if($conf['settings']!=1){
    exit('{"code":-1,"msg":"API功能已关闭，请联系管理员！"}');
}else{
    switch($act){
case 'getmoney':
    $uid = trim($_POST['uid']);
    $key = trim($_POST['key']);
    $key = md5($key);

    if ($uid == '' || $key == '') {
        exit('{"code":0,"msg":"所有项目不能为空"}');
    }

    if (!ctype_digit($uid)) {
        exit('{"code":0,"msg":"UID 必须是纯数字"}');
    }

    $sql = "SELECT * FROM qingka_wangke_user WHERE uid = ? LIMIT 1";
    $row = $DB->prepare_getrow($sql, [$uid]);

    if (!$row) {
        $result = ["code" => -3, "msg" => "用户不存在"];
        exit(json_encode($result));
    }

    if ($row['key'] == '0') {
        $result = ["code" => -1, "msg" => "你还没有开通接口哦"];
        exit(json_encode($result));
    } elseif ($row['key'] != $key) {
        $result = ["code" => -2, "msg" => "密匙错误"];
        exit(json_encode($result));
    } else {
        $result = [
            'code' => 1,
            'msg' => '查询成功',
            'money' => $row['money']
        ];
        exit(json_encode($result));
    }
    break;
  
  case 'get'://订单查询
       $ckmoney=$conf['api_ck'];
       $uid=daddslashes($_POST['uid']);
       $key=daddslashes($_POST['key']);
       $platform=daddslashes($_POST['platform']);
       $school=daddslashes($_POST['school']);
       $user=daddslashes($_POST['user']);
       $pass=daddslashes($_POST['pass']);
       $type=daddslashes($_POST['type']);
       $money=daddslashes($_POST['money']);
       $ck=$DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API查课' AND uid='$uid' ");
       $xd=$DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API添加任务' AND uid='$uid' ");
       $xdb=round($xd/$ck,4)*100;
       $key=md5($key);
        if($uid=='' || $key=='' || $platform=='' || $school=='' || $user=='' || $pass==''){
     	   exit('{"code":0,"msg":"所有项目不能为空"}');
        }
        $row = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE uid = ? LIMIT 1", [$uid]);
		$rs = $DB->prepare_getrow("SELECT * FROM qingka_wangke_class WHERE cid = ? LIMIT 1", [$platform]);
	     if($row['key']=='0'){
	     	$result=array("code"=>-1,"msg"=>"你还没有开通接口哦");
	     	exit(json_encode($result));
	     }elseif($row['key']!=$key){
	     	$result=array("code"=>-2,"msg"=>"密匙错误");
	     	exit(json_encode($result));
	     }elseif($row['money'] < $ckmoney){
	     	$result=array("code"=>-2,"msg"=>"余额小于{$ckmoney}禁止调用查课");
	     	exit(json_encode($result));
	     }elseif($rs['status'] == 0){
	     	$result=array("code"=>-2,"msg"=>"网课已下架禁止查课！");
	     	exit(json_encode($result));
	     }else{
	          if($xdb < $conf['api_proportion']){
	            $ckkf=$row['money']-0;
    	        $DB->query("update qingka_wangke_user set money='$ckkf' where uid='$uid' ");
    	        $rs=$DB->get_row("select * from qingka_wangke_class where cid='$platform' limit 1 ");	    
                $result=getWk($rs['queryplat'],$rs['getnoun'],$school,$user,$pass,$rs['name']);					
    	 		$result['userinfo']=$school." ".$user." ".$pass;
    		    wlog($uid,"API查课","{$rs['name']}-查课信息：{$school} {$user} {$pass}",0);	
        		    
        		    if($type=="xiaochu"){
        		    	foreach($result['data'] as $row){			    		    		
        		    		if($value==''){
        		    			$value=$row['name'];
        		    		}else{
        		    			$value=$value.','.$row['name'];
        		    		}	
        		    	}		 
        		    	$v[0]=$rs['name'];   	
        		    	$v[1]=$user;
        		    	$v[2]=$pass;
        		    	$v[3]=$school;
        		    	$v[4]=$value;	
        		    	$data=array(
        		    	  'code'=>$result['code'],
        		    	  'msg'=>$result['msg'],
        		    	  'data'=>$v,
        		    	  'js'=>'',
        		    	  'info'=>'昔日之苦，安知异日不在尝之? 共勉'
        		    	);
        		    	exit(json_encode($data));
        		    }else{
        		    	exit(json_encode($result));
        		    }		   		   
	          }
	          else{
	                $rs=$DB->get_row("select * from qingka_wangke_class where cid='$platform' limit 1 ");	    
                    $result=getWk($rs['queryplat'],$rs['getnoun'],$school,$user,$pass,$rs['name']);					
        	 		$result['userinfo']=$school." ".$user." ".$pass;
        		    wlog($uid,"API查课","{$rs['name']}-查课信息：{$school} {$user} {$pass}",0);	
        		    
        		    if($type=="xiaochu"){
        		    	foreach($result['data'] as $row){			    		    		
        		    		if($value==''){
        		    			$value=$row['name'];
        		    		}else{
        		    			$value=$value.','.$row['name'];
        		    		}	
        		    	}		 
        		    	$v[0]=$rs['name'];   	
        		    	$v[1]=$user;
        		    	$v[2]=$pass;
        		    	$v[3]=$school;
        		    	$v[4]=$value;	
        		    	$data=array(
        		    	  'code'=>$result['code'],
        		    	  'msg'=>$result['msg'],
        		    	  'data'=>$v,
        		    	  'js'=>'',
        		    	  'info'=>'昔日之苦，安知异日不在尝之? 共勉'
        		    	);
        		    	exit(json_encode($data));
        		    }else{
        		    	exit(json_encode($result));
        		    }
	          }
     }
  break;


case 'add':
    $xdmoney = $conf['api_xd'];
    $uid = daddslashes($_POST['uid']);
    $key = daddslashes($_POST['key']);
    $platform = daddslashes($_POST['platform']);
    $school = daddslashes($_POST['school']);
    $user = daddslashes($_POST['user']);
    $pass = daddslashes($_POST['pass']);
    $kcid = daddslashes($_POST['kcid']);
    $kcname = daddslashes($_POST['kcname']);
    $score = daddslashes($_POST['score']);
    $shichang = daddslashes($_POST['shichang']);
    $clientip = real_ip();
    $key = md5($key);

    if (!$uid || !$key || !$platform || !$school || !$user || !$pass || !$kcname) {
        exit('{"code":-1,"msg":"所有项目不能为空"}');
    }

    $row = $DB->prepare_getrow("SELECT * FROM qingka_wangke_user WHERE uid = ? LIMIT 1", [$uid]);
    $rs = $DB->prepare_getrow("SELECT * FROM qingka_wangke_class WHERE cid = ? LIMIT 1", [$platform]);

    if ($row['key'] == '0') {
        exit('{"code":-1,"msg":"你还没有开通接口哦"}');
    }

    if ($row['key'] != $key) {
        exit('{"code":-2,"msg":"密匙错误"}');
    } elseif ($row['money'] < $xdmoney) {
        $result = array("code" => -2, "msg" => "余额不足");
        exit(json_encode($result));
    } elseif ($rs['status'] == 0) {
        $result = array("code" => -2, "msg" => "小老弟，商品都下架了你还下什么单呢！");
        exit(json_encode($result));
    } else {
        $rs = $DB->get_row("select * from qingka_wangke_class where cid='$platform' limit 1 ");
        $res = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$rs['docking']}' limit 1 ");

        // 计算初始单价
        if ($rs['yunsuan'] == "*") {
            $danjia = round($rs['price'] * $row['addprice'], 2);
        } elseif ($rs['yunsuan'] == "+") {
            $danjia = round($rs['price'] + $row['addprice'], 2);
        } else {
            $danjia = round($rs['price'] * $row['addprice'], 2);
        }
        $danjia1 = $danjia; // 保存初始单价用于后续比较

        // 处理密价
        $mijia = $DB->get_row("select * from qingka_wangke_mijia where uid='{$uid}' and cid='{$platform}' ");
        $mijia_price = $danjia; // 默认等于当前单价
        if ($mijia) {
            if ($mijia['mode'] == 0) {
                $mijia_price = round($danjia - $mijia['price'], 2);
            } elseif ($mijia['mode'] == 1) {
                $mijia_price = round(($rs['price'] - $mijia['price']) * $row['addprice'], 2);
            } elseif ($mijia['mode'] == 2) {
                $mijia_price = $mijia['price'];
            }
            if ($mijia_price <= 0) {
                $mijia_price = 0;
            }
        }

        // 处理质押价格
        $zhiya_price = $danjia;
        $zhiya = $DB->get_row("SELECT zr.*, zc.discount_rate
            FROM qingka_wangke_zhiya_records zr
            LEFT JOIN qingka_wangke_zhiya_config zc ON zc.id=zr.config_id
            WHERE zr.uid='{$row['uid']}'
            AND zc.category_id='{$rs['fenlei']}'
            AND zr.status=1
            ORDER BY zr.id DESC LIMIT 1");

        if ($zhiya && $zhiya['discount_rate'] > 0) {
            $zhiya_price = round($danjia * $zhiya['discount_rate'], 2);
        }

        // 比较密价和质押价格，选择较低的价格
        if ($mijia && $zhiya && $zhiya['discount_rate'] > 0) {
            if ($mijia_price <= $zhiya_price) {
                $danjia = $mijia_price;
            } else {
                $danjia = $zhiya_price;
            }
        } elseif ($mijia) {
            $danjia = $mijia_price;
        } elseif ($zhiya && $zhiya['discount_rate'] > 0) {
            $danjia = $zhiya_price;
        }

        // 如果最终价格不低于初始单价，恢复初始单价
        if ($danjia >= $danjia1) {
            $danjia = $danjia1;
        }

        // 检查单价和用户加成
        if ($danjia == 0 || $row['addprice'] < 0.1) {
            exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
        }

        $c = explode(",", $kcname);
        $d = explode(",", $kcid);

        for ($i = 0; $i < count($c); $i++) {
            if ($row['money'] < $danjia * count($c)) {
                exit('{"code":-1,"msg":"余额不足以本次提交"}');
                return;
            }

            if ($DB->get_row("select * from qingka_wangke_order where ptname='{$rs['name']}' and school='$school' and user='$user' and pass='$pass' and kcid='$kcid' and kcname='$kcname' and score='$score' and shichang='$shichang' and dockstatus!=4")) {
                wlog($row['uid'], "API重复下单", "{$user} {$pass}未扣费，为您节省了{$danjia}元", -0);
                exit('{"code":0,"msg":"订单已存在，系统未扣费！","status":0,"message":"订单已存在，系统未扣费！"}');
                return;
                $dockstatus = '3'; // 重复下单
            } else {
                $dockstatus = '0';
            }

            $is = $DB->query("insert into qingka_wangke_order (uid,cid,hid,ptname,school,user,pass,kcid,kcname,fees,noun,miaoshua,addtime,ip,dockstatus,score,shichang) values ('{$uid}','{$rs['cid']}','{$rs['docking']}','{$rs['name']}','{$school}','$user','$pass','$d[$i]','$c[$i]','{$danjia}','{$rs['noun']}','$miaoshua','$date','$clientip','$dockstatus','$score','$shichang') ");

            if ($is) {
                $DB->query("update qingka_wangke_user set money=money-'{$danjia}' where uid='{$row['uid']}' limit 1 ");
                wlog($row['uid'], "API添加任务", "{$user} {$pass} {$c[$i]} 扣除{$danjia}元！", -$danjia);
                $ok = 1;
                $latest = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
                $money = $latest['money'];
                $token = $latest['tuisongtoken'];
            }
        }

        if ($ok == 1 & $money > 20) {
            exit('{"code":0,"msg":"提交成功，ID可以显示但我就是不显示","status":0,"message":"提交成功"}');
        } else if ($ok == 1 & $money < 20) {
            tuisong($row['uid'], "balance", "余额告警", "当前可用剩余{$money}，避免影响API调用，请及时查看。");
            exit('{"code":0,"msg":"提交成功，但你的余额不多了","status":0,"message":"提交成功"}');
        } else {
            exit('{"code":-1,"msg":"请完整输入课程名字","status":-1,"message":"请完整输入课程名字"}');
        }
    }
    break;



case 'addyqsl':
    $xdmoney = $conf['api_xd'];
    $uid = daddslashes($_POST['uid']);
    $key = daddslashes($_POST['key']);
    $platform = daddslashes($_POST['platform']);
    $school = daddslashes($_POST['school']);
    $user = daddslashes($_POST['user']);
    $pass = daddslashes($_POST['pass']);
    $kcid = daddslashes($_POST['kcid']);
    $kcname = daddslashes($_POST['kcname']);
    $score = daddslashes($_POST['score']);
    $shichang = daddslashes($_POST['shichang']);
    $clientip = real_ip();
    $key = md5($key);

    if (!$uid || !$key || !$platform || !$school || !$user || !$pass || !$kcname) {
        exit('{"code":-1,"msg":"不能为空"}');
    }

    $row = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
    $rs = $DB->get_row("select * from qingka_wangke_class where cid='$platform' limit 1 ");

    if ($row['key'] == '0') {
        exit('{"code":-1,"msg":"你还没有开通接口哦"}');
    }
    if ($row['key'] != $key) {
        exit('{"code":-2,"msg":"密匙错误"}');
    }
    if ($row['money'] < $xdmoney) {
        $result = array("code" => -2, "msg" => "余额不足");
        exit(json_encode($result));
    }
    if ($rs['status'] == 0) {
        $result = array("code" => -2, "msg" => "小老弟，商品都下架了你还下什么单呢！");
        exit(json_encode($result));
    }

    $rs = $DB->get_row("select * from qingka_wangke_class where cid='$platform' limit 1 ");
    $res = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$docking}' limit 1 ");

    // Calculate initial price
    if ($rs['yunsuan'] == "*") {
        $danjia = round($rs['price'] * $row['addprice'], 2);
        $danjia1 = $danjia;
    } elseif ($rs['yunsuan'] == "+") {
        $danjia = round($rs['price'] + $row['addprice'], 2);
        $danjia1 = $danjia;
    } else {
        $danjia = round($rs['price'] * $row['addprice'], 2);
        $danjia1 = $danjia;
    }

    // 密价 (Secret price) calculation
    $mijia = $DB->get_row("select * from qingka_wangke_mijia where uid='{$uid}' and cid='{$platform}' ");
    $mijia_price = $danjia;
    if ($mijia) {
        if ($mijia['mode'] == 0) {
            $mijia_price = round($danjia - $mijia['price'], 2);
        } elseif ($mijia['mode'] == 1) {
            $mijia_price = round(($rs['price'] - $mijia['price']) * $row['addprice'], 2);
        } elseif ($mijia['mode'] == 2) {
            $mijia_price = $mijia['price'];
        }
        if ($mijia_price <= 0) {
            $mijia_price = 0;
        }
    }

    // 质押 (Pledge) price calculation
    $zhiya_price = $danjia;
    $sql = "SELECT zr.*, zc.discount_rate FROM qingka_wangke_zhiya_records zr LEFT JOIN qingka_wangke_zhiya_config zc ON zc.id=zr.config_id WHERE zr.uid=? AND zc.category_id=? AND zr.status=1 ORDER BY zr.id DESC LIMIT 1";
    $zhiya = $DB->prepare_getrow($sql, [$row['uid'], $rs['fenlei']]);
    if ($zhiya && $zhiya['discount_rate'] > 0) {
        $zhiya_price = round($danjia * $zhiya['discount_rate'], 2);
    }

    // Determine final price
    if ($mijia && $zhiya && $zhiya['discount_rate'] > 0) {
        $danjia = ($mijia_price <= $zhiya_price) ? $mijia_price : $zhiya_price;
    } elseif ($mijia) {
        $danjia = $mijia_price;
    } elseif ($zhiya && $zhiya['discount_rate'] > 0) {
        $danjia = $zhiya_price;
    }

    // Ensure final price doesn't exceed original price
    if ($danjia >= $danjia1) {
        $danjia = $danjia1;
    }

    if ($danjia == 0 || $row['addprice'] < 0.1) {
        exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
    }
    if ($row['money'] < $danjia) {
        exit('{"code":-1,"msg":"余额不足以本次提交"}');
        return;
    }

    if ($DB->get_row("select * from qingka_wangke_order where ptname='{$rs['name']}' and school='$school' and user='$user' and pass='$pass' and kcid='$kcid' and kcname='$kcname' and score='$score' and shichang='$shichang' and dockstatus!=4")) {
        wlog($row['uid'], "API重复下单", "{$user} {$pass}未扣费，为您节省了{$danjia}元", -0);
        exit('{"code":0,"msg":"下单成功，订单已存在，系统未扣费！","status":0,"message":"下单成功，订单已存在，系统未扣费！"}');
        return;
        $dockstatus = '3';
    } else {
        $dockstatus = '0';
    }

    $is = $DB->query("insert into qingka_wangke_order (uid,cid,hid,ptname,school,user,pass,kcid,kcname,fees,noun,miaoshua,addtime,ip,dockstatus,score,shichang) values ('{$uid}','{$rs['cid']}','{$rs['docking']}','{$rs['name']}','{$school}','$user','$pass','$kcid','$kcname','{$danjia}','{$rs['noun']}','$miaoshua','$date','$clientip','$dockstatus','$score','$shichang') ");

    if ($is) {
        $DB->query("update qingka_wangke_user set money=money-'{$danjia}' where uid='{$row['uid']}' limit 1 ");
        wlog($row['uid'], "API添加任务", "{$user} {$pass} {$kcname} 扣除{$danjia}元！", -$danjia);
        $ok = 1;
        $latest = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
        $money = $latest['money'];
        $token = $latest['tuisongtoken'];
    }

    if ($ok == 1 && $money > 20) {
        exit('{"code":0,"msg":"提交成功，ID可以显示但我就是不显示","status":0,"message":"提交成功"}');
    } else if ($ok == 1 && $money < 20) {
        tuisong($row['uid'], "balance", "余额告警", "当前可用剩余{$money}，避免影响API调用，请及时查看。");
        exit('{"code":0,"msg":"提交成功，但你的余额不多了","status":0,"message":"提交成功"}');
    } else {
        exit('{"code":-1,"msg":"请完整输入课程名字","status":-1,"message":"请完整输入课程名字"}');
    }
    break;


   
case 'chadan':
    $username = trim($_POST['username']);
    $id = trim($_REQUEST['yid']);
    if (empty($username) && empty($id)) {
        $data = array('code' => -1, 'msg' => "账号和订单ID不能同时为空");
        exit(json_encode($data));
    }
    if (!empty($username)) {
        $sql = "SELECT * FROM qingka_wangke_order WHERE user = ? ORDER BY oid DESC";
        $params = [$username];
    } else {
        $sql = "SELECT * FROM qingka_wangke_order WHERE oid = ?";
        $params = [$id];
    }
    $stmt = $DB->prepare_query($sql, $params);

    if ($stmt) {
        $result = $stmt->get_result();
        $data = [];

        while ($row = $result->fetch_assoc()) {
            $data[] = array(
                'id' => $row['oid'],
                'ptname' => $row['ptname'],
                'school' => $row['school'],
                'name' => $row['name'],
                'user' => $row['user'],
                'kcname' => $row['kcname'],
                'addtime' => $row['addtime'],
                'courseStartTime' => $row['courseStartTime'],
                'courseEndTime' => $row['courseEndTime'],
                'examStartTime' => $row['examStartTime'],
                'examEndTime' => $row['examEndTime'],
                'status' => $row['status'],
                'process' => $row['process'],
                'remarks' => $row['remarks']
            );
        }
        $stmt->close();
        if(empty($data)){
            $data=array('code'=>-1,'msg'=>"未查到该账号的下单信息");
            exit(json_encode($data));
        }
        $data = array('code' => 1, 'data' => $data);
        exit(json_encode($data));
    } else {
        $data = array('code' => -1, 'msg' => "查询失败");
        exit(json_encode($data));
    }
break;
  

case 'chadan2':
    $id = trim(strip_tags($_POST['yid']));
    if (empty($id)) {
        $data = ['code' => -1, 'msg' => "订单ID不能为空"];
        exit(json_encode($data));
    }

    $a = $DB->prepare_getrow("SELECT * FROM qingka_wangke_order WHERE oid = ?", [$id]);

    if ($a) {
        $oid = $id;
        $gailv = $conf['api_tongb'];
        $shouldProcess = rand(1, $gailv);
        $orderhid = $a['hid'];

        if ($shouldProcess == '1' && ($orderhid != '0')) {
            $result = processCx($oid);
            foreach ($result as $row) {
                $db_row = $DB->prepare_getrow("SELECT * FROM qingka_wangke_order WHERE user = ? AND kcname = ? AND oid = ?", [$row['user'], $row['kcname'], $oid]);

                if ($db_row) {
                    $update_query = "UPDATE qingka_wangke_order SET
                        yid = ?,
                        status = ?,
                        courseStartTime = ?,
                        courseEndTime = ?,
                        examStartTime = ?,
                        examEndTime = ?,
                        process = ?,
                        remarks = ?
                        WHERE user = ? AND kcname = ? AND oid = ?";
                    $DB->prepare_query($update_query, [
                        $row['yid'],
                        $row['status_text'],
                        $row['kcks'],
                        $row['kcjs'],
                        $row['ksks'],
                        $row['ksjs'],
                        $row['process'],
                        $row['remarks'],
                        $row['user'],
                        $row['kcname'],
                        $oid
                    ]);
                }
            }
        }

        $data = [];
        $latest_data = $DB->prepare_query("SELECT * FROM qingka_wangke_order WHERE oid = ?", [$oid]);
        while ($row = $latest_data->fetch($latest_data)) {
            $data[] = [
                'id' => $row['oid'],
                'ptname' => $row['ptname'],
                'school' => $row['school'],
                'name' => $row['name'],
                'user' => $row['user'],
                'kcname' => $row['kcname'],
                'addtime' => $row['addtime'],
                'courseStartTime' => $row['courseStartTime'],
                'courseEndTime' => $row['courseEndTime'],
                'examStartTime' => $row['examStartTime'],
                'examEndTime' => $row['examEndTime'],
                'status' => $row['status'],
                'process' => $row['process'],
                'remarks' => $row['remarks']
            ];
        }

        $dataResponse = ['code' => 1, 'data' => $data];
        exit(json_encode($dataResponse));
    } else {
        $data = ['code' => -1, 'msg' => "未查到该账号的下单信息"];
        exit(json_encode($data));
    }
    break;


case 'plchadan':
    $uid = daddslashes($_POST['uid']);
    $key = daddslashes($_POST['key']);
    $key = md5($key);
    $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
    $timestamp = isset($_POST['timestamp']) ? intval($_POST['timestamp']) : 0;

    $userrow = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid='$uid' LIMIT 1");
    if ($userrow['key'] == '0') {
        exit('{"code":-1,"msg":"你还没有开通接口哦"}');
    }
    if ($userrow['key'] != $key) {
        exit('{"code":-2,"msg":"密匙错误"}');
    }
    if ($uid == "" || $key == "") {
        exit(json_encode(array('code' => -1, 'msg' => "uid和key不能为空")));
    }

    // 连接到Redis
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379); // 请根据您的Redis配置修改

    // 检查请求频率
    $last_request_time = $redis->get("plchadan_last_request:$uid");
    $current_time = time();
    if ($last_request_time && ($current_time - $last_request_time) < 60) {
        exit(json_encode(array('code' => -3, 'msg' => "请求过于频繁，请1分钟后再试")));
    }

    // 更新最后请求时间
    $redis->set("plchadan_last_request:$uid", $current_time);
    // 将时间戳转换为MySQL datetime格式
    $datetime = date('Y-m-d H:i:s', $timestamp);

    $query = "SELECT * FROM qingka_wangke_order WHERE uid = '$uid' AND updatetime > '$datetime' ORDER BY updatetime ASC LIMIT $offset, 10000";

    $result = $DB->query($query);
    if ($result) {
        $data = array();
        while ($row = $DB->fetch($result)) {
            $data[] = array(
                'id' => $row['oid'],
                'status' => $row['status'],
                'remarks' => $row['remarks'],
                'process' => $row['process'],
                'kcks' => $row['courseStartTime'],
                'kcjs' => $row['courseEndTime'],
                'ksks' => $row['examStartTime'],
                'ksjs' => $row['examEndTime'],
                'user' => $row['user'],
                'pass' => $row['pass'],
                'kcname' => $row['kcname'],
                'cid' => $row['cid']
            );
        }
        $response = array('code' => 1, 'data' => $data);
    } else {
        $response = array('code' => -1, 'msg' => "未查到更新的订单信息");
    }
    
    exit(json_encode($response));
    break;

case 'budan':
        $uid=daddslashes($_POST['uid']);
        $oid=daddslashes($_POST['id']);
		$b=$DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
		if($b){
		if($b['bsnum']>15){
			exit('{"code":-1,"msg":"该订单补刷已超过15次，年轻人，你做个人吧！"}');
		}
        	  $c=budanWk($oid);
              wlog($uid, "API补刷", "补刷了订单 $oid", -0.00);
        	  if($c['code']==1){
                    if($b['status']=="退款已完成" || $b['status']=="已取消"){
                        jsonReturn(1,"订单已无法补刷！");
                    }else{
                        $DB->query("update qingka_wangke_order set status='补刷中',`bsnum`=bsnum+1 where oid='{$oid}' ");
        	  	        jsonReturn(1,$c['msg']);
                    }
        	  }else{
        	  	jsonReturn(-1,$c['msg']);
        	  }          
		}else{
        	  	jsonReturn(-1,"oid无效");
        	  }   
  break;
  case 'zt':
        $oid=daddslashes($_POST['id']);
		$b=$DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
        	  $c=ztWk($oid);
        	  if($c['code']==1){
        	      if($b['status']=="已退款"){
                        jsonReturn(1,"给你退款了还想暂停？");
                    }else{
                        $DB->query("update qingka_wangke_order set status='已停止',`bsnum`=bsnum+1 where oid='{$oid}' ");
        	  	        jsonReturn(1,$c['msg']);
                    }
        	  	
        	  }else{
        	  	jsonReturn(-1,$c['msg']);
        	  }          
  break;
  case 'ms':
        $oid=daddslashes($_POST['id']);
		$b=$DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
		
        	  $c=msWk($oid);
        	  if($c['code']==1){
        	      if($b['status']=="已退款"){
                        jsonReturn(1,"订单已退款无法操作");
                    }else{
                        $DB->query("update qingka_wangke_order set status='转换中',`bsnum`=bsnum+1 where oid='{$oid}' ");
        	  	        jsonReturn(1,$c['msg']);
                    }
        	  	
        	  }else{
        	  	jsonReturn(-1,$c['msg']);
        	  }          
  break;
case 'xgmm':
    $oid=daddslashes($_POST['id']);
    $xgmm = strip_tags(daddslashes($_POST['xgmm']));
        if (empty($xgmm)) {
        jsonReturn(-1, "密码不能为空");
    }
        if (strlen($xgmm) < 3) {
        jsonReturn(-1, "密码长度至少为3位");
    }   else {
			$b = xgmm($oid,$xgmm);
			if ($b['code'] == 1) {
              
              $DB->query("UPDATE qingka_wangke_order SET pass = '{$xgmm}' WHERE oid = '{$oid}'");
              $DB->query("update qingka_wangke_user set money=money-0.05 where uid='{$userrow['uid']}' limit 1 ");
				wlog($userrow['uid'], "修改密码", "订单{$oid}修改密码成功扣除0.05", -0.05);
				jsonReturn(1, $b['msg']);
			} else {
				jsonReturn(-1, $b['msg']);
			}
		}
    break;
case 'getclass':
    $uid = trim(strip_tags(daddslashes($_POST['uid'])));
    $key = trim(strip_tags(daddslashes($_POST['key'])));
    $key=md5($key);
    if ($uid == '' || $key == '') {
        exit('{"code":0,"msg":"所有项目不能为空"}');
    }
    $userrow = $DB->get_row("select * from qingka_wangke_user where uid='$uid' limit 1");
    if ($userrow['key'] == '0') {
        $result = array("code" => -1, "msg" => "你还没有开通接口哦");
        exit(json_encode($result));
    } elseif ($userrow['key'] != $key) {
        $result = array("code" => -2, "msg" => "密匙错误");
        exit(json_encode($result));
    }
    if ($_REQUEST['cid']) {
        $a = $DB->query("select * from qingka_wangke_class where status=1 and cid = '{$_REQUEST['cid']}' order by sort desc");
    } else {
        $a = $DB->query("select * from qingka_wangke_class where status=1 order by sort desc");
    }
    $data = [];
    while ($row = $DB->fetch($a)) {
        // Calculate initial price based on yunsuan
        if ($row['yunsuan'] == "*") {
            $price = round($row['price'] * $userrow['addprice'], 2);
            $price1 = $price;
        } elseif ($row['yunsuan'] == "+") {
            $price = round($row['price'] + $userrow['addprice'], 2);
            $price1 = $price;
        } else {
            $price = round($row['price'] * $userrow['addprice'], 2);
            $price1 = $price;
        }

        // Get category name
        $fenlei = $DB->get_row("select * from qingka_wangke_fenlei where id='{$row['fenlei']}' ");
        $fenleiname = $fenlei['name'];

        // Handle mijia (secret price)
        $mijia = $DB->get_row("select * from qingka_wangke_mijia where uid='{$userrow['uid']}' and cid='{$row['cid']}' ");
        $mijia_price = $price; // Default to current price
        if ($mijia) {
            if ($mijia['mode'] == 0) {
                $mijia_price = round($price - $mijia['price'], 2);
            } elseif ($mijia['mode'] == 1) {
                $mijia_price = round(($row['price'] - $mijia['price']) * $userrow['addprice'], 2);
            } elseif ($mijia['mode'] == 2) {
                $mijia_price = $mijia['price'];
            }
            if ($mijia_price <= 0) {
                $mijia_price = 0;
            }
        }

        // Handle zhiya (pledge price)
        $zhiya_price = $price; // Default to current price
        $zhiya = $DB->get_row("SELECT zr.*, zc.discount_rate
            FROM qingka_wangke_zhiya_records zr
            LEFT JOIN qingka_wangke_zhiya_config zc ON zc.id=zr.config_id
            WHERE zr.uid='{$userrow['uid']}'
            AND zc.category_id='{$row['fenlei']}'
            AND zr.status=1
            ORDER BY zr.id DESC LIMIT 1");
        if ($zhiya && $zhiya['discount_rate'] > 0) {
            $zhiya_price = round($price * $zhiya['discount_rate'], 2);
        }

        // Compare mijia and zhiya prices, select the lower one
        if ($mijia && $zhiya && $zhiya['discount_rate'] > 0) {
            if ($mijia_price <= $zhiya_price) {
                $price = $mijia_price;
                $row['name'] = "【密价】{$row['name']}";
            } else {
                $price = $zhiya_price;
                $row['name'] = "【质押优惠】{$row['name']}";
            }
        } elseif ($mijia) {
            $price = $mijia_price;
            $row['name'] = "【密价】{$row['name']}";
        } elseif ($zhiya && $zhiya['discount_rate'] > 0) {
            $price = $zhiya_price;
            $row['name'] = "【质押优惠】{$row['name']}";
        }

        // If final price is greater than or equal to original price, revert to original
        if ($price >= $price1) {
            $price = $price1;
            $row['name'] = str_replace(['【密价】', '【质押优惠】'], '', $row['name']);
        }

        // Collect data for output
        $data[] = array(
            'sort' => $row['sort'],
            'cid' => $row['cid'],
            'kcid' => $row['kcid'],
            'name' => $row['name'],
            'noun' => $row['noun'],
            'price' => $price,
            'fenlei' => $row['fenlei'],
            'content' => $row['content'],
            'status' => $row['status'],
            'miaoshua' => $miaoshua, // Note: $miaoshua is undefined in the original code
            'fenleiname' => $fenleiname
        );
    }

    // Sort data
    $sort = array_column($data, 'sort');
    $cid = array_column($data, 'cid');
    array_multisort($sort, SORT_ASC, $cid, SORT_DESC, $data);

    // Output JSON
    $data = array('code' => 1, 'data' => $data);
    exit(json_encode($data));
    break;
}
}


?>