*{margin: 0; padding: 0; }
html,body{height: 100%; background-color: #FFF;} 
.app{height: 100%;}
.app *{transition: all 0.2s; font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",<PERSON><PERSON>,sans-serif;}

/* 左边 */
.nav-left{
	width: 200px;
	height: 100%;
	position: fixed;
	left: 0px;
}
/* 左上 logo部分 */
.nav-left-top{width: 200px; height: 75px; line-height: 75px; cursor: pointer;}
.admin-logo{width: 40px; height: 40px; border-radius: 50%; vertical-align: middle; margin: 0.5em; margin-left: 1em;}
/* ==== .nav-left-top:hover{color: #FFF;} */

/* 左下 */
.nav-left-bottom{
	width: 100%;
	height: calc(100% - 75px);
	overflow: hidden;
}
/* 1 2 配合，把滚动条隐藏 */
.menu-box-1{width: 220px; height: 100%; overflow-y: auto;}
.menu-box-2{width: 201px;}

/* 尺寸调整 */
.el-submenu__title,.el-menu-item{height: 45px !important; line-height: 45px !important;}
.el-submenu .el-menu-item{height: 40px !important; line-height: 40px !important;}

/* 隐藏右边框 */
.el-menu{border: 0px;}


/* -------- 右边 --------- */
.nav-right{
	width: calc(100% - 200px);
	height: 100%;
	position: fixed;
	left: 200px; 
}

/* 第一行建筑物 */
.nav-right-1{height: 39px; line-height: 39px; border-bottom: 1px #DDD solid; }

.tools-left{border: 0px #000 solid; float: left;}
.tools-right{float: right;}
.tool-fox{padding: 0 1em; display: inline-block; cursor: pointer;}


.user-info{position: relative; top: -2px;}
.user-avatar{width: 30px; height: 30px; border-radius: 50%; vertical-align: middle;}
.user-info .user-name{font-size: 0.9em;} 

/* 第二行建筑物 */
.nav-right-2{height: 35px; position: relative; z-index: 110; box-shadow: 0 2px 2px #CCC;}
.nav-right-2>div{height: 100%; position: absolute;}

.towards-left,.towards-right{width: 25px; text-align: center; background-color: #FFF; cursor: pointer; line-height: 35px;} 
.towards-right{right: 0px;}
.towards-left:hover i,.towards-right:hover i{font-size: 1.1em;font-weight: bold;}

.towards-middle{width: 10000px; overflow: auto;/* calc(100% - 50px) */ left: 25px;background-color: #FFF;}
.page-title-box{display: inline-block; position: absolute; left: 0px; }
.page-title {
    padding: 0 8px;
    margin: 0 3px;
    margin-top: 1px;
    height: 30px;
    line-height: 30px;
    background-color: #FFF;        /* 白色背景 */
    font-size: 0.8em;
    border: 1px solid #DDD;        /* 灰色边框 */
    border-radius: 6px;            /* 保持圆角 */
    cursor: pointer;
    display: inline-block;
    box-sizing: border-box;        /* 确保边框不影响整体尺寸 */
}
.page-title .el-icon-caret-left{color: #EEE; font-size: 1.7em; position: relative; top: 4px;}
.page-title .el-icon-document-copy:hover{background-color: #D8D9DD;border-radius: 25%;} 
.page-title .el-icon-document-copy{color: #b6b7ba; margin-left: -0.5em;}
.page-title .el-icon-close:hover{background-color: red; color: #FFF;} 
.page-title .el-icon-close{display: inline-block; border-radius: 50%; padding: 0px; color: #999; margin-left: 0.5em;}
.page-title span{display: inline-block; margin-right: 1em;}
.page-title:hover span,.page-native span{font-weight: bold;}
.page-title:hover,.page-title.page-native{border: 1px solid #DDD; border-radius: 6px;  padding: 0 10px; margin: 0 1px;}

/* 第三杠 */
.nav-right-3{width: 100%; height: calc(100% - 75px); position: relative;}
.nav-right-3 iframe{width: 100%; height: 100%; border: 0px; position: absolute; z-index: 100; background-color: #FFF;}


/* 右键菜单 样式 */
.right-box{
	width: 100px;
	font-size: 0.8em;
	border: 1px #CCC solid;
	border-radius: 1px;
	background-color: #FFF;
	padding: 0.5em 0;
	position: fixed;
	z-index: 99999999999;
}
.right-box>div{line-height: 2.2em; text-indent: 1em; cursor: pointer;}
.right-box>div:hover{background-color: #EEE;}


/* 菜单折叠时候样式调整 */
.fold .nav-left{width: 64px;}
.fold .admin-logo{width: 40px; height: 40px;}
.fold .nav-right{width: calc(100% - 64px); left: 64px; }

/* 菜单折叠时 部分元素隐藏 */
.fold .nav-title,
.fold .menu-name
{display: none;}

/* 折叠时悬浮菜单样式，防止透明 */
.el-menu--vertical .el-menu--popup{background-color: #FFF !important; color: red !important;}


/* Element UI MessageBox 移动端居中样式 */
/* 针对移动端的 MessageBox 居中适配 */
@media only screen and (max-width: 768px) {
  .el-message-box {
    /* 确保 MessageBox 宽度适配移动端屏幕 */
    width: 90% !important;
    max-width: 400px; /* 设置最大宽度，防止过宽 */
    min-width: 280px; /* 设置最小宽度，防止过窄 */
    /* 居中显示 */
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    /* 确保定位为固定，覆盖默认定位 */
    position: fixed !important;
    margin: 0 !important; /* 清除可能干扰的 margin */
  }

  /* 修复可能出现的垂直滚动问题 */
  .el-message-box__wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh; /* 占满视口高度 */
    overflow: auto; /* 允许内容滚动 */
  }

  /* 确保遮罩层覆盖全屏 */
  .v-modal {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }
}

/* 小屏菜单隐藏样式 */
.menu-hidden .nav-left {
    display: none;
}

/* ========== 修复下拉框项目名称显示不全问题 ========== */
/* Element UI 下拉框选项样式优化 */
.el-select-dropdown {
    min-width: 400px !important; /* 增加下拉框最小宽度 */
    max-width: 600px !important; /* 设置最大宽度防止过宽 */
}

.el-select-dropdown .el-option {
    height: auto !important; /* 允许选项高度自适应 */
    min-height: 34px !important; /* 设置最小高度 */
    line-height: 1.4 !important; /* 调整行高 */
    padding: 8px 20px !important; /* 增加内边距 */
    white-space: normal !important; /* 允许文本换行 */
    word-wrap: break-word !important; /* 长单词换行 */
    overflow: visible !important; /* 显示溢出内容 */
}

/* 选项内容布局优化 */
.el-option .el-option-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    width: 100% !important;
    flex-wrap: wrap !important; /* 允许换行 */
}

/* 项目名称样式 */
.el-option .el-option-content .project-name {
    flex: 1 !important;
    max-width: none !important; /* 移除最大宽度限制 */
    overflow: visible !important; /* 显示溢出内容 */
    text-overflow: initial !important; /* 移除省略号 */
    white-space: normal !important; /* 允许换行 */
    word-break: break-all !important; /* 强制换行 */
    margin-right: 10px !important; /* 与价格保持间距 */
}

/* 价格标签样式 */
.el-option .el-option-content .project-price {
    flex-shrink: 0 !important; /* 价格不缩放 */
    color: #8492a6 !important;
    font-size: 13px !important;
    margin-left: auto !important; /* 右对齐 */
}

/* 针对现有的span标签样式覆盖 */
.el-option span[style*="max-width: 230px"] {
    max-width: none !important;
    overflow: visible !important;
    text-overflow: initial !important;
    white-space: normal !important;
}

/* 下拉框输入框样式优化 */
.el-select .el-input__inner {
    text-overflow: ellipsis !important; /* 输入框内容过长时显示省略号 */
}

/* 移动端适配 */
@media only screen and (max-width: 768px) {
    .el-select-dropdown {
        min-width: 300px !important;
        max-width: 95vw !important; /* 移动端不超过视口宽度 */
    }

    .el-option .el-option-content {
        flex-direction: column !important; /* 移动端垂直布局 */
        align-items: flex-start !important;
    }

    .el-option .el-option-content .project-price {
        margin-left: 0 !important;
        margin-top: 4px !important;
    }
}
.menu-hidden .nav-right {
    left: 0 !important;
    width: 100% !important;
}
}
