.layui-btn-hover {
    background-color: #FF6600 !important;
}

.layui-btn-disabled {
    background-color: #ABBCC6 !important;
    color: #fff !important;
}

.example-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, 90px); /* 自动填充固定宽度列 */
  justify-content: center; /* 整体居中 */
  gap: 5px;
}

.example-box .layui-btn {
    margin: 0;
    width: 88px;
    height: 53px;
}
.channel-category {
    border: 1px solid #606060 !important;
    background-color: rgba(255,255,255,0.9) !important; /* 半透明白底 */
    box-shadow: 0 2px 8px rgba(0,52,89,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-radius: 6px !important;
    font-size: 11px; /* 调整字体大小 */
}

.channel-category:active {
    background-color: rgba(0,52,89,0.1) !important; /* 深蓝底色 */
    box-shadow: 
        inset 0 2px 4px rgba(0,0,0,0.1),
        0 1px 2px rgba(0,52,89,0.2);
    transform: translateY(1px);
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(rgba(255,255,255,0.2), transparent);
        mix-blend-mode: overlay;
    }
}
 
/* 禁用状态适配 */
.layui-btn-disabled {
    border-color: #ced4da !important;
    background-image: linear-gradient(45deg,#666,#999) !important;
    opacity: 0.7;
}
.channel-category {
    border-radius: 6px !important;
}
.resource-item label {
    background: #ffffff;
    border: 1px solid #e0e0e0;
}
.resource-item label:hover {
    background: #f8f9fa;
    border-color: #007bff;
}
.resource-item input[type="checkbox"]:checked + span {
    color: #007bff;
}
.resource-item label:has(input[type="checkbox"]:checked) {
    background: #e6f3ff;
    border-color: #007bff;
}
.resource-item span {
    white-space: normal; /* Allow text to wrap */
    word-break: break-all; /* Break long words if necessary */
}