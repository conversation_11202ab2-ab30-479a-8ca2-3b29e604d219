/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableStickyHeader={exports:{}}.exports}})(this,function(){'use strict';function a(a,b){if(!(a instanceof b))throw new TypeError('Cannot call a class as a function')}function b(a,b){if(!a)throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called');return b&&('object'==typeof b||'function'==typeof b)?b:a}function c(a,b){if('function'!=typeof b&&null!==b)throw new TypeError('Super expression must either be null or a function, not '+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}var d=function(){function a(a,b){for(var c,d=0;d<b.length;d++)c=b[d],c.enumerable=c.enumerable||!1,c.configurable=!0,'value'in c&&(c.writable=!0),Object.defineProperty(a,c.key,c)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=function a(b,c,d){null===b&&(b=Function.prototype);var e=Object.getOwnPropertyDescriptor(b,c);if(e===void 0){var f=Object.getPrototypeOf(b);return null===f?void 0:a(f,c,d)}if('value'in e)return e.value;var g=e.get;return void 0===g?void 0:g.call(d)};(function(f){var g=f.fn.bootstrapTable.utils;f.extend(f.fn.bootstrapTable.defaults,{stickyHeader:!1,stickyHeaderOffsetY:0});var h=4===g.bootstrapVersion?'d-none':'hidden';f.BootstrapTable=function(g){function i(){return a(this,i),b(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return c(i,g),d(i,[{key:'initHeader',value:function(){for(var a,b=this,c=arguments.length,d=Array(c),g=0;g<c;g++)d[g]=arguments[g];(a=e(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),'initHeader',this)).call.apply(a,[this].concat(d)),this.options.stickyHeader&&(this.$el.before('<div class="sticky-header-container"></div>'),this.$el.before('<div class="sticky_anchor_begin"></div>'),this.$el.after('<div class="sticky_anchor_end"></div>'),this.$header.addClass('sticky-header'),this.$stickyContainer=this.$tableBody.find('.sticky-header-container'),this.$stickyBegin=this.$tableBody.find('.sticky_anchor_begin'),this.$stickyEnd=this.$tableBody.find('.sticky_anchor_end'),this.$stickyHeader=this.$header.clone(!0,!0),f(window).on('resize.sticky-header-table',function(){return b.renderStickyHeader()}),f(window).on('scroll.sticky-header-table',function(){return b.renderStickyHeader()}),this.$tableBody.off('scroll').on('scroll',function(){return b.matchPositionX()}))}},{key:'renderStickyHeader',value:function(){var a=this,b=f(window).scrollTop(),c=this.$stickyBegin.offset().top-this.options.stickyHeaderOffsetY,d=this.$stickyEnd.offset().top-this.options.stickyHeaderOffsetY-this.$header.height();b>c&&b<=d?(this.$stickyHeader.find('tr:eq(0)').find('th').each(function(b,c){f(c).css('min-width',a.$header.find('tr:eq(0)').find('th').eq(b).css('width'))}),this.$stickyContainer.removeClass(h).addClass('fix-sticky fixed-table-container'),this.$stickyContainer.css('top',this.options.stickyHeaderOffsetY+'px'),this.$stickyTable=f('<table/>'),this.$stickyTable.addClass(this.options.classes),this.$stickyContainer.html(this.$stickyTable.append(this.$stickyHeader)),this.matchPositionX()):this.$stickyContainer.removeClass('fix-sticky').addClass(h)}},{key:'matchPositionX',value:function(){this.$stickyContainer.scrollLeft(this.$tableBody.scrollLeft())}}]),i}(f.BootstrapTable)})(jQuery)});