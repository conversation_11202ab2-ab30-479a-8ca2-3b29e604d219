### 2. 8090教育认证机制

**认证类型**: JWT Token认证

**登录流程**:

```python
# API端点
POST http://1.14.58.242:8090/api/login

# 请求数据
{
    "username": "用户名",
    "password": "密码"
}

# 响应格式
{
    "code": 200,
    "state": true,
    "data": {
        "token": "JWT_TOKEN_STRING"
    }
}
```

**Token使用**:

- Header格式: `Authorization: {token}` (注意：不使用Bearer前缀)
- 双API架构: 主API(8090端口) + 查课API(15888端口)
- 缓存机制: 网站列表支持本地缓存

**关键特点**:

- Token格式与易教育略有不同，不使用Bearer前缀
- 拥有专用的查课API服务器
- 支持本地搜索过滤，减少API调用
### 8090教育API接口

| 功能     | 方法 | 端点                             | 说明                   |
| -------- | ---- | -------------------------------- | ---------------------- |
| 登录     | POST | `/api/login`                     | JWT认证登录            |
| 用户信息 | GET  | `/api/user/info`                 | 获取用户基本信息       |
| 公告列表 | GET  | `/api/announcements`             | 获取系统公告           |
| 网站列表 | GET  | `/api/order/website/list`        | 获取所有网站(支持缓存) |
| 网站详情 | GET  | `/api/order/website/info`        | 获取单个网站详情       |
| 查询课程 | POST | `http://1.14.58.242:15888/query` | 专用查课API            |
| 提交订单 | POST | `/api/order/submit`              | 提交订单               |
| 订单列表 | GET  | `/api/order/list`                | 获取订单列表           |

### 8090教育特点  

- **双API架构**: 主API负责基本功能，专用查课API负责课程查询
- **Token格式**: 不使用Bearer前缀，直接使用token值
