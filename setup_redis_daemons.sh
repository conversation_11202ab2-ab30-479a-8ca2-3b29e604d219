#!/bin/bash

# Redis守护进程一键配置脚本
# 适用于宝塔面板环境

echo "=========================================="
echo "Redis守护进程一键配置脚本"
echo "=========================================="

# 设置基础路径
BASE_PATH="/www/wwwroot/117.72.158.75"
REDIS_PATH="$BASE_PATH/redis"
PHP_BIN="/usr/bin/php"

# 检查PHP是否存在
if [ ! -f "$PHP_BIN" ]; then
    echo "错误: 未找到PHP可执行文件 $PHP_BIN"
    echo "请检查PHP安装路径"
    exit 1
fi

# 检查redis目录是否存在
if [ ! -d "$REDIS_PATH" ]; then
    echo "错误: Redis目录不存在 $REDIS_PATH"
    exit 1
fi

echo "基础路径: $BASE_PATH"
echo "Redis路径: $REDIS_PATH"
echo "PHP路径: $PHP_BIN"
echo ""

# 创建systemd服务文件的函数
create_systemd_service() {
    local service_name=$1
    local php_file=$2
    local description=$3
    
    cat > "/etc/systemd/system/${service_name}.service" << EOF
[Unit]
Description=${description}
After=network.target
Wants=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=${REDIS_PATH}
ExecStart=${PHP_BIN} ${REDIS_PATH}/${php_file}
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    echo "创建服务: ${service_name}"
}

echo "正在创建systemd服务文件..."
echo ""

# 创建持续运行的守护进程服务
create_systemd_service "redis-chusheng1" "chusheng1.php" "Redis队列处理器1"
create_systemd_service "redis-chusheng2" "chusheng2.php" "Redis队列处理器2"
create_systemd_service "redis-chusheng3" "chusheng3.php" "Redis队列处理器3"
create_systemd_service "redis-addchu" "addchu.php" "Redis订单添加处理器"
create_systemd_service "redis-plbs" "chushengplbs.php" "Redis批量补刷处理器"
create_systemd_service "redis-plms" "chushengplms.php" "Redis批量秒刷处理器"
create_systemd_service "redis-pltx" "chushengpltx.php" "Redis批量停止处理器"

# 重新加载systemd
echo "重新加载systemd配置..."
systemctl daemon-reload

# 启用并启动服务
echo ""
echo "启用并启动服务..."

services=("redis-chusheng1" "redis-chusheng2" "redis-chusheng3" "redis-addchu" "redis-plbs" "redis-plms" "redis-pltx")

for service in "${services[@]}"; do
    echo "启用服务: $service"
    systemctl enable "$service"
    echo "启动服务: $service"
    systemctl start "$service"
    
    # 检查服务状态
    if systemctl is-active --quiet "$service"; then
        echo "✅ $service 启动成功"
    else
        echo "❌ $service 启动失败"
        systemctl status "$service" --no-pager -l
    fi
    echo ""
done

echo "=========================================="
echo "创建定时任务脚本..."
echo "=========================================="

# 创建定时任务脚本
cat > "$BASE_PATH/redis_cron_tasks.sh" << 'EOF'
#!/bin/bash

# Redis定时任务脚本
BASE_PATH="/www/wwwroot/117.72.158.75"
PHP_BIN="/usr/bin/php"

# 记录日志的函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$BASE_PATH/logs/redis_cron.log"
}

# 创建日志目录
mkdir -p "$BASE_PATH/logs"

# 执行入队脚本
log_message "开始执行Redis入队任务"

# 订单状态入队
$PHP_BIN "$BASE_PATH/redis/aaru.php" >> "$BASE_PATH/logs/aaru.log" 2>&1
log_message "aaru.php 执行完成"

# 新订单入队
$PHP_BIN "$BASE_PATH/redis/addru.php" >> "$BASE_PATH/logs/addru.log" 2>&1
log_message "addru.php 执行完成"

# 订单状态更新入队
$PHP_BIN "$BASE_PATH/redis/bbru.php" >> "$BASE_PATH/logs/bbru.log" 2>&1
log_message "bbru.php 执行完成"

# 进行中订单入队
$PHP_BIN "$BASE_PATH/redis/ccru.php" >> "$BASE_PATH/logs/ccru.log" 2>&1
log_message "ccru.php 执行完成"

# 待考试订单入队
$PHP_BIN "$BASE_PATH/redis/ddru.php" >> "$BASE_PATH/logs/ddru.log" 2>&1
log_message "ddru.php 执行完成"

# 其他状态订单入队
$PHP_BIN "$BASE_PATH/redis/eeru.php" >> "$BASE_PATH/logs/eeru.log" 2>&1
log_message "eeru.php 执行完成"

log_message "Redis入队任务全部执行完成"
EOF

chmod +x "$BASE_PATH/redis_cron_tasks.sh"

echo "定时任务脚本已创建: $BASE_PATH/redis_cron_tasks.sh"
echo ""

echo "=========================================="
echo "添加到系统crontab..."
echo "=========================================="

# 添加到crontab
(crontab -l 2>/dev/null; echo "# Redis队列入队任务") | crontab -
(crontab -l 2>/dev/null; echo "*/5 * * * * $BASE_PATH/redis_cron_tasks.sh") | crontab -

echo "已添加定时任务到crontab (每5分钟执行一次)"
echo ""

echo "=========================================="
echo "配置完成！"
echo "=========================================="
echo ""
echo "守护进程状态:"
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "✅ $service: 运行中"
    else
        echo "❌ $service: 未运行"
    fi
done

echo ""
echo "常用命令:"
echo "查看所有服务状态: systemctl status redis-*"
echo "重启所有服务: systemctl restart redis-*"
echo "停止所有服务: systemctl stop redis-*"
echo "查看服务日志: journalctl -u redis-chusheng1 -f"
echo "查看定时任务日志: tail -f $BASE_PATH/logs/redis_cron.log"
echo ""
echo "注意: 请确保Redis服务已启动并正常运行"
echo "可以通过 'redis-cli ping' 测试Redis连接"
