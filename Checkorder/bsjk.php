<?php  
function budanWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,yid,user from qingka_wangke_order where oid='{$oid}' ");
	$hid = $b["hid"];
	$yid = $b["yid"];
	$user = $b["user"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];

	//YYY补刷接口
	if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "ids" => $yid, "dotype" => "reset");
		$ace_rl = $a["url"];
		$ace_url = "$ace_rl/api/setOrder";

		$result = get_url($ace_url, $data);
		$result = json_decode($result, true);

        if ($result["code"] == "200") {
			$b = array("code" => 1, "msg" => "补刷成功");
        } else {
			$b = array("code" => -1, "msg" => $result["message"]);
        }
		return $b;
	}

	//8090edu补刷接口
	if ($type == "8090edu") {
		// 检查Token是否存在
		if (empty($token)) {
			return array("code" => -1, "msg" => "8090教育Token未配置");
		}

		// 构建补刷请求数据
		$refresh_data = array(
			"orderId" => intval($yid),
			"status" => "队列中",
			"reason" => ""
		);

		// 8090教育补刷API地址
		$refresh_url = $a["url"] . "/api/order/status/update";

		// 设置请求头
		$headers = array(
			"Authorization: Bearer " . $token,
			"Content-Type: application/json",
			"Accept: application/json"
		);

		// 发送补刷请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $refresh_url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($refresh_data));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		$curl_error = curl_error($ch);
		curl_close($ch);

		// 处理请求错误
		if ($curl_error) {
			return array("code" => -1, "msg" => "网络请求失败: " . $curl_error);
		}

		// 处理HTTP错误
		if ($http_code !== 200) {
			return array("code" => -1, "msg" => "HTTP请求失败，状态码: " . $http_code);
		}

		// 解析响应
		$result = json_decode($response, true);

		if (!$result) {
			return array("code" => -1, "msg" => "响应解析失败");
		}

		// 检查API响应
		if (isset($result["code"]) && $result["code"] == 200 && isset($result["state"]) && $result["state"] === true) {
			return array("code" => 1, "msg" => "8090教育补刷成功，订单已重新进入队列");
		} else {
			$error_msg = isset($result["message"]) ? $result["message"] : "未知错误";
			return array("code" => -1, "msg" => "8090教育补刷失败: " . $error_msg);
		}
	}

    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=budan";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }
    
    //暗网补刷
    if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=budan";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       return $result;
    }

    //易教育补刷接口
    if ($type == "jxjy") {
        // 易教育平台通过 /api/order/edit 接口实现补刷功能
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$login_result) {
                return array("code" => -1, "msg" => "登录失败：网络错误 - " . $curl_error);
            }

            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                return array("code" => -1, "msg" => $error_msg);
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$hid}'");
        }

        // 构建补刷请求数据 - 使用 /api/order/edit 接口
        $edit_data = array(
            "number" => $yid,        // 订单号
            "username" => $user,     // 账号
            "password" => $pass      // 密码
        );

        // 发送补刷请求到 /api/order/edit 接口
        $edit_url = "{$a["url"]}/api/order/edit";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $edit_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($edit_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $edit_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($curl_error) {
            return array("code" => -1, "msg" => "补刷失败：网络错误 - " . $curl_error);
        }

        if ($http_code != 200) {
            return array("code" => -1, "msg" => "补刷失败：HTTP状态码 {$http_code}");
        }

        $edit_result_array = json_decode($edit_result, true);

        if (!$edit_result_array) {
            return array("code" => -1, "msg" => "补刷失败：响应解析失败");
        }

        // 检查易教育API返回结果
        if (isset($edit_result_array["code"]) && $edit_result_array["code"] == 200 &&
            isset($edit_result_array["status"]) && $edit_result_array["status"] == "success") {

            // 🔥 补刷成功后的关键处理
            // 1. 更新订单状态为"补刷中"，避免立即进入同步队列
            // 2. 设置一个标记，延迟进度同步
            global $DB;
            $current_time = date('Y-m-d H:i:s');
            $update_sql = "UPDATE qingka_wangke_order SET
                          status = '补刷中',
                          process = '补刷重置中',
                          remarks = CONCAT(IFNULL(remarks, ''), ' [补刷时间: {$current_time}]'),
                          uptime = NOW()
                          WHERE oid = '{$oid}'";
            $DB->query($update_sql);

            // 补刷成功，返回成功信息
            $success_msg = "补刷成功";
            if (isset($edit_result_array["data"]["message"])) {
                $success_msg .= "：" . $edit_result_array["data"]["message"];
            }
            $success_msg .= "，订单已重置，请等待2-3分钟后查看进度";

            return array("code" => 1, "msg" => $success_msg);
        } else {
            // 补刷失败，返回错误信息
            $error_msg = "补刷失败";
            if (isset($edit_result_array["message"])) {
                $error_msg .= "：" . $edit_result_array["message"];
            } elseif (isset($edit_result_array["data"]["message"])) {
                $error_msg .= "：" . $edit_result_array["data"]["message"];
            }

            return array("code" => -1, "msg" => $error_msg);
        }
    }

	else {
				$b = array("code" => -1, "msg" => "接口异常，请联系管理员");
		}
}




?>