<?php  

function wkname()
{
	$data = array(
	    "29" => "29",
	    "bdkj" => "暗网",
	    "yyy" => "yyy",
		"8090edu" => "8090edu",
		"jxjy" => "jxjy"
	    );
	return $data;
}

function addWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];
	$b = $DB->get_row("select * from qingka_wangke_class where cid='{$cid}' ");
	$hid = $b["docking"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];

	//YYY下单接口
	if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
		$dx_rl = $a["url"];
		$dx_url = "$dx_rl/api/order";

		$result = get_url($dx_url, $data);
		$result = json_decode($result, true);

        if ($result["code"] == "200") {
			// 验证返回的yid是否有效
			$returned_yid = isset($result["data"]["yid"]) ? $result["data"]["yid"] : "";

			// 如果返回的yid为空、为1或无效，使用时间戳作为临时ID
			if (empty($returned_yid) || $returned_yid == "1" || !is_numeric($returned_yid)) {
				$returned_yid = time() . rand(100, 999);
			}

			$b = array("code" => 1, "msg" => "下单成功" ,"yid" => $returned_yid);
        } else {
			$b = array("code" => -1, "msg" => $result["msg"]);
        }
		return $b;
	}

	//8090edu下单接口
	if ($type == "8090edu") {
		// 智能Token管理 - 优先使用缓存的token，失效时自动刷新
		$token = $a["token"];
		$need_refresh_token = false;

		// 检查是否有缓存的token
		if (empty($token)) {
			$need_refresh_token = true;
		} else {
			// 验证token是否有效
			$test_url = "{$a["url"]}/api/user/balance";
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $test_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 10);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
			$test_result = curl_exec($ch);
			$curl_error = curl_error($ch);
			curl_close($ch);

			if ($curl_error || !$test_result) {
				$need_refresh_token = true;
			} else {
				$test_result_array = json_decode($test_result, true);
				if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
					$need_refresh_token = true;
				}
			}
		}

		// 如果需要刷新token，重新登录
		if ($need_refresh_token) {
			$login_data = array(
				"username" => $a["user"],
				"password" => $a["pass"]
			);

			$login_url = "{$a["url"]}/api/auth/login";

			// 使用curl发送JSON请求
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $login_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
			$login_result = curl_exec($ch);
			curl_close($ch);

			$login_result = json_decode($login_result, true);

			if ($login_result["code"] != 200) {
				return array("code" => -1, "msg" => $login_result["message"]);
			}

			$token = $login_result["data"]["token"];

			// 更新数据库中的token缓存
			$DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
		}

		// 提交订单
		// 检查课程名是否为账号密码格式（用户名----密码）
		$selectedCourseKeys = array($kcname);

		// 如果课程名包含"----"，说明是直接提交模式，不需要查课
		if (strpos($kcname, '----') !== false) {
			// 对于直接提交的订单，使用空数组或特殊标识
			$selectedCourseKeys = array();
		}

		$order_data = array(
			"websiteId" => $noun,
			"accountInfo" => $user . " " . $pass,
			"selectedCourseKeys" => $selectedCourseKeys
		);

		$order_url = "{$a["url"]}/api/order/submit";

		// 使用curl发送POST请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $order_url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			"Authorization: {$token}",
			"Content-Type: application/json"
		));
		$order_result = curl_exec($ch);
		curl_close($ch);

		$order_result = json_decode($order_result, true);

		if ($order_result["code"] == 200) {
			// 8090教育新版API：下单成功后直接返回订单编号
			// 检查返回数据中是否包含订单编号
			if (isset($order_result["data"]) && !empty($order_result["data"])) {
				$yid = $order_result["data"]; // 直接获取订单编号

				return array(
					"code" => 1,
					"msg" => "下单成功",
					"yid" => $yid
				);
			} else {
				// 如果新API没有返回订单编号，回退到旧的查询方式
				// 记录下单时间戳，用于后续精准匹配
				$order_timestamp = time();

				// 等待一小段时间确保订单已创建
				sleep(2);

				$order_list_url = "{$a["url"]}/api/order/list?username=" . urlencode($user) . "&page=1&pageSize=10&sortField=createTime&sortOrder=descend";
				$ch = curl_init();
				curl_setopt($ch, CURLOPT_URL, $order_list_url);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_TIMEOUT, 30);
				curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
				$list_result = curl_exec($ch);
				$curl_error = curl_error($ch);
				curl_close($ch);

				if ($curl_error) {
					return array("code" => -1, "msg" => "获取订单ID失败: " . $curl_error);
				}

				$list_result = json_decode($list_result, true);

				// 获取最新订单的ID - 使用时间戳精准匹配
				$yid = null;
				$matched_order = null;

				if ($list_result && $list_result["code"] == 200 && isset($list_result["data"]["list"])) {
					foreach ($list_result["data"]["list"] as $order) {
						if (isset($order["orderId"]) && isset($order["username"]) && $order["username"] == $user) {
							// 检查订单创建时间是否在下单时间附近（前后5分钟内）
							$create_time = isset($order["createTime"]) ? strtotime($order["createTime"]) : 0;
							$time_diff = abs($create_time - $order_timestamp);

							// 如果时间差在5分钟内，认为是匹配的订单
							if ($time_diff <= 300) {
								// 进一步通过网站ID验证
								$site_id = isset($order["siteId"]) ? $order["siteId"] : "";
								if ($site_id == $noun) {
									$yid = $order["orderId"];
									$matched_order = $order;
									break;
								}

								// 如果网站ID不匹配，但时间最近，也可以作为候选
								if (!$yid) {
									$yid = $order["orderId"];
									$matched_order = $order;
								}
							}
						}
					}

					// 如果通过时间匹配没找到，使用最新的订单（同用户名）
					if (!$yid) {
						foreach ($list_result["data"]["list"] as $order) {
							if (isset($order["orderId"]) && isset($order["username"]) && $order["username"] == $user) {
								$yid = $order["orderId"];
								$matched_order = $order;
								break;
							}
						}
					}
				}

				if ($yid && $matched_order) {
					// 将匹配信息存储到数据库的remarks字段，用于后续进度查询
					$match_info = json_encode([
						'order_timestamp' => $order_timestamp,
						'site_id' => $noun,
						'create_time' => isset($matched_order["createTime"]) ? $matched_order["createTime"] : "",
						'uuid' => isset($matched_order["uuid"]) ? $matched_order["uuid"] : ""
					], JSON_UNESCAPED_UNICODE);

					return array(
						"code" => 1,
						"msg" => "下单成功",
						"yid" => $yid,
						"match_info" => $match_info
					);
				} else {
					return array("code" => -1, "msg" => "下单成功但无法获取订单ID，请手动查询订单状态");
				}
			}
		} else {
			$error_msg = isset($order_result["message"]) ? $order_result["message"] : "下单失败";
			return array("code" => -1, "msg" => $error_msg);
		}
	}

	/*****
	 自己可以根据规则增加下单接口

	//XXXX下单接口
	else if ($type == "XXXX") {
	$data = array("optoken" => $token,"type" => $noun);  请求体参数自己加
	$XXXX_ul = $a["url"];      变量XXXX自己命名    获取顶级域名
	$XXXX_dingdan = "http://$XXXX_ul/api/CourseQuery/api/";    请求接口   XXXX自己命名
	$result = get_url($XXXX_dingdan, $data, $cookie);
	$result = json_decode($result, true);

	if ($result["code"] == "0") {
		$b = array("code" => 1, "msg" => $result["msg"]);
	} else {
		$b = array("code" => -1, "msg" => $result["msg"]);
	}
	return $b;
    }


	$token  传的token
	$school  传的学校
	$user    传的账号
	$pass    传的密码
	$noun    传的平台里面的接口编号
	$kcid    传的课程id
	****/
    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=add";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        if ($result["code"] == "0") {
        $b = array("code" => 1, "msg" => "下单成功");
        } else {
        $b = array("code" => -1, "msg" => $result["msg"]);
        }
    return $b;
    }
          if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=add";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       if ($result["code"] == "0") {
          	return ['code'=> 1,'msg'=>'对接成功，id:'.$result['id'],'yid'=>$result['id']];
       } else {
          $b = array("code" => - 1, "msg" => $result["msg"]);
       }
       return $b;
    }

    //易教育下单接口
    if ($type == "jxjy") {
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$login_result) {
                return array("code" => -1, "msg" => "登录失败：网络错误");
            }

            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                return array("code" => -1, "msg" => $error_msg);
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$hid}'");
        }

        // 获取项目信息 - 从主商品表查询，使用cid字段匹配（cid是确定的商品ID）
        $class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");
        if (!$class_info) {
            return array("code" => -1, "msg" => "项目信息不存在，商品ID: {$cid}");
        }

        // 验证是否为易教育项目
        if ($class_info['docking'] != $hid) {
            return array("code" => -1, "msg" => "项目不属于易教育货源，商品ID: {$cid}，期望货源: {$hid}，实际货源: {$class_info['docking']}");
        }

        $websiteNumber = $class_info['noun']; // 使用noun字段作为项目编号

        // 判断是否需要查课（可以通过项目名称等判断，这里暂时默认为需要查课）
        $isSearchCourse = '1'; // 默认需要查课

        // 检查课程名是否为账号密码格式（用户名----密码），如果是则表示无需查课
        if (strpos($kcname, '----') !== false && strpos($kcname, $user) !== false && strpos($kcname, $pass) !== false) {
            $isSearchCourse = '0'; // 无需查课
        }

        // 构建下单数据
        if ($isSearchCourse == '0') {
            // 无需查课的项目，直接下单
            $order_data = array(
                "websiteNumber" => $websiteNumber,
                "data" => array(array(
                    "username" => $user,
                    "password" => $pass
                ))
            );
        } else {
            // 需要查课的项目，包含课程信息
            if (strpos($kcname, '----') !== false) {
                // 处理嵌套课程结构
                $course_parts = explode('----', $kcname);
                $main_course = $course_parts[0];
                $sub_course = $course_parts[1];

                $order_data = array(
                    "websiteNumber" => $websiteNumber,
                    "data" => array(array(
                        "username" => $user,
                        "password" => $pass,
                        "name" => $user . "----" . $pass,
                        "children" => array(array(
                            "name" => $main_course,
                            "children" => array(array(
                                "name" => $sub_course,
                                "disabled" => false,
                                "id" => $kcid,
                                "selected" => true
                            )),
                            "disabled" => true,
                            "selected" => true
                        )),
                        "selected" => true
                    ))
                );
            } else {
                // 普通课程结构
                $order_data = array(
                    "websiteNumber" => $websiteNumber,
                    "data" => array(array(
                        "username" => $user,
                        "password" => $pass,
                        "name" => $user . "----" . $pass,
                        "children" => array(array(
                            "name" => $kcname,
                            "disabled" => false,
                            "id" => $kcid,
                            "selected" => true
                        )),
                        "selected" => true
                    ))
                );
            }
        }

        // 发送下单请求
        $order_url = "{$a["url"]}/api/order/buy";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $order_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $order_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($curl_error || !$order_result) {
            return array("code" => -1, "msg" => "下单失败：网络错误 - " . $curl_error . " (HTTP: {$http_code})");
        }

        if ($http_code != 200) {
            return array("code" => -1, "msg" => "下单失败：HTTP状态码 {$http_code}");
        }

        $order_result_array = json_decode($order_result, true);

        if (!$order_result_array || !isset($order_result_array["code"]) || $order_result_array["code"] != 200) {
            $error_msg = isset($order_result_array["message"]) ? $order_result_array["message"] : "下单失败";
            // 添加调试信息
            $debug_info = " (响应: " . substr($order_result, 0, 200) . ")";
            return array("code" => -1, "msg" => $error_msg . $debug_info);
        }

        // 获取订单ID - 改进订单ID获取逻辑
        $yid = "";

        // 检查不同的可能数据结构
        if (isset($order_result_array["data"]["orderList"]) && is_array($order_result_array["data"]["orderList"])) {
            // 标准格式：data.orderList
            foreach ($order_result_array["data"]["orderList"] as $order) {
                if (isset($order["orderId"]) && $order["username"] == $user) {
                    $yid = $order["orderId"];
                    break;
                }
            }
        } elseif (isset($order_result_array["data"]["orderId"])) {
            // 直接格式：data.orderId
            $yid = $order_result_array["data"]["orderId"];
        } elseif (isset($order_result_array["data"]) && is_array($order_result_array["data"])) {
            // 数组格式：data是订单数组
            foreach ($order_result_array["data"] as $order) {
                if (isset($order["orderId"]) && $order["username"] == $user) {
                    $yid = $order["orderId"];
                    break;
                }
            }
        }

        if (empty($yid)) {
            // 提供更详细的调试信息
            $debug_data = json_encode($order_result_array["data"], JSON_UNESCAPED_UNICODE);
            return array("code" => -1, "msg" => "下单成功但未获取到订单ID，数据结构: " . substr($debug_data, 0, 300));
        }

        return array("code" => 1, "msg" => "下单成功", "yid" => $yid);
    }

	else{
	    print_r("没有了,文件xdjk.php,可能故障：参数缺少，比如平台名错误！！！");die;
	}
	
}


?>