<?php  
function ztWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,yid,user from qingka_wangke_order where oid='{$oid}' ");
	$hid = $b["hid"];
	$yid = $b["yid"];
	$user = $b["user"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];

	// yyy教育暂停接口 - 该平台不支持暂停功能
	if ($type == "yyy") {
		// yyy教育平台不支持暂停订单功能
		// 返回不支持的提示信息
		return array("code" => -1, "msg" => "yyy教育平台不支持暂停订单功能");
	}

	if($type=="ikun" ){
$KUN_surl= $a["url"];
$KUN_url=$KUN_surl."/uporder/?token=".$yid."&state=".urlencode("已停止");
$result=get_url($KUN_url);
$result=json_decode($result,true);
if($result["code"]==1){
$b=array("code"=>1,"msg"=>$result["msg"]);}
else{
$b=array("code"=>-1,"msg"=>$result["msg"]);}
return$b;
}
	if ($type == "上游接口的type") {
$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
$uu_rl = $a["url"];
$uu_url = "$uu_rl/api.php?act=tingzhi";
$result = get_url($uu_url, $data);
$result = json_decode($result, true);
return $result;
}

    //易教育暂停接口
    if ($type == "jxjy") {
        // 易教育平台通过修改订单状态实现暂停功能
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$login_result) {
                return array("code" => -1, "msg" => "登录失败：网络错误");
            }

            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                return array("code" => -1, "msg" => $error_msg);
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$hid}'");
        }

        // 构建暂停请求数据
        $pause_data = array(
            "orderNumber" => $yid,
            "status" => 8  // 8 = 暂停中
        );

        // 发送暂停请求
        $pause_url = "{$a["url"]}/api/order/status/update";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $pause_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($pause_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $pause_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$pause_result) {
            return array("code" => -1, "msg" => "暂停失败：网络错误");
        }

        $pause_result_array = json_decode($pause_result, true);

        if (!$pause_result_array || !isset($pause_result_array["code"]) || $pause_result_array["code"] != 200) {
            $error_msg = isset($pause_result_array["message"]) ? $pause_result_array["message"] : "暂停失败";
            return array("code" => -1, "msg" => $error_msg);
        }

        return array("code" => 1, "msg" => "易教育订单暂停成功");
    }

	else {
				$b = array("code" => -1, "msg" => "接口异常，请联系管理员");
		}
}




?>