<?php

function processCx($oid)
{
	global $DB;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$order_info = $DB->get_row("select hid,user,pass from qingka_wangke_order where oid='{$oid}' ");
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$order_info["hid"]}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$user = $order_info["user"];
	$pass = $order_info["pass"];
	$kcname = $d["kcname"];
	$school = $d["school"];
	$pt = $d["noun"];
	$kcid = $d["kcid"];

	// 初始化返回数组
	$b = array();

	//YYY进度接口
    if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $pt, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "yid" => $d['yid']);
		$dx_rl = $a["url"];
        $dx_url = "$dx_rl/api/getorder";

		$result = get_url($dx_url,$data);
		$result = json_decode($result, true);

		if ($result["code"] == "200") {
			foreach ($result["data"]["list"] as $res) {
				// 🔥 关键修复：保护yid字段，避免被API返回的异常值覆盖
				$api_returned_yid = isset($res["id"]) ? $res["id"] : "";

				// 使用数据库中的原始yid，只有在原yid为空或为1时才考虑使用API返回值
				$yid = $d['yid']; // 优先使用数据库中的原始订单号

				// 只有当数据库中的yid为空或为"1"，且API返回了有效的yid时，才更新
				if ((empty($d['yid']) || $d['yid'] == '1') && !empty($api_returned_yid) && $api_returned_yid != '1') {
					$yid = $api_returned_yid;
				}

				$remarks = $res["status"];
				$process = 0;
				if (isset($res["train"])){
					$kcname = $res["train"];
				}
				$code=$res["code"];
				if ($code==107){
					$status = "队列中";
				}elseif ($code==103){
					$status = "异常";
				}elseif ($code==102){
					$status = "已完成";
					$process = 100;
				}elseif ($code==101){
					$status = "已退款";
				}else{
					$status = "进行中";
					preg_match('/^\d+(\.\d+)?/', $remarks, $matches);
					if (isset($matches[0])) {
						$process = $matches[0];
					} else {
						$process = 50;
					}
				}
				$process="$process%";

				$b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "status_text" => $status, "process" => $process, "remarks" => $remarks);
			}
		} else {
			$b[] = array("code" => -1, "msg" => $result["message"]);
		}
		return $b;
    }

	//8090edu进度接口
	if ($type == "8090edu") {
		// 智能Token管理 - 优先使用缓存的token，失效时自动刷新
		$token = $a["token"];
		$need_refresh_token = false;

		// 检查是否有缓存的token
		if (empty($token)) {
			$need_refresh_token = true;
		} else {
			// 验证token是否有效
			$test_url = "{$a["url"]}/api/user/balance";
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $test_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 10);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
			$test_result = curl_exec($ch);
			$curl_error = curl_error($ch);
			curl_close($ch);

			if ($curl_error || !$test_result) {
				$need_refresh_token = true;
			} else {
				$test_result_array = json_decode($test_result, true);
				if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
					$need_refresh_token = true;
				}
			}
		}

		// 如果需要刷新token，重新登录
		if ($need_refresh_token) {
			$login_data = array(
				"username" => $a["user"],
				"password" => $a["pass"]
			);

			$login_url = "{$a["url"]}/api/auth/login";

			// 使用curl发送JSON请求
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $login_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
			$login_result = curl_exec($ch);
			curl_close($ch);

			$login_result = json_decode($login_result, true);

			if (!$login_result || $login_result["code"] != 200) {
				$error_msg = isset($login_result["message"]) ? $login_result["message"] : "登录失败";
				return [array("code" => -1, "msg" => $error_msg)];
			}

			$token = $login_result["data"]["token"];

			// 更新数据库中的token缓存
			$DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
		}

		$b = [];
		$local_yid = $d['yid']; // 本地订单的yid
		$local_remarks = isset($d['remarks']) ? $d['remarks'] : ''; // 本地订单的备注信息
		$found_order = false;

		// 解析本地订单的匹配信息
		$match_info = null;
		if (!empty($local_remarks)) {
			$match_info = json_decode($local_remarks, true);
		}

		// 查询该用户的订单列表
		$order_url = "{$a["url"]}/api/order/list?username=" . urlencode($user) . "&page=1&pageSize=20&sortField=createTime&sortOrder=descend";
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $order_url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
		$order_result = curl_exec($ch);
		$curl_error = curl_error($ch);
		curl_close($ch);

		if ($curl_error) {
			return [array("code" => -1, "msg" => "网络请求失败: " . $curl_error)];
		}

		$order_result = json_decode($order_result, true);

		if ($order_result && $order_result["code"] == 200 && isset($order_result["data"]["list"])) {
			$best_match = null;
			$best_score = 0;

			foreach ($order_result["data"]["list"] as $order) {
				// 获取订单信息
				$orderId = isset($order["orderId"]) ? strval($order["orderId"]) : "";
				$status = isset($order["status"]) ? $order["status"] : "未知";
				$courseName = isset($order["courseName"]) ? $order["courseName"] : "";
				$username = isset($order["username"]) ? $order["username"] : "";
				$password = isset($order["password"]) ? $order["password"] : "";
				$courseInfo = isset($order["courseInfo"]) ? $order["courseInfo"] : "";
				$createTime = isset($order["createTime"]) ? $order["createTime"] : "";
				$updateTime = isset($order["updateTime"]) ? $order["updateTime"] : "";
				$siteId = isset($order["siteId"]) ? strval($order["siteId"]) : "";
				$uuid = isset($order["uuid"]) ? $order["uuid"] : "";

				// 计算匹配分数
				$score = 0;

				// 1. 精确订单ID匹配（最高优先级）
				if ($local_yid && strval($local_yid) == $orderId) {
					$score += 100;
				}

				// 2. UUID匹配（如果有的话）
				if ($match_info && isset($match_info['uuid']) && !empty($match_info['uuid']) && $match_info['uuid'] == $uuid) {
					$score += 80;
				}

				// 3. 用户名匹配（必须匹配）
				if ($username == $user) {
					$score += 50;
				} else {
					continue; // 用户名不匹配，跳过
				}

				// 4. 网站ID匹配
				if ($match_info && isset($match_info['site_id']) && $match_info['site_id'] == $siteId) {
					$score += 30;
				}

				// 5. 时间匹配（下单时间附近）
				if ($match_info && isset($match_info['order_timestamp']) && !empty($createTime)) {
					$order_timestamp = $match_info['order_timestamp'];
					$create_timestamp = strtotime($createTime);
					$time_diff = abs($create_timestamp - $order_timestamp);

					if ($time_diff <= 300) { // 5分钟内
						$score += 20;
					} else if ($time_diff <= 1800) { // 30分钟内
						$score += 10;
					}
				}

				// 6. 课程名匹配
				if (!empty($kcname) && strpos($courseName, $kcname) !== false) {
					$score += 15;
				}

				// 7. 创建时间匹配（如果有记录的创建时间）
				if ($match_info && isset($match_info['create_time']) && $match_info['create_time'] == $createTime) {
					$score += 25;
				}

				// 选择得分最高的订单
				if ($score > $best_score) {
					$best_score = $score;
					$best_match = $order;
				}
			}

			// 如果找到最佳匹配的订单
			if ($best_match && $best_score >= 50) { // 至少要有用户名匹配
				$found_order = true;
				$order = $best_match;

				$orderId = isset($order["orderId"]) ? strval($order["orderId"]) : "";
				$status = isset($order["status"]) ? $order["status"] : "未知";
				$courseName = isset($order["courseName"]) ? $order["courseName"] : "";
				$username = isset($order["username"]) ? $order["username"] : "";
				$password = isset($order["password"]) ? $order["password"] : "";
				$courseInfo = isset($order["courseInfo"]) ? $order["courseInfo"] : "";
				$createTime = isset($order["createTime"]) ? $order["createTime"] : "";
				$updateTime = isset($order["updateTime"]) ? $order["updateTime"] : "";

				// 重要：使用数据库中的用户名和课程名，确保WHERE条件匹配
				// 这样系统的更新SQL才能正确匹配到订单
				$db_user = $user;  // 使用数据库中的用户名
				$db_kcname = $kcname; // 使用数据库中的课程名

				$process = 0;

				// 根据状态设置进度
				switch ($status) {
					case "已完成":
						$process = 100;
						break;
					case "已退款":
						$process = 0;
						break;
					case "进行中":
						// 尝试从courseInfo中提取进度信息
						if (preg_match('/(\d+(?:\.\d+)?)%/', $courseInfo, $matches)) {
							$process = floatval($matches[1]);
						} else {
							$process = 50; // 默认进度
						}
						break;
					case "待处理":
					case "队列中":
						$process = 10;
						break;
					case "异常":
						$process = 0;
						break;
					default:
						$process = 20;
						break;
				}

				// 只提取课程信息作为详细信息
				$detailed_info = "";
				if (!empty($courseName)) {
					$detailed_info = "课程: " . $courseName;
				} else {
					// 如果没有courseName，使用数据库中的课程名作为备用
					$detailed_info = $db_kcname;
				}

				$b[] = array(
					"code" => 1,
					"msg" => "查询成功",
					"yid" => $orderId,
					"kcname" => $db_kcname, // 使用数据库中的课程名，确保WHERE条件匹配
					"name" => $detailed_info, // 系统期望的字段，使用详细的订单信息
					"user" => $db_user, // 使用数据库中的用户名，确保WHERE条件匹配
					"pass" => $password,
					"status_text" => $status,
					"process" => $process . "%",
					"remarks" => $detailed_info, // 备注字段也使用详细信息
					"createTime" => $createTime,
					"updateTime" => $updateTime,
					"match_score" => $best_score, // 调试信息
					// 系统期望的时间字段，8090教育没有这些信息，设置为空
					"kcks" => "", // 课程开始时间
					"kcjs" => "", // 课程结束时间
					"ksks" => "", // 考试开始时间
					"ksjs" => ""  // 考试结束时间
				);
			}
		}

		// 如果没有找到匹配的订单
		if (!$found_order) {
			$b[] = array(
				"code" => -1,
				"msg" => "未找到匹配的订单，订单ID: {$local_yid}，用户: {$user}",
				"yid" => $local_yid,
				"kcname" => $kcname, // 使用数据库中的课程名
				"name" => $kcname, // 系统期望的字段
				"user" => $user, // 使用数据库中的用户名
				"pass" => $pass,
				"status_text" => "未知",
				"process" => "0%",
				"remarks" => "请检查订单信息是否正确，或稍后重试",
				// 系统期望的时间字段
				"kcks" => "",
				"kcjs" => "",
				"ksks" => "",
				"ksjs" => ""
			);
		}

		return $b;
    }



    if ($type == "29") {
        $data = array("username" => $user);
        $dx_rl = $a["url"];
        $dx_url = "$dx_rl/api.php?act=chadan";
        $result = get_url($dx_url, $data);
        $result = json_decode($result, true);
        if ($result["code"] == "1") {
        foreach ($result["data"] as $res) {
        $yid = $res["id"];
        $kcname = $res["kcname"];
        $status = $res["status"];
        $process = $res["process"];
        $remarks = $res["remarks"];
        $kcks = $res["courseStartTime"];
        $kcjs = $res["courseEndTime"];
        $ksks = $res["examStartTime"];
        $ksjs = $res["examEndTime"];
        $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
        }
        } else {
        $b[] = array("code" => -1, "msg" => $result["msg"]);
        }
        return $b;
    }
     if ($type == "bdkj") {
       $data = array("oid" => $d['yid'],"uid" => $a["user"], "key" => $a["pass"]);
    $dx_rl = $a["url"];
    $dx_url  = "$dx_rl/api.php?act=chadanoid";
    $result = get_url($dx_url, $data);
    $result = json_decode($result, true);
    if ($result["code"] == "1") {
    foreach ($result["data"] as $res) {
    $yid = $res["id"];
    $kcname = $res["kcname"];
    $status = $res["status"];
    $process = $res["process"];
    $remarks = $res["remarks"];
    $kcks = $res["courseStartTime"];
    $kcjs = $res["courseEndTime"];
    $ksks = $res["examStartTime"];
    $ksjs = $res["examEndTime"];
    $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
    }
    } else {
    $b[] = array("code" => -1, "msg" => "查询失败,请联系管理员");
    }
    }

    //易教育进度接口
    if ($type == "jxjy") {
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$login_result) {
                $b[] = array("code" => -1, "msg" => "登录失败：网络错误 - " . $curl_error);
                return $b;
            }

            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                $b[] = array("code" => -1, "msg" => $error_msg);
                return $b;
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$a["hid"]}'");
        }

        // 🔥 修复：根据实际API要求，只使用订单号查询
        // API: http://125.208.21.156:9900/api/order/list
        $query_data = array(
            "pageSize" => 10,
            "pageNum" => 1,
            "status" => "all",
            "name" => "",
            "username" => "",
            "websiteNumber" => "",
            "orderNumber" => $d['yid']  // 只使用订单号查询
        );

        // 发送查询请求
        $query_url = "{$a["url"]}/api/order/list";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $query_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($query_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $query_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$query_result) {
            $b[] = array("code" => -1, "msg" => "查询失败：网络错误 - " . $curl_error);
            return $b;
        }

        $query_result_array = json_decode($query_result, true);

        if (!$query_result_array || !isset($query_result_array["code"]) || $query_result_array["code"] != 200) {
            $error_msg = isset($query_result_array["message"]) ? $query_result_array["message"] : "查询失败";
            $b[] = array("code" => -1, "msg" => $error_msg);
            return $b;
        }

        // 🔥 修复：根据实际API返回数据结构处理
        // 实际返回的status字段：0=待处理, 1=已完成
        $status_mapping = array(
            0 => '队列中',    // 待处理
            1 => '已完成',    // 已完成
            2 => '进行中',    // 进行中
            3 => '异常',      // 异常
            4 => '正在售后',  // 售后处理中
            5 => '售后完成',  // 售后已完成
            6 => '人工处理',  // 需要人工干预
            7 => '已退款',    // 已退款
            8 => '暂停中',    // 暂停状态
            9 => '已暂停'     // 已暂停
        );

        $found_order = false;
        if (isset($query_result_array["data"]["list"]) && is_array($query_result_array["data"]["list"])) {
            foreach ($query_result_array["data"]["list"] as $order) {
                // 🔥 修复：只使用订单号匹配，确保精确匹配
                $order_number = isset($order["number"]) ? $order["number"] : "";

                // 严格的订单号匹配
                if (!empty($d['yid']) && !empty($order_number) && $order_number == $d['yid']) {
                    $found_order = true;

                    // 🔥 关键修复：保护yid字段，使用数据库中的原始订单号
                    $yid = $d['yid']; // 使用数据库中的原始订单号，不允许覆盖

                    // 获取状态信息
                    $status_code = isset($order["status"]) ? intval($order["status"]) : 0;
                    $status = isset($status_mapping[$status_code]) ? $status_mapping[$status_code] : "队列中";

                    // 🔥 修复：根据实际API返回数据处理进度
                    // 实际API返回的progress字段：如"学习完成"
                    $raw_progress = isset($order["progress"]) ? $order["progress"] : "";
                    $progress = "0%";

                    // 根据实际返回的进度文本进行判断
                    if (!empty($raw_progress)) {
                        if (strpos($raw_progress, '学习完成') !== false ||
                            strpos($raw_progress, '完成') !== false ||
                            $status_code == 1) {
                            $progress = "100%";
                            $status = "已完成";  // 确保状态也更新为已完成
                        } elseif (strpos($raw_progress, '学习中') !== false ||
                                 strpos($raw_progress, '进行') !== false ||
                                 strpos($raw_progress, '运行') !== false) {
                            $progress = "50%";
                            $status = "进行中";
                        } elseif (strpos($raw_progress, '队列') !== false ||
                                 strpos($raw_progress, '等待') !== false ||
                                 $status_code == 0) {
                            $progress = "10%";
                            $status = "队列中";
                        } else {
                            // 尝试提取百分比数字
                            if (preg_match('/(\d+(?:\.\d+)?)%/', $raw_progress, $matches)) {
                                $progress = $matches[1] . "%";
                            } elseif (is_numeric($raw_progress)) {
                                $progress_num = floatval($raw_progress);
                                if ($progress_num > 100) $progress_num = 100;
                                if ($progress_num < 0) $progress_num = 0;
                                $progress = $progress_num . "%";
                            } else {
                                // 默认根据状态设置进度
                                $progress = ($status_code == 1) ? "100%" : "10%";
                            }
                        }
                    } else {
                        // 没有进度信息，根据状态设置
                        $progress = ($status_code == 1) ? "100%" : "10%";
                    }

                    // 获取其他订单信息
                    $order_name = isset($order["name"]) ? $order["name"] : "";
                    $order_username = isset($order["username"]) ? $order["username"] : $user;
                    $order_password = isset($order["password"]) ? $order["password"] : $pass;
                    $website_name = isset($order["websiteName"]) ? $order["websiteName"] : "";
                    $update_date = isset($order["updateDate"]) ? $order["updateDate"] : "";
                    $create_date = isset($order["createDate"]) ? $order["createDate"] : "";

                    // 使用网站名称作为课程名称
                    $order_kcname = !empty($website_name) ? $website_name : $kcname;

                    // 🔥 关键修复：确保返回的数据结构与守护进程更新逻辑一致
                    $b[] = array(
                        "code" => 1,
                        "msg" => "查询成功",
                        "yid" => $yid,
                        "kcname" => $order_kcname,
                        "name" => $order_name,
                        "user" => $order_username,
                        "pass" => $order_password,
                        "status_text" => $status,
                        "process" => $progress,
                        "remarks" => "进度: {$raw_progress} - 更新: {$update_date}",
                        "kcks" => "",  // 课程开始时间
                        "kcjs" => "",  // 课程结束时间
                        "ksks" => "",  // 考试开始时间
                        "ksjs" => ""   // 考试结束时间
                    );
                    break; // 找到匹配的订单后退出循环
                }
            }
        }

        // 如果没有找到匹配的订单
        if (!$found_order) {
            $b[] = array("code" => -1, "msg" => "未找到订单号为 {$d['yid']} 的订单信息");
        }

        return $b;
    }

    // 如果不是支持的平台类型，返回错误
    $b[] = array("code" => -1, "msg" => "查询失败,请联系管理员");
    return $b;
}
?>