<?php
/**
 * 8090教育Token管理函数
 * 解决token容易过期的问题
 */

/**
 * 获取8090教育有效Token
 * @param array $huoyuan_info 货源信息
 * @return array 返回结果 ['success' => true/false, 'token' => 'token_string', 'msg' => 'error_message']
 */
function get8090eduToken($huoyuan_info) {
    global $DB;
    
    $token = $huoyuan_info["token"];
    $need_refresh_token = false;
    
    // 检查是否有缓存的token
    if (empty($token)) {
        $need_refresh_token = true;
    } else {
        // 验证token是否有效 - 通过调用余额API来测试
        $test_url = "{$huoyuan_info["url"]}/api/user/balance";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
        $test_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error || !$test_result) {
            $need_refresh_token = true;
        } else {
            $test_result_array = json_decode($test_result, true);
            if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                $need_refresh_token = true;
            }
        }
    }
    
    // 如果需要刷新token，重新登录
    if ($need_refresh_token) {
        $login_data = array(
            "username" => $huoyuan_info["user"],
            "password" => $huoyuan_info["pass"]
        );

        $login_url = "{$huoyuan_info["url"]}/api/auth/login";

        // 使用curl发送JSON请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $login_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        $login_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            return [
                'success' => false,
                'token' => '',
                'msg' => "网络连接失败: " . $curl_error
            ];
        }

        $login_result = json_decode($login_result, true);

        if (!$login_result || !isset($login_result["code"]) || $login_result["code"] != 200) {
            $error_msg = isset($login_result["message"]) ? $login_result["message"] : "登录失败";
            return [
                'success' => false,
                'token' => '',
                'msg' => $error_msg
            ];
        }

        $token = $login_result["data"]["token"];
        
        // 更新数据库中的token缓存
        $escaped_token = $DB->escape($token);
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$escaped_token}', endtime = NOW() WHERE hid = '{$huoyuan_info["hid"]}'");
    }
    
    return [
        'success' => true,
        'token' => $token,
        'msg' => 'Token获取成功'
    ];
}

/**
 * 清除8090教育Token缓存
 * @param string $hid 货源ID
 */
function clear8090eduToken($hid) {
    global $DB;
    $DB->query("UPDATE qingka_wangke_huoyuan SET token = '' WHERE hid = '{$hid}'");
}

/**
 * 检查8090教育Token是否有效
 * @param array $huoyuan_info 货源信息
 * @param string $token Token字符串
 * @return bool 是否有效
 */
function validate8090eduToken($huoyuan_info, $token) {
    if (empty($token)) {
        return false;
    }
    
    $test_url = "{$huoyuan_info["url"]}/api/user/balance";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $test_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error || !$test_result) {
        return false;
    }
    
    $test_result_array = json_decode($test_result, true);
    return ($test_result_array && isset($test_result_array["code"]) && $test_result_array["code"] == 200);
}

/**
 * 获取8090教育余额信息
 * @param array $huoyuan_info 货源信息
 * @return array 余额信息
 */
function get8090eduBalance($huoyuan_info) {
    $token_result = get8090eduToken($huoyuan_info);
    
    if (!$token_result['success']) {
        return [
            'success' => false,
            'balance' => 0,
            'msg' => $token_result['msg']
        ];
    }
    
    $token = $token_result['token'];
    $balance_url = "{$huoyuan_info["url"]}/api/user/balance";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $balance_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
    $balance_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return [
            'success' => false,
            'balance' => 0,
            'msg' => "网络连接失败: " . $curl_error
        ];
    }
    
    $balance_result_array = json_decode($balance_result, true);
    
    if (!$balance_result_array || !isset($balance_result_array["code"]) || $balance_result_array["code"] != 200) {
        $error_msg = isset($balance_result_array["message"]) ? $balance_result_array["message"] : "获取余额失败";
        return [
            'success' => false,
            'balance' => 0,
            'msg' => $error_msg
        ];
    }
    
    $balance = isset($balance_result_array["data"]["balance"]) ? $balance_result_array["data"]["balance"] : 0;
    
    return [
        'success' => true,
        'balance' => $balance,
        'msg' => '余额获取成功'
    ];
}

/**
 * 记录8090教育API调用日志
 * @param string $action 操作类型
 * @param string $message 日志消息
 * @param array $data 额外数据
 */
function log8090eduAction($action, $message, $data = []) {
    $log_entry = [
        'time' => date('Y-m-d H:i:s'),
        'action' => $action,
        'message' => $message,
        'data' => $data
    ];
    
    // 可以根据需要将日志写入文件或数据库
    error_log("8090edu_" . $action . ": " . json_encode($log_entry, JSON_UNESCAPED_UNICODE));
}

?>
