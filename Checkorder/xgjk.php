<?php  
function xgmm($oid,$xgmm){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,yid,user from qingka_wangke_order where oid='{$oid}' ");
	$hid = $b["hid"];
	$yid = $b["yid"];
	$user = $b["user"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];

	//YYY修改密码接口
	if ($type == "yyy") {
		$data = array("uid" => $a["user"], "key" => $a["pass"], "ids" => $yid, "dotype" => "edit","odpwd" => $xgmm);
		$ace_rl = $a["url"];
		$ace_url = "$ace_rl/api/setOrder";
		$result = get_url($ace_url, $data);
		$result = json_decode($result, true);

		if ($result["code"] == "200") {
			$b = array("code" => 1, "msg" => "修改成功");
		} else {
			$b = array("code" => -1, "msg" => $result["message"]);
		}
		return $b;
	}

	//8090edu修改密码接口 - 8090教育不支持修改密码功能
	if ($type == "8090edu") {
		return array("code" => -1, "msg" => "8090教育平台不支持修改密码功能");
	}

	if($type=="ikun" ){
$KUN_surl= $a["url"];
$KUN_url=$KUN_surl."/upPwd/?token=".$yid."&pwd=".$xgmm;
$result=get_url($KUN_url);
$result=json_decode($result,true);
if($result["code"]==1){
$b=array("code"=>1,"msg"=>$result["msg"]);}
else{
$b=array("code"=>-1,"msg"=>$result["msg"]);}
return$b;
}


	if ($type == "上游接口的type") {
$data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid, "xgmm" => $xgmm);
$uu_rl = $a["url"];
$uu_url = "$uu_rl/api.php?act=gaimima";
$result = get_url($uu_url, $data);
$result = json_decode($result, true);
return $result;
}

	//易教育修改密码接口
	if ($type == "jxjy") {
		// 易教育平台不支持直接修改密码，需要重新下单
		// 但可以更新本地数据库中的密码记录

		// 更新本地订单密码
		$DB->query("UPDATE qingka_wangke_order SET pass = '{$xgmm}' WHERE oid = '{$oid}'");

		// 检查是否更新成功
		$updated_order = $DB->get_row("SELECT pass FROM qingka_wangke_order WHERE oid = '{$oid}'");
		if ($updated_order && $updated_order['pass'] == $xgmm) {
			return array("code" => 1, "msg" => "密码修改成功（仅更新本地记录，如需在易教育平台生效请重新下单）");
		} else {
			return array("code" => -1, "msg" => "密码修改失败");
		}
	}

	else {
				$b = array("code" => -1, "msg" => "接口异常，请联系管理员");
		}
}




?>