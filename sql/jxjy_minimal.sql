-- 易教育对接最简化SQL脚本
-- 只创建必要的数据库表结构，不插入任何数据

-- 创建易教育课程/项目表
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL,
  `number` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `format` varchar(255) NOT NULL,
  `url` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unitId` varchar(255) NOT NULL,
  `must` varchar(255) NOT NULL,
  `unit` varchar(255) NOT NULL,
  `rate` varchar(255) NOT NULL,
  `isExam` varchar(255) NOT NULL,
  `isSearchCourse` varchar(255) NOT NULL,
  `status` int(11) NOT NULL DEFAULT 1,
  `description` varchar(255) NOT NULL,
  `remark` varchar(255) NOT NULL,
  `date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_number` (`number`),
  KEY `idx_status` (`status`),
  KEY `idx_must` (`must`),
  KEY `idx_isSearchCourse` (`isSearchCourse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='易教育项目课程表';

-- 创建易教育订单表
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyorder` (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL,
  `yid` varchar(255) NOT NULL DEFAULT '',
  `ptname` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `user` varchar(255) NOT NULL,
  `pass` varchar(255) NOT NULL,
  `kcid` text NOT NULL,
  `kcname` varchar(255) NOT NULL DEFAULT '',
  `fees` decimal(10,2) NOT NULL DEFAULT 0.00,
  `noun` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL DEFAULT '待更新',
  `process` varchar(255) NOT NULL DEFAULT '',
  `bsnum` varchar(255) NOT NULL DEFAULT '0',
  `remarks` varchar(255) NOT NULL DEFAULT '',
  `ip` varchar(255) NOT NULL,
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`oid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_cid` (`cid`),
  KEY `idx_yid` (`yid`),
  KEY `idx_user` (`user`),
  KEY `idx_status` (`status`),
  KEY `idx_addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='易教育订单表';

-- 完成提示
SELECT '易教育数据库表创建完成！' AS message;
