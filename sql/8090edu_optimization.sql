-- 8090教育数据库优化脚本
-- 解决重复记录问题和提升查询性能

-- 1. 添加必要的索引（如果不存在）
-- 检查并添加docking字段索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_docking'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_docking (docking)', 
    'SELECT "Index idx_docking already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加noun字段索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_noun'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_noun (noun)', 
    'SELECT "Index idx_noun already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加复合索引（docking + noun）
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_docking_noun'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_docking_noun (docking, noun)', 
    'SELECT "Index idx_docking_noun already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加status字段索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_status'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_status (status)', 
    'SELECT "Index idx_status already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 清理8090教育的重复记录
-- 获取8090教育的货源ID
SET @hid_8090 = (
    SELECT hid 
    FROM qingka_wangke_huoyuan 
    WHERE pt = '8090edu' 
    LIMIT 1
);

-- 如果找到8090教育货源，清理重复记录
SET @cleanup_sql = IF(@hid_8090 IS NOT NULL,
    CONCAT('DELETE c1 FROM qingka_wangke_class c1 
            INNER JOIN qingka_wangke_class c2 
            WHERE c1.docking = ', @hid_8090, ' 
            AND c2.docking = ', @hid_8090, '
            AND c1.noun = c2.noun 
            AND c1.cid < c2.cid'),
    'SELECT "No 8090edu source found" as message'
);

PREPARE stmt FROM @cleanup_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加唯一约束防止未来重复（可选，谨慎使用）
-- 注意：这个约束可能会影响现有功能，请根据实际情况决定是否启用
-- ALTER TABLE qingka_wangke_class ADD UNIQUE KEY uk_docking_noun (docking, noun);

-- 4. 优化订单表索引
-- 检查并添加订单表的hid索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_order' 
    AND INDEX_NAME = 'idx_hid'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_order ADD INDEX idx_hid (hid)', 
    'SELECT "Index idx_hid already exists on order table" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加订单表的yid索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_order' 
    AND INDEX_NAME = 'idx_yid'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_order ADD INDEX idx_yid (yid)', 
    'SELECT "Index idx_yid already exists on order table" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加订单表的user索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_order' 
    AND INDEX_NAME = 'idx_user'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_order ADD INDEX idx_user (user)', 
    'SELECT "Index idx_user already exists on order table" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 显示优化结果
SELECT 
    '8090教育数据库优化完成' as message,
    NOW() as completion_time;

-- 显示当前8090教育项目统计
SELECT
    COUNT(*) as total_projects,
    SUM(CASE WHEN c.status = 1 THEN 1 ELSE 0 END) as active_projects,
    SUM(CASE WHEN c.status = 0 THEN 1 ELSE 0 END) as inactive_projects
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu';

-- 检查是否还有重复记录
SELECT 
    noun,
    COUNT(*) as duplicate_count
FROM qingka_wangke_class c
JOIN qingka_wangke_huoyuan h ON c.docking = h.hid
WHERE h.pt = '8090edu'
GROUP BY noun
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC
LIMIT 5;
