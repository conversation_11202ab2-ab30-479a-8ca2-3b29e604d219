-- 易教育对接数据库表创建脚本（兼容版本）
-- 创建时间: 2025-08-21
-- 说明: 兼容所有MySQL版本的易教育对接数据库脚本

-- 1. 创建易教育课程/项目表
DROP TABLE IF EXISTS `qingka_wangke_jxjyclass`;
CREATE TABLE `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL COMMENT '项目ID，对应易教育的项目ID',
  `number` varchar(255) NOT NULL COMMENT '对接站ID，易教育的项目编号',
  `name` varchar(255) NOT NULL COMMENT '项目名称',
  `format` varchar(255) NOT NULL COMMENT '下单格式要求',
  `url` varchar(255) NOT NULL COMMENT '项目网址',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '项目价格',
  `unitId` varchar(255) NOT NULL COMMENT '价格单位ID',
  `must` varchar(255) NOT NULL COMMENT '是否必须查课：0=无需查课，1=必须查课',
  `unit` varchar(255) NOT NULL COMMENT '价格单位类型：年度、培训等',
  `rate` varchar(255) NOT NULL COMMENT '速率：0=正常，1=加速，2=秒学',
  `isExam` varchar(255) NOT NULL COMMENT '是否支持考试：0=不支持，1=支持',
  `isSearchCourse` varchar(255) NOT NULL COMMENT '是否支持查课：0=不支持，1=支持',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
  `description` varchar(255) NOT NULL COMMENT '项目描述说明',
  `remark` varchar(255) NOT NULL COMMENT '备注信息',
  `date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_number` (`number`),
  KEY `idx_status` (`status`),
  KEY `idx_must` (`must`),
  KEY `idx_isSearchCourse` (`isSearchCourse`),
  KEY `idx_price` (`price`),
  KEY `idx_unit` (`unit`),
  KEY `idx_rate` (`rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='易教育项目课程表';

-- 2. 创建易教育订单表
DROP TABLE IF EXISTS `qingka_wangke_jxjyorder`;
CREATE TABLE `qingka_wangke_jxjyorder` (
  `oid` int(11) NOT NULL AUTO_INCREMENT COMMENT '本地订单ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `cid` int(11) NOT NULL COMMENT '项目ID，关联qingka_wangke_jxjyclass.id',
  `yid` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '易教育订单ID',
  `ptname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '平台名称',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '学员姓名',
  `user` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '学习账号',
  `pass` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '学习密码',
  `kcid` text COLLATE utf8_unicode_ci NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '课程名称',
  `fees` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单费用',
  `noun` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '项目编号，对应易教育的websiteNumber',
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '待更新' COMMENT '订单状态',
  `process` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '学习进度',
  `bsnum` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '备注信息',
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '下单IP地址',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`oid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_cid` (`cid`),
  KEY `idx_yid` (`yid`),
  KEY `idx_user` (`user`),
  KEY `idx_status` (`status`),
  KEY `idx_addtime` (`addtime`),
  KEY `idx_ptname` (`ptname`),
  KEY `idx_fees` (`fees`),
  KEY `idx_updatetime` (`updatetime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='易教育订单表';

-- 3. 插入易教育货源配置（如果不存在）
INSERT INTO `qingka_wangke_huoyuan` (
  `pt`, `name`, `url`, `user`, `pass`, `token`, `ip`, `cookie`, `status`
)
SELECT 'jxjy', '易教育', 'http://**************:9900', '573749877', 'liuyaxin123.', '', '**************', '', 1
WHERE NOT EXISTS (
  SELECT 1 FROM `qingka_wangke_huoyuan` WHERE `pt` = 'jxjy'
);

-- 4. 插入易教育分类（如果不存在）
INSERT INTO `qingka_wangke_fenlei` (`name`, `text`, `sort`, `status`)
SELECT '易教育', '易教育平台聚合多个教育网站，支持查课和下单功能', 100, 1
WHERE NOT EXISTS (
  SELECT 1 FROM `qingka_wangke_fenlei` WHERE `name` = '易教育'
);

-- 5. 项目数据将通过API同步脚本自动获取和更新
-- 无需手动插入示例数据

-- 6. 显示配置完成信息
SELECT 
  '货源配置完成' as message,
  hid as 货源ID,
  name as 货源名称,
  url as API地址,
  user as 账号
FROM qingka_wangke_huoyuan 
WHERE pt = 'jxjy';

SELECT 
  '分类配置完成' as message,
  id as 分类ID,
  name as 分类名称
FROM qingka_wangke_fenlei 
WHERE name = '易教育';

SELECT '易教育对接数据库配置完成！请运行商品同步脚本获取项目数据' as message;
