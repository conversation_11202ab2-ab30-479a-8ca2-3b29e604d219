-- 易教育对接数据库脚本 - 分步执行版本
-- 请按顺序逐步执行以下SQL语句

-- 步骤1：创建易教育课程/项目表
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL COMMENT '项目ID，对应易教育的项目ID',
  `number` varchar(255) NOT NULL COMMENT '对接站ID，易教育的项目编号',
  `name` varchar(255) NOT NULL COMMENT '项目名称',
  `format` varchar(255) NOT NULL COMMENT '下单格式要求',
  `url` varchar(255) NOT NULL COMMENT '项目网址',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '项目价格',
  `unitId` varchar(255) NOT NULL COMMENT '价格单位ID',
  `must` varchar(255) NOT NULL COMMENT '是否必须查课：0=无需查课，1=必须查课',
  `unit` varchar(255) NOT NULL COMMENT '价格单位类型：年度、培训等',
  `rate` varchar(255) NOT NULL COMMENT '速率：0=正常，1=加速，2=秒学',
  `isExam` varchar(255) NOT NULL COMMENT '是否支持考试：0=不支持，1=支持',
  `isSearchCourse` varchar(255) NOT NULL COMMENT '是否支持查课：0=不支持，1=支持',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
  `description` varchar(255) NOT NULL COMMENT '项目描述说明',
  `remark` varchar(255) NOT NULL COMMENT '备注信息',
  `date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='易教育项目课程表';

-- 步骤2：为qingka_wangke_jxjyclass表添加索引
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_number` (`number`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_status` (`status`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_must` (`must`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_isSearchCourse` (`isSearchCourse`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_price` (`price`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_unit` (`unit`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_rate` (`rate`);

-- 步骤3：创建易教育订单表
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyorder` (
  `oid` int(11) NOT NULL AUTO_INCREMENT COMMENT '本地订单ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `cid` int(11) NOT NULL COMMENT '项目ID，关联qingka_wangke_jxjyclass.id',
  `yid` varchar(255) NOT NULL DEFAULT '' COMMENT '易教育订单ID',
  `ptname` varchar(255) NOT NULL COMMENT '平台名称',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '学员姓名',
  `user` varchar(255) NOT NULL COMMENT '学习账号',
  `pass` varchar(255) NOT NULL COMMENT '学习密码',
  `kcid` text NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) NOT NULL DEFAULT '' COMMENT '课程名称',
  `fees` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单费用',
  `noun` varchar(255) NOT NULL COMMENT '项目编号，对应易教育的websiteNumber',
  `status` varchar(255) NOT NULL DEFAULT '待更新' COMMENT '订单状态',
  `process` varchar(255) NOT NULL DEFAULT '' COMMENT '学习进度',
  `bsnum` varchar(255) NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息',
  `ip` varchar(255) NOT NULL COMMENT '下单IP地址',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`oid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='易教育订单表';

-- 步骤4：为qingka_wangke_jxjyorder表添加索引
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_uid` (`uid`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_cid` (`cid`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_yid` (`yid`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_user` (`user`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_status` (`status`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_addtime` (`addtime`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_ptname` (`ptname`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_fees` (`fees`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_updatetime` (`updatetime`);

-- 步骤5：插入易教育货源配置（请根据实际情况修改）
-- 注意：如果已存在相同pt的记录，请先删除或修改以下语句
INSERT INTO `qingka_wangke_huoyuan` (
  `pt`, `name`, `url`, `user`, `pass`, `token`, `ip`, `cookie`, `status`
) VALUES (
  'jxjy',
  '易教育',
  'http://**************:9900',
  '573749877',
  'liuyaxin123.',
  '',
  '**************',
  '',
  1
);

-- 步骤6：插入易教育分类（请根据实际情况修改）
-- 注意：如果已存在相同name的记录，请先删除或修改以下语句
INSERT INTO `qingka_wangke_fenlei` (
  `name`, `text`, `sort`, `status`
) VALUES (
  '易教育',
  '易教育平台聚合多个教育网站，支持查课和下单功能',
  100,
  1
);

-- 步骤7：项目数据将通过API同步脚本自动获取
-- 无需手动插入示例数据，请运行：你的域名/api/jxjy.php?pricee=5

-- 完成提示
SELECT '易教育对接数据库配置完成！请运行商品同步脚本获取项目数据' AS message;
