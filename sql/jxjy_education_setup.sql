-- 易教育对接数据库表创建脚本
-- 创建时间: 2025-08-21
-- 说明: 基于易教育对接文件创建必要的数据库表结构

-- 1. 创建易教育课程/项目表
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL COMMENT '项目ID，对应易教育的项目ID',
  `number` varchar(255) NOT NULL COMMENT '对接站ID，易教育的项目编号',
  `name` varchar(255) NOT NULL COMMENT '项目名称',
  `format` varchar(255) NOT NULL COMMENT '下单格式要求',
  `url` varchar(255) NOT NULL COMMENT '项目网址',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '项目价格',
  `unitId` varchar(255) NOT NULL COMMENT '价格单位ID',
  `must` varchar(255) NOT NULL COMMENT '是否必须查课：0=无需查课，1=必须查课',
  `unit` varchar(255) NOT NULL COMMENT '价格单位类型：年度、培训等',
  `rate` varchar(255) NOT NULL COMMENT '速率：0=正常，1=加速，2=秒学',
  `isExam` varchar(255) NOT NULL COMMENT '是否支持考试：0=不支持，1=支持',
  `isSearchCourse` varchar(255) NOT NULL COMMENT '是否支持查课：0=不支持，1=支持',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
  `description` varchar(255) NOT NULL COMMENT '项目描述说明',
  `remark` varchar(255) NOT NULL COMMENT '备注信息',
  `date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_number` (`number`),
  KEY `idx_status` (`status`),
  KEY `idx_must` (`must`),
  KEY `idx_isSearchCourse` (`isSearchCourse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='易教育项目课程表';

-- 2. 创建易教育订单表
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyorder` (
  `oid` int(11) NOT NULL AUTO_INCREMENT COMMENT '本地订单ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `cid` int(11) NOT NULL COMMENT '项目ID，关联qingka_wangke_jxjyclass.id',
  `yid` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '易教育订单ID',
  `ptname` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '平台名称',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '学员姓名',
  `user` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '学习账号',
  `pass` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '学习密码',
  `kcid` text COLLATE utf8_unicode_ci NOT NULL COMMENT '课程ID',
  `kcname` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '课程名称',
  `fees` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单费用',
  `noun` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '项目编号，对应易教育的websiteNumber',
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '待更新' COMMENT '订单状态',
  `process` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '学习进度',
  `bsnum` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0' COMMENT '补刷次数',
  `remarks` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '备注信息',
  `ip` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '下单IP地址',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`oid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_cid` (`cid`),
  KEY `idx_yid` (`yid`),
  KEY `idx_user` (`user`),
  KEY `idx_status` (`status`),
  KEY `idx_addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='易教育订单表';

-- 3. 插入易教育货源配置（如果不存在）
INSERT IGNORE INTO `qingka_wangke_huoyuan` (
  `pt`, `name`, `url`, `user`, `pass`, `token`, `ip`, `cookie`, `status`
) VALUES (
  'jxjy',
  '易教育',
  'http://*************:9900',
  '573749877',
  'liuyaxin123.',
  '',
  '*************',
  '',
  1
);

-- 4. 插入易教育分类（如果不存在）
INSERT IGNORE INTO `qingka_wangke_fenlei` (
  `name`, `text`, `sort`, `status`
) VALUES (
  '易教育',
  '易教育平台聚合多个教育网站，支持查课和下单功能',
  100,
  1
);

-- 5. 创建索引优化查询性能（兼容所有MySQL版本）
-- 为qingka_wangke_jxjyclass表添加索引
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_price` (`price`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_unit` (`unit`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_rate` (`rate`);

-- 为qingka_wangke_jxjyorder表添加索引
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_ptname` (`ptname`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_fees` (`fees`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_updatetime` (`updatetime`);

-- 6. 添加外键约束（可选，根据系统需要）
-- ALTER TABLE `qingka_wangke_jxjyorder` 
-- ADD CONSTRAINT `fk_jxjyorder_uid` FOREIGN KEY (`uid`) REFERENCES `qingka_wangke_user` (`uid`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_jxjyorder_cid` FOREIGN KEY (`cid`) REFERENCES `qingka_wangke_jxjyclass` (`id`) ON DELETE CASCADE;

-- 7. 插入示例数据（基于易教育API返回的项目）
-- 注意：实际项目数据应该通过API同步脚本自动获取和更新
INSERT IGNORE INTO `qingka_wangke_jxjyclass` (
  `id`, `number`, `name`, `format`, `url`, `price`, `unitId`, `must`, `unit`, 
  `rate`, `isExam`, `isSearchCourse`, `status`, `description`, `remark`
) VALUES
(1, '10010101', '国培网-融学Web', '账号 密码', 'https://web.chinahrt.com', 0.30, '1', '1', '年度', '1', '1', '1', 1, '大概1至2小时完成', '无'),
(191, '10010102', '国培网-融学App【秒学版】', '账号 密码', 'https://gp.chinahrt.com/', 0.20, '2', '1', '培训', '2', '1', '1', 1, '每培训10分钟内完成', ''),
(37, '10330101', '广东省教师继续教育信息管理平台', '账号 密码', 'https://jsglpt.gdedu.gov.cn/', 0.25, '1', '1', '年度', '1', '1', '1', 1, '教师继续教育培训', '');

-- 8. 创建触发器（可选）- 用于自动更新时间戳
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS `tr_jxjyorder_update` 
BEFORE UPDATE ON `qingka_wangke_jxjyorder`
FOR EACH ROW 
BEGIN
    SET NEW.updatetime = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 完成提示
SELECT '易教育对接数据库表创建完成！' AS message;
