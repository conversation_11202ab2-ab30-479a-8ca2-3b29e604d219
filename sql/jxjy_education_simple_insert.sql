-- 易教育货源简单插入脚本
-- 适用于已有表结构的情况
-- 创建时间: 2025-08-21

-- 1. 插入易教育货源配置（如果不存在）
INSERT IGNORE INTO `qingka_wangke_huoyuan` (
  `pt`, `name`, `url`, `user`, `pass`, `token`, `ip`, `cookie`, `money`, `status`, `addtime`, `endtime`
) VALUES (
  'jxjy',
  '易教育',
  'http://*************:9900',
  '573749877',
  'liuyaxin123.',
  '',
  '*************',
  '',
  '0',
  '1',
  NOW(),
  ''
);

-- 2. 插入易教育分类（如果不存在）
INSERT IGNORE INTO `qingka_wangke_fenlei` (`name`, `text`, `sort`, `status`, `time`) VALUES
('易教育', '易教育平台聚合多个教育网站，支持查课和下单功能', '100', '1', NOW());

-- 3. 获取货源ID和分类ID
SET @huoyuan_id = (SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' ORDER BY hid DESC LIMIT 1);
SET @fenlei_id = (SELECT id FROM qingka_wangke_fenlei WHERE name = '易教育' ORDER BY id DESC LIMIT 1);

-- 4. 插入示例商品（基于易教育API返回的项目）
INSERT IGNORE INTO `qingka_wangke_class` (
  `sort`, `name`, `getnoun`, `noun`, `price`, `queryplat`, `docking`, 
  `yunsuan`, `content`, `status`, `fenlei`, `addtime`
) VALUES
(100, '国培网-融学Web', '1', '10010101', 1.50, @huoyuan_id, @huoyuan_id, '*', '大概1至2小时完成 格式：账号 密码', 1, @fenlei_id, NOW()),
(99, '国培网-融学App【秒学版】', '191', '10010102', 1.00, @huoyuan_id, @huoyuan_id, '*', '每培训10分钟内完成 格式：账号 密码', 1, @fenlei_id, NOW()),
(98, '广东省教师继续教育信息管理平台', '37', '10330101', 1.25, @huoyuan_id, @huoyuan_id, '*', '教师继续教育培训 格式：账号 密码', 1, @fenlei_id, NOW());

-- 5. 显示配置完成信息
SELECT 
  '货源配置完成' as message,
  hid as 货源ID,
  name as 货源名称,
  url as API地址,
  user as 账号
FROM qingka_wangke_huoyuan 
WHERE pt = 'jxjy';

SELECT 
  '分类配置完成' as message,
  id as 分类ID,
  name as 分类名称
FROM qingka_wangke_fenlei 
WHERE name = '易教育';

SELECT 
  '商品配置完成' as message,
  COUNT(*) as 商品数量
FROM qingka_wangke_class 
WHERE fenlei = @fenlei_id;
