-- 用户表结构（如果不存在则创建）
CREATE TABLE IF NOT EXISTS `qingka_wangke_user` (
  `uid` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `uuid` int(11) DEFAULT 1 COMMENT '上级用户ID',
  `user` varchar(50) NOT NULL COMMENT '用户名/账号',
  `pass` varchar(255) NOT NULL COMMENT '密码（支持哈希）',
  `name` varchar(100) NOT NULL COMMENT '显示名称',
  `money` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `addprice` decimal(4,2) DEFAULT 1.00 COMMENT '费率',
  `key` varchar(32) DEFAULT '0' COMMENT 'API密钥',
  `yqm` varchar(20) DEFAULT NULL COMMENT '邀请码',
  `yqprice` decimal(4,2) DEFAULT NULL COMMENT '邀请费率',
  `tuisongtoken` varchar(255) DEFAULT NULL COMMENT '推送token',
  `endtime` datetime DEFAULT NULL COMMENT '最后活动时间',
  `ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `user` (`user`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_uuid` (`uuid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 配置表结构（如果不存在则创建）
CREATE TABLE IF NOT EXISTS `qingka_wangke_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `v` varchar(100) NOT NULL COMMENT '配置键',
  `k` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `v` (`v`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认配置（如果不存在）
INSERT IGNORE INTO `qingka_wangke_config` (`v`, `k`, `description`) VALUES
('sitename', '猿猿国王v2', '网站名称'),
('logo', '/assets/img/logo.png', '网站Logo'),
('verification_code', '123456', '超级管理员二次验证码'),
('user_yqzc', '1', '是否开启邀请码注册'),
('settings', '1', 'API功能开关'),
('api_ck', '0.1', 'API查课费用'),
('api_xd', '1.0', 'API下单费用'),
('api_tongb', '10', 'API同步概率'),
('api_proportion', '80', 'API查课比例限制'),
('sykg', '0', '是否开启水印');

-- 日志表结构（如果不存在则创建）
CREATE TABLE IF NOT EXISTS `qingka_wangke_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '操作类型',
  `text` text COMMENT '操作描述',
  `money` decimal(10,2) DEFAULT 0.00 COMMENT '金额变动',
  `smoney` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_type` (`type`),
  KEY `idx_addtime` (`addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 创建默认超级管理员（如果不存在）
-- 注意：这里使用临时密码，部署后请立即修改
INSERT IGNORE INTO `qingka_wangke_user` (
  `uid`, `user`, `pass`, `name`, `money`, `addprice`, `addtime`, `uuid`, `status`
) VALUES (
  1, 
  'admin', 
  'temp123456',  -- 临时密码，请立即修改
  '超级管理员', 
  0.00, 
  1.00, 
  NOW(),
  1,
  1
);

-- 为超级管理员生成API密钥
UPDATE `qingka_wangke_user` SET `key` = MD5(CONCAT('admin_key_', UNIX_TIMESTAMP())) WHERE `uid` = 1 AND `key` = '0';
