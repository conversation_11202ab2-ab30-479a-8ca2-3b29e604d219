<?php
/**
 * 更新现有易教育订单的备注格式
 * 将"易教育 - 进度:"格式改为"进度:"格式
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/confing/common.php');

echo "=== 更新现有易教育订单备注格式 ===\n";

// 查找易教育货源
$jxjy_huoyuan = $DB->get_row("SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");

if (!$jxjy_huoyuan) {
    echo "❌ 未找到易教育货源配置\n";
    exit;
}

$hid = $jxjy_huoyuan['hid'];
echo "✅ 找到易教育货源，HID: {$hid}\n";

// 查找需要更新的订单
echo "\n查找需要更新的订单...\n";
$orders = $DB->query("
    SELECT oid, remarks 
    FROM qingka_wangke_order 
    WHERE hid = '{$hid}' 
    AND remarks LIKE '%易教育 - 进度:%'
    ORDER BY oid DESC
    LIMIT 100
");

$total_count = 0;
$updated_count = 0;

while ($order = $orders->fetch_assoc()) {
    $total_count++;
    $old_remarks = $order['remarks'];
    
    // 将"易教育 - 进度:"替换为"进度:"
    $new_remarks = str_replace('易教育 - 进度:', '进度:', $old_remarks);
    
    if ($new_remarks != $old_remarks) {
        // 转义SQL字符
        $new_remarks_escaped = $DB->escape($new_remarks);
        
        // 更新数据库
        $update_sql = "UPDATE qingka_wangke_order SET remarks = '{$new_remarks_escaped}' WHERE oid = '{$order['oid']}'";
        
        if ($DB->query($update_sql)) {
            $updated_count++;
            echo "✅ 更新订单 {$order['oid']}\n";
            echo "   原格式: " . substr($old_remarks, 0, 50) . "...\n";
            echo "   新格式: " . substr($new_remarks, 0, 50) . "...\n\n";
        } else {
            echo "❌ 更新失败: 订单 {$order['oid']}\n";
        }
    }
}

echo "=== 更新完成 ===\n";
echo "检查订单数: {$total_count}\n";
echo "更新订单数: {$updated_count}\n";

if ($updated_count > 0) {
    echo "✅ 成功更新 {$updated_count} 个订单的备注格式\n";
} else {
    echo "ℹ️ 没有需要更新的订单（可能已经是新格式）\n";
}

echo "\n现在所有新的易教育订单都将使用新格式：进度: xxxx - 更新: xxxx\n";
?>
