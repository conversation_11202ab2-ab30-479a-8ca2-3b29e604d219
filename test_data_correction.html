<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据校正功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-area { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        textarea { width: 100%; height: 150px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 智能数据校正功能测试</h1>
        
        <div class="test-area">
            <h3>测试数据输入</h3>
            <p>请在下面输入需要测试的数据，每行一条记录：</p>
            <textarea id="testInput" placeholder="测试数据示例：
440881197503162219	aA123456
自动识别 18888888888 aA1 23456
北京大学 student001 password，123
清华大学　admin　ｐａｓｓｗｏｒｄ１２３　高等数学
自动识别 <EMAIL> pass word 英语课程">440881197503162219	aA123456
自动识别 18888888888 aA1 23456
北京大学 student001 password，123
清华大学　admin　ｐａｓｓｗｏｒｄ１２３　高等数学
自动识别 <EMAIL> pass word 英语课程</textarea>
            
            <button onclick="testCorrection()">🔧 测试数据校正</button>
            <button onclick="clearAll()">🗑️ 清空</button>
        </div>
        
        <div class="test-area">
            <h3>校正结果</h3>
            <div id="result"></div>
        </div>
        
        <div class="test-area">
            <h3>功能说明</h3>
            <ul>
                <li><strong>多余空格处理：</strong>将多个空格、Tab键转换为单个空格</li>
                <li><strong>密码空格修复：</strong>自动合并被空格分割的密码</li>
                <li><strong>中文符号转换：</strong>将中文标点符号转换为英文符号</li>
                <li><strong>全角转半角：</strong>将全角字符转换为半角字符</li>
                <li><strong>格式验证：</strong>检查数据格式是否正确</li>
            </ul>
        </div>
    </div>

    <script>
        // 智能数据校正功能
        function smartCorrectUserinfo(text) {
            // 中文符号到英文符号的映射表
            var symbolMap = {
                '，': ',', '。': '.', '；': ';', '：': ':', '？': '?', '！': '!',
                '"': '"', '"': '"', ''': "'", ''': "'", '（': '(', '）': ')',
                '【': '[', '】': ']', '｛': '{', '｝': '}', '《': '<', '》': '>',
                '～': '~', '｀': '`', '＠': '@', '＃': '#', '￥': '$', '％': '%',
                '＾': '^', '＆': '&', '＊': '*', '－': '-', '＿': '_', '＋': '+',
                '＝': '=', '｜': '|', '＼': '\\', '／': '/', '　': ' '
            };
            
            // 全角数字和字母到半角的映射
            var fullWidthMap = {};
            // 全角数字 0-9
            for (var i = 0; i <= 9; i++) {
                fullWidthMap[String.fromCharCode(0xFF10 + i)] = i.toString();
            }
            // 全角大写字母 A-Z
            for (var i = 0; i < 26; i++) {
                fullWidthMap[String.fromCharCode(0xFF21 + i)] = String.fromCharCode(0x41 + i);
            }
            // 全角小写字母 a-z
            for (var i = 0; i < 26; i++) {
                fullWidthMap[String.fromCharCode(0xFF41 + i)] = String.fromCharCode(0x61 + i);
            }
            
            // 合并映射表
            var allMap = Object.assign({}, symbolMap, fullWidthMap);
            
            // 执行字符替换
            var corrected = text;
            for (var key in allMap) {
                corrected = corrected.replace(new RegExp(key, 'g'), allMap[key]);
            }
            
            return corrected;
        }
        
        function testCorrection() {
            var input = document.getElementById('testInput').value;
            var lines = input.split('\n');
            var errors = [];
            var warnings = [];
            var correctedLines = [];
            var hasCorrections = false;
            
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line === '') continue; // 跳过空行
                
                var originalLine = line;
                
                // 1. 智能符号校正
                line = smartCorrectUserinfo(line);
                
                // 2. 将Tab键转换为空格
                line = line.replace(/\t/g, ' ');
                
                // 3. 将多个空格替换为单个空格
                line = line.replace(/\s+/g, ' ');
                
                // 4. 智能密码校正 - 检测并修复密码中的空格
                var parts = line.split(' ');
                if (parts.length >= 2) {
                    var correctedParts = [];
                    var currentPart = '';
                    var isPassword = false;
                    
                    for (var j = 0; j < parts.length; j++) {
                        var part = parts[j];
                        
                        // 判断是否为学校名（通常包含中文或者是"自动识别"）
                        if (j === 0 && (/[\u4e00-\u9fa5]/.test(part) || part === '自动识别')) {
                            correctedParts.push(part);
                        }
                        // 判断是否为账号（通常是数字、邮箱或手机号）
                        else if ((j === 0 && !/[\u4e00-\u9fa5]/.test(part)) || 
                                (j === 1 && correctedParts.length === 1)) {
                            correctedParts.push(part);
                        }
                        // 其余部分可能是被分割的密码，需要合并
                        else {
                            if (!isPassword) {
                                currentPart = part;
                                isPassword = true;
                            } else {
                                currentPart += part; // 直接连接，不加空格
                            }
                        }
                    }
                    
                    // 如果有密码部分，添加到结果中
                    if (isPassword && currentPart) {
                        correctedParts.push(currentPart);
                    }
                    
                    // 重新组合
                    if (correctedParts.length >= 2) {
                        line = correctedParts.join(' ');
                    }
                }
                
                // 检查是否有修正
                if (originalLine !== line) {
                    hasCorrections = true;
                    warnings.push('第' + (i+1) + '行已自动校正：\n原始：' + originalLine + '\n校正：' + line);
                }
                
                correctedLines.push(line);
                
                // 验证最终格式
                var finalParts = line.split(' ');
                if (finalParts.length < 2 || finalParts.length > 4) {
                    errors.push('第' + (i+1) + '行数据数量不正确，此行有' + finalParts.length + '个数据。');
                }
                
                // 检查密码格式（基本验证）
                if (finalParts.length >= 2) {
                    var password = finalParts[finalParts.length - 1]; // 最后一个是密码
                    if (password.length < 3) {
                        warnings.push('第' + (i+1) + '行密码可能过短：' + password);
                    }
                    // 检查密码是否包含明显的空格（可能校正失败）
                    if (password.includes(' ')) {
                        errors.push('第' + (i+1) + '行密码仍包含空格，请手动检查：' + password);
                    }
                }
            }
            
            // 更新输入框
            document.getElementById('testInput').value = correctedLines.join('\n');
            
            // 显示结果
            var resultDiv = document.getElementById('result');
            var message = '';
            var className = '';
            
            if (hasCorrections) {
                message += '✅ 已自动校正 ' + warnings.length + ' 行数据\n\n';
                message += warnings.join('\n\n');
                message += '\n\n';
                className = 'success';
            }
            
            if (errors.length > 0) {
                message += '❌ 发现 ' + errors.length + ' 个错误：\n' + errors.join('\n');
                className = 'error';
            } else {
                if (hasCorrections) {
                    message += '✅ 所有数据格式正确！';
                } else {
                    message = '✅ 数据检查通过，无需校正！';
                    className = 'success';
                }
            }
            
            resultDiv.innerHTML = '<div class="result ' + className + '">' + message + '</div>';
        }
        
        function clearAll() {
            document.getElementById('testInput').value = '';
            document.getElementById('result').innerHTML = '';
        }
    </script>
</body>
</html>
